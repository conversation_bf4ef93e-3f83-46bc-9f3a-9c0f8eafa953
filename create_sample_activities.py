#!/usr/bin/env python
"""
Créer des activités d'exemple pour enrichir le Recent Activity dashboard
"""
import requests
import json
from datetime import datetime, timedelta

def create_sample_activities():
    print("🎾 Création d'Activités d'Exemple")
    print("="*35)
    
    # 1. Login player pour créer des réservations
    print("\n1️⃣ Login Player pour créer des activités:")
    try:
        login_response = requests.post('http://127.0.0.1:8000/auth/api/login/', json={
            'email': '<EMAIL>',
            'password': 'player123'
        })
        
        if login_response.status_code == 200:
            print("✅ Login player réussi")
            access_token = login_response.json().get('access')
            headers = {'Authorization': f'Bearer {access_token}'}
        else:
            print(f"❌ Login player échoué: {login_response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Erreur login player: {e}")
        return False
    
    # 2. <PERSON><PERSON><PERSON> quelques réservations de coach
    print("\n2️⃣ Création de réservations de coach:")
    try:
        # Get available coaches
        coaches_response = requests.get('http://127.0.0.1:8000/res/api/coaches/')
        coaches = coaches_response.json().get('coaches', [])
        
        if len(coaches) > 0:
            coach = coaches[0]  # Premier coach disponible
            
            # Créer 2-3 réservations de coach
            tomorrow = (datetime.now() + timedelta(days=1)).strftime('%Y-%m-%d')
            
            coach_reservations = [
                {
                    'coach_id': coach['id'],
                    'date': tomorrow,
                    'start_time': '10:00',
                    'end_time': '11:00'
                },
                {
                    'coach_id': coach['id'],
                    'date': tomorrow,
                    'start_time': '14:00',
                    'end_time': '15:00'
                }
            ]
            
            for i, reservation_data in enumerate(coach_reservations):
                try:
                    response = requests.post('http://127.0.0.1:8000/res/api/coach/book-slot/', 
                                           json=reservation_data, headers=headers)
                    
                    if response.status_code == 201:
                        print(f"   ✅ Réservation coach {i+1} créée: {reservation_data['start_time']}-{reservation_data['end_time']}")
                    else:
                        print(f"   ⚠️  Réservation coach {i+1} échouée: {response.status_code}")
                except Exception as e:
                    print(f"   ❌ Erreur réservation coach {i+1}: {e}")
        else:
            print("   ⚠️  Aucun coach disponible")
    except Exception as e:
        print(f"❌ Erreur création réservations coach: {e}")
    
    # 3. Créer quelques réservations de court
    print("\n3️⃣ Création de réservations de court:")
    try:
        # Get available courts
        courts_response = requests.get('http://127.0.0.1:8000/res/api/terrains/')
        courts = courts_response.json()
        
        if len(courts) > 0:
            court = courts[0]  # Premier court disponible
            
            # Créer 2 réservations de court
            tomorrow = (datetime.now() + timedelta(days=1)).strftime('%Y-%m-%d')
            
            court_reservations = [
                {
                    'terrain_id': court['id'],
                    'date': tomorrow,
                    'start_time': '09:00',
                    'end_time': '10:00'
                },
                {
                    'terrain_id': court['id'],
                    'date': tomorrow,
                    'start_time': '16:00',
                    'end_time': '17:00'
                }
            ]
            
            for i, reservation_data in enumerate(court_reservations):
                try:
                    response = requests.post('http://127.0.0.1:8000/res/api/court-reservations/', 
                                           json=reservation_data, headers=headers)
                    
                    if response.status_code == 201:
                        print(f"   ✅ Réservation court {i+1} créée: {reservation_data['start_time']}-{reservation_data['end_time']}")
                    else:
                        print(f"   ⚠️  Réservation court {i+1} échouée: {response.status_code}")
                        print(f"      Message: {response.text}")
                except Exception as e:
                    print(f"   ❌ Erreur réservation court {i+1}: {e}")
        else:
            print("   ⚠️  Aucun court disponible")
    except Exception as e:
        print(f"❌ Erreur création réservations court: {e}")
    
    # 4. Créer quelques commandes d'équipement
    print("\n4️⃣ Création de commandes d'équipement:")
    try:
        # Get available equipment
        equipment_response = requests.get('http://127.0.0.1:8000/res/api/equipment/')
        equipment_list = equipment_response.json()
        
        if len(equipment_list) > 0:
            equipment = equipment_list[0]  # Premier équipement disponible
            
            # Créer 1-2 commandes d'équipement
            equipment_orders = [
                {
                    'equipment_id': equipment['id'],
                    'quantity': 2
                },
                {
                    'equipment_id': equipment['id'],
                    'quantity': 1
                }
            ]
            
            for i, order_data in enumerate(equipment_orders):
                try:
                    response = requests.post('http://127.0.0.1:8000/res/api/equipment/order/', 
                                           json=order_data, headers=headers)
                    
                    if response.status_code == 201:
                        print(f"   ✅ Commande équipement {i+1} créée: {order_data['quantity']}x {equipment['name']}")
                    else:
                        print(f"   ⚠️  Commande équipement {i+1} échouée: {response.status_code}")
                        print(f"      Message: {response.text}")
                except Exception as e:
                    print(f"   ❌ Erreur commande équipement {i+1}: {e}")
        else:
            print("   ⚠️  Aucun équipement disponible")
    except Exception as e:
        print(f"❌ Erreur création commandes équipement: {e}")
    
    # 5. Créer quelques paiements
    print("\n5️⃣ Création de paiements:")
    try:
        # Créer 1-2 paiements
        payments = [
            {
                'amount': 50.00,
                'payment_method': 'credit_card',
                'description': 'Court reservation payment'
            },
            {
                'amount': 75.00,
                'payment_method': 'paypal',
                'description': 'Coach session payment'
            }
        ]
        
        for i, payment_data in enumerate(payments):
            try:
                response = requests.post('http://127.0.0.1:8000/res/api/payments/', 
                                       json=payment_data, headers=headers)
                
                if response.status_code == 201:
                    print(f"   ✅ Paiement {i+1} créé: ${payment_data['amount']} via {payment_data['payment_method']}")
                else:
                    print(f"   ⚠️  Paiement {i+1} échoué: {response.status_code}")
                    print(f"      Message: {response.text}")
            except Exception as e:
                print(f"   ❌ Erreur paiement {i+1}: {e}")
    except Exception as e:
        print(f"❌ Erreur création paiements: {e}")
    
    print("\n✅ Création d'activités d'exemple terminée!")
    print("🔄 Vous pouvez maintenant rafraîchir le dashboard admin")
    print("   pour voir les nouvelles activités dans Recent Activity")
    
    return True

if __name__ == '__main__':
    print("🎾 TENNIS MANAGEMENT SYSTEM")
    print("🔧 Création d'Activités d'Exemple pour Recent Activity")
    print("="*55)
    
    success = create_sample_activities()
    
    if success:
        print("\n🎉 ACTIVITÉS D'EXEMPLE CRÉÉES!")
        print("="*30)
        print("✅ Réservations de coach")
        print("✅ Réservations de court")
        print("✅ Commandes d'équipement")
        print("✅ Paiements")
        
        print("\n🎯 PROCHAINES ÉTAPES:")
        print("="*20)
        print("1. 🌐 Aller sur: http://127.0.0.1:8000/auth/login/")
        print("2. 🔑 Se connecter: <EMAIL> / admin123")
        print("3. 📊 Observer le Dashboard Admin")
        print("4. 🕒 Vérifier la section 'Recent Activity'")
        print("5. 🔄 Cliquer sur 'Refresh' pour voir les nouvelles activités")
        print("6. 🎨 Observer les différents types d'activités avec leurs icônes")
        
    else:
        print("\n⚠️  Certaines activités n'ont pas pu être créées.")
        print("Vérifiez les erreurs ci-dessus.")
