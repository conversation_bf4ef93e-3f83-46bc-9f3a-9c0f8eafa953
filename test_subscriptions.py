#!/usr/bin/env python
"""
Script to test subscription functionality
"""
import os
import sys
import django
from datetime import datetime, timedelta

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'tennis_manager.settings')
django.setup()

from django.contrib.auth import get_user_model
from reservations.models import Subscription, Payment, Notification
from django.utils import timezone

User = get_user_model()

def test_subscription_functionality():
    print("🧪 Testing Subscription Functionality...")
    
    # Test 1: Create test subscriptions
    print("\n1️⃣ Creating test subscriptions...")
    try:
        # Get test users
        player_alice = User.objects.filter(username='player_alice').first()
        player_bob = User.objects.filter(username='player_bob').first()
        
        if not player_alice or not player_bob:
            print("❌ Test users not found. Run create_test_data.py first.")
            return False
        
        # Create subscription for Alice (Premium)
        alice_subscription = Subscription.objects.create(
            user=player_alice,
            plan_type='premium',
            start_date=timezone.now(),
            end_date=timezone.now() + timedelta(days=30),
            monthly_price=49.99,
            status='active'
        )
        print(f"✅ Created premium subscription for {player_alice.username}")
        
        # Create subscription for Bob (Basic)
        bob_subscription = Subscription.objects.create(
            user=player_bob,
            plan_type='basic',
            start_date=timezone.now(),
            end_date=timezone.now() + timedelta(days=30),
            monthly_price=29.99,
            status='active'
        )
        print(f"✅ Created basic subscription for {player_bob.username}")
        
        # Create payment records
        Payment.objects.create(
            user=player_alice,
            payment_type='subscription',
            amount=49.99,
            status='completed',
            transaction_id=f'SUB_{alice_subscription.id}_{timezone.now().timestamp()}',
            description='Premium subscription for 1 month(s)'
        )
        
        Payment.objects.create(
            user=player_bob,
            payment_type='subscription',
            amount=29.99,
            status='completed',
            transaction_id=f'SUB_{bob_subscription.id}_{timezone.now().timestamp()}',
            description='Basic subscription for 1 month(s)'
        )
        
        # Create notifications
        Notification.objects.create(
            user=player_alice,
            title='Subscription Activated',
            message='Your Premium subscription has been activated successfully!',
            notification_type='subscription'
        )
        
        Notification.objects.create(
            user=player_bob,
            title='Subscription Activated',
            message='Your Basic subscription has been activated successfully!',
            notification_type='subscription'
        )
        
        print("✅ Created payment records and notifications")
        
    except Exception as e:
        print(f"❌ Error creating subscriptions: {e}")
        return False
    
    # Test 2: Test subscription properties
    print("\n2️⃣ Testing subscription properties...")
    try:
        alice_sub = Subscription.objects.filter(user=player_alice).first()
        bob_sub = Subscription.objects.filter(user=player_bob).first()
        
        print(f"✅ Alice subscription active: {alice_sub.is_active}")
        print(f"✅ Bob subscription active: {bob_sub.is_active}")
        print(f"✅ Alice plan: {alice_sub.plan_type} - ${alice_sub.monthly_price}")
        print(f"✅ Bob plan: {bob_sub.plan_type} - ${bob_sub.monthly_price}")
        
    except Exception as e:
        print(f"❌ Error testing subscription properties: {e}")
        return False
    
    # Test 3: Test subscription counts
    print("\n3️⃣ Testing subscription counts...")
    try:
        total_subscriptions = Subscription.objects.count()
        active_subscriptions = Subscription.objects.filter(status='active').count()
        
        print(f"✅ Total subscriptions: {total_subscriptions}")
        print(f"✅ Active subscriptions: {active_subscriptions}")
        
    except Exception as e:
        print(f"❌ Error testing subscription counts: {e}")
        return False
    
    print("\n🎉 All subscription tests passed!")
    print("\n📋 Summary:")
    print(f"   💳 Total Subscriptions: {Subscription.objects.count()}")
    print(f"   ✅ Active Subscriptions: {Subscription.objects.filter(status='active').count()}")
    print(f"   💰 Total Payments: {Payment.objects.filter(payment_type='subscription').count()}")
    print(f"   🔔 Subscription Notifications: {Notification.objects.filter(notification_type='subscription').count()}")
    
    print("\n🌐 Test the subscription functionality at:")
    print("   Player Dashboard: http://127.0.0.1:8000/auth/joueur-dashboard/")
    print("   Login as: player_alice / player123 (Premium subscriber)")
    print("   Login as: player_bob / player123 (Basic subscriber)")
    
    return True

if __name__ == '__main__':
    print("🎾 Tennis Management System - Subscription Test")
    print("="*60)
    
    success = test_subscription_functionality()
    
    if success:
        print("\n🚀 Subscription functionality is working correctly!")
        print("✅ You can now test subscriptions in the player dashboard.")
    else:
        print("\n⚠️  Some subscription tests failed. Check the errors above.")
