#!/usr/bin/env python
"""
Test complet de l'authentification JWT et création d'horaires
"""
import requests
import json
from datetime import datetime, timedelta

def test_complete_jwt_flow():
    print("🔍 TEST COMPLET AUTHENTIFICATION JWT + CRÉATION HORAIRES")
    print("="*65)
    
    base_url = "http://127.0.0.1:8000"
    
    # 1. Test de connexion avec l'admin créé précédemment
    print("\n1️⃣ Test de connexion admin...")
    
    login_data = {
        "email": "<EMAIL>",
        "password": "test123"
    }
    
    try:
        login_response = requests.post(f"{base_url}/auth/api/login/", json=login_data)
        print(f"   📥 Status connexion: {login_response.status_code}")
        
        if login_response.status_code == 200:
            token_data = login_response.json()
            access_token = token_data.get('access')
            refresh_token = token_data.get('refresh')
            
            print(f"   ✅ Connexion réussie!")
            print(f"   🔑 Access token: {access_token[:50]}...")
            print(f"   🔄 Refresh token: {refresh_token[:50] if refresh_token else 'None'}...")
            
            # 2. Test de récupération des coachs avec le token
            print("\n2️⃣ Test récupération coachs avec token...")
            headers = {
                'Authorization': f'Bearer {access_token}',
                'Content-Type': 'application/json'
            }
            
            coaches_response = requests.get(f"{base_url}/res/api/coaches/", headers=headers)
            print(f"   📥 Status coachs: {coaches_response.status_code}")
            
            if coaches_response.status_code == 200:
                coaches_data = coaches_response.json()
                coaches = coaches_data.get('coaches', [])
                print(f"   ✅ {len(coaches)} coachs récupérés avec succès")
                
                if coaches:
                    # Prendre le coach de test créé
                    test_coach = None
                    for coach in coaches:
                        if coach['name'] == 'Test Coach API':
                            test_coach = coach
                            break
                    
                    if not test_coach:
                        test_coach = coaches[0]  # Prendre le premier
                    
                    print(f"   👨‍🏫 Coach sélectionné: {test_coach['name']} (ID: {test_coach['id']})")
                    
                    # 3. Test de création d'horaire
                    print("\n3️⃣ Test création d'horaire avec token valide...")
                    test_schedule_creation_with_token(base_url, headers, test_coach['id'])
                    
                else:
                    print("   ❌ Aucun coach trouvé")
            else:
                print(f"   ❌ Erreur récupération coachs: {coaches_response.text}")
                
        else:
            print(f"   ❌ Échec de connexion: {login_response.text}")
            
    except Exception as e:
        print(f"   ❌ Erreur: {e}")

def test_schedule_creation_with_token(base_url, headers, coach_id):
    """Test de création d'horaire avec token JWT valide"""
    
    tomorrow = (datetime.now() + timedelta(days=1)).strftime('%Y-%m-%d')
    
    # Essayer plusieurs créneaux horaires
    test_slots = [
        {'start_time': '08:00', 'end_time': '09:00'},
        {'start_time': '12:00', 'end_time': '13:00'},
        {'start_time': '17:00', 'end_time': '18:00'},
        {'start_time': '19:00', 'end_time': '20:00'},
        {'start_time': '20:00', 'end_time': '21:00'}
    ]
    
    for i, slot in enumerate(test_slots):
        print(f"\n   🔄 Tentative {i+1}: {slot['start_time']}-{slot['end_time']}")
        
        schedule_data = {
            'coach_id': coach_id,
            'date': tomorrow,
            'start_time': slot['start_time'],
            'end_time': slot['end_time']
        }
        
        try:
            response = requests.post(
                f"{base_url}/res/api/coach-schedule/create/",
                json=schedule_data,
                headers=headers
            )
            
            print(f"   📥 Status: {response.status_code}")
            
            if response.status_code == 201:
                print("   ✅ SUCCÈS! Horaire créé avec succès!")
                response_data = response.json()
                print(f"   📋 Réponse: {response_data}")
                
                # 4. Vérifier que l'horaire a été créé
                print("\n4️⃣ Vérification de l'horaire créé...")
                verify_schedule_creation(base_url, headers, coach_id, tomorrow, slot)
                return True
                
            elif response.status_code == 400:
                error_data = response.json()
                error_msg = error_data.get('error', 'Unknown error')
                print(f"   ⚠️  Erreur 400: {error_msg}")
                
                if 'already exists' in error_msg.lower():
                    print("   🔄 Créneau déjà existant, essayons le suivant...")
                    continue
                else:
                    print(f"   ❌ Erreur de validation: {error_msg}")
                    break
                    
            elif response.status_code == 401:
                print("   ❌ ERREUR 401: Token non valide ou expiré!")
                print(f"   🔑 Token utilisé: {headers['Authorization'][:70]}...")
                break
                
            elif response.status_code == 403:
                print("   ❌ ERREUR 403: Permissions insuffisantes!")
                break
                
            else:
                print(f"   ❌ Erreur inattendue {response.status_code}: {response.text}")
                break
                
        except Exception as e:
            print(f"   ❌ Exception: {e}")
            break
    
    print("   ❌ Aucun créneau n'a pu être créé")
    return False

def verify_schedule_creation(base_url, headers, coach_id, date, slot):
    """Vérifier que l'horaire a été créé en récupérant les horaires du coach"""
    
    try:
        # Utiliser l'API admin pour récupérer les horaires
        response = requests.get(
            f"{base_url}/res/api/coaches/{coach_id}/schedule/",
            headers=headers
        )
        
        print(f"   📥 Status vérification: {response.status_code}")
        
        if response.status_code == 200:
            schedule_data = response.json()
            slots = schedule_data.get('schedule_slots', [])
            
            # Chercher le créneau créé
            found_slot = None
            for s in slots:
                if (s['date'] == date and 
                    s['start_time'] == slot['start_time'] and 
                    s['end_time'] == slot['end_time']):
                    found_slot = s
                    break
            
            if found_slot:
                print(f"   ✅ Horaire trouvé en base!")
                print(f"   📅 {found_slot['date']} {found_slot['start_time']}-{found_slot['end_time']}")
                print(f"   🆔 ID: {found_slot['id']}")
                print(f"   🔒 Réservé: {found_slot['is_booked']}")
                return True
            else:
                print(f"   ❌ Horaire non trouvé en base")
                print(f"   📊 Total créneaux pour ce coach: {len(slots)}")
                return False
        else:
            print(f"   ❌ Erreur récupération: {response.text}")
            return False
            
    except Exception as e:
        print(f"   ❌ Erreur vérification: {e}")
        return False

def test_browser_simulation():
    """Simuler le comportement du navigateur"""
    print("\n5️⃣ Simulation du comportement navigateur...")
    
    base_url = "http://127.0.0.1:8000"
    
    # Créer une session pour maintenir les cookies
    session = requests.Session()
    
    # 1. Récupérer la page de connexion pour obtenir le CSRF token
    print("   🌐 Récupération de la page de connexion...")
    login_page = session.get(f"{base_url}/auth/login/")
    print(f"   📥 Status page login: {login_page.status_code}")
    
    # 2. Connexion avec session
    login_data = {
        "email": "<EMAIL>",
        "password": "test123"
    }
    
    # Ajouter les headers comme un navigateur
    headers = {
        'Content-Type': 'application/json',
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
        'Accept': 'application/json, text/plain, */*',
        'Accept-Language': 'en-US,en;q=0.9',
        'Accept-Encoding': 'gzip, deflate',
        'Connection': 'keep-alive'
    }
    
    login_response = session.post(f"{base_url}/auth/api/login/", json=login_data, headers=headers)
    print(f"   📥 Status connexion session: {login_response.status_code}")
    
    if login_response.status_code == 200:
        token_data = login_response.json()
        access_token = token_data.get('access')
        
        print(f"   ✅ Token obtenu via session: {access_token[:50]}...")
        
        # 3. Test création avec session + token
        auth_headers = headers.copy()
        auth_headers['Authorization'] = f'Bearer {access_token}'
        
        tomorrow = (datetime.now() + timedelta(days=1)).strftime('%Y-%m-%d')
        schedule_data = {
            'coach_id': 10,  # ID du coach de test
            'date': tomorrow,
            'start_time': '23:00',
            'end_time': '23:59'
        }
        
        create_response = session.post(
            f"{base_url}/res/api/coach-schedule/create/",
            json=schedule_data,
            headers=auth_headers
        )
        
        print(f"   📥 Status création avec session: {create_response.status_code}")
        print(f"   📥 Response: {create_response.text}")
        
        if create_response.status_code == 201:
            print("   ✅ SUCCÈS avec simulation navigateur!")
            return True
    
    return False

if __name__ == '__main__':
    print("🎾 TENNIS MANAGEMENT SYSTEM")
    print("🔧 Test Complet JWT + Création Horaires")
    print("="*55)
    
    # Test principal
    success1 = test_complete_jwt_flow()
    
    # Test simulation navigateur
    success2 = test_browser_simulation()
    
    if success1 or success2:
        print("\n🎉 AU MOINS UN TEST RÉUSSI!")
        print("="*30)
        print("✅ L'authentification JWT fonctionne")
        print("✅ L'API de création d'horaires fonctionne")
        print("✅ Le problème est probablement côté interface web")
        
        print("\n🎯 SOLUTION:")
        print("="*15)
        print("1. 🔐 Connectez-vous à l'interface web avec:")
        print("   Email: <EMAIL>")
        print("   Password: test123")
        print("2. 🌐 Ouvrez les outils de développement (F12)")
        print("3. 🌐 Vérifiez l'onglet Network lors de la création d'horaire")
        print("4. 🌐 Vérifiez que le token JWT est bien envoyé")
        print("5. 🌐 Vérifiez la réponse du serveur")
        
    else:
        print("\n❌ TOUS LES TESTS ONT ÉCHOUÉ")
        print("="*30)
        print("Le problème est au niveau de l'authentification JWT")
        print("Vérifiez la configuration JWT dans settings.py")
