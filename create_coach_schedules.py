#!/usr/bin/env python
"""
Créer des horaires pour les coachs pour avoir des créneaux disponibles
"""
import os
import sys
import django
from datetime import datetime, timedelta, time

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'tennis_manager.settings')
django.setup()

from django.contrib.auth import get_user_model
from reservations.models import Coach, ScheduleSlot

User = get_user_model()

def create_coach_schedules():
    print("🎾 Création d'Horaires pour les Coachs")
    print("="*40)
    
    try:
        # Get all coaches
        coaches = Coach.objects.all()
        
        if not coaches:
            print("❌ Aucun coach trouvé")
            return False
        
        print(f"📋 {len(coaches)} coachs trouvés")
        
        # Create schedules for the next 7 days
        today = datetime.now().date()
        
        for coach in coaches:
            print(f"\n👨‍🏫 Création d'horaires pour {coach.name}:")
            
            # Create schedule for next 7 days
            for day_offset in range(1, 8):  # Tomorrow to next week
                schedule_date = today + timedelta(days=day_offset)
                
                # Skip weekends for some variety
                if schedule_date.weekday() >= 5:  # Saturday = 5, Sunday = 6
                    continue
                
                # Create morning slots (9:00-12:00)
                morning_slots = [
                    (time(9, 0), time(10, 0)),
                    (time(10, 0), time(11, 0)),
                    (time(11, 0), time(12, 0)),
                ]
                
                # Create afternoon slots (14:00-17:00)
                afternoon_slots = [
                    (time(14, 0), time(15, 0)),
                    (time(15, 0), time(16, 0)),
                    (time(16, 0), time(17, 0)),
                ]
                
                # Combine all slots
                all_slots = morning_slots + afternoon_slots
                
                # Create schedule slots for this coach and date
                created_count = 0
                for start_time, end_time in all_slots:
                    # Check if slot already exists
                    existing = ScheduleSlot.objects.filter(
                        coach=coach,
                        date=schedule_date,
                        start_time=start_time,
                        end_time=end_time
                    ).exists()
                    
                    if not existing:
                        ScheduleSlot.objects.create(
                            coach=coach,
                            date=schedule_date,
                            start_time=start_time,
                            end_time=end_time,
                            is_booked=False
                        )
                        created_count += 1
                
                if created_count > 0:
                    print(f"   📅 {schedule_date}: {created_count} créneaux créés")
        
        print("\n✅ Horaires créés avec succès!")
        
        # Show summary
        total_slots = ScheduleSlot.objects.count()
        available_slots = ScheduleSlot.objects.filter(is_booked=False).count()
        
        print(f"\n📊 Résumé:")
        print(f"   Total créneaux: {total_slots}")
        print(f"   Créneaux disponibles: {available_slots}")
        print(f"   Créneaux réservés: {total_slots - available_slots}")
        
        return True
        
    except Exception as e:
        print(f"❌ Erreur: {e}")
        return False

def show_coach_schedules():
    print("\n📋 Horaires des Coachs:")
    print("="*25)
    
    coaches = Coach.objects.all()
    
    for coach in coaches:
        slots = ScheduleSlot.objects.filter(coach=coach).order_by('date', 'start_time')
        available_count = slots.filter(is_booked=False).count()
        
        print(f"\n👨‍🏫 {coach.name} (${coach.price_per_hour}/heure):")
        print(f"   📊 {available_count} créneaux disponibles")
        
        # Show next few available slots
        next_slots = slots.filter(is_booked=False)[:5]
        for slot in next_slots:
            print(f"   📅 {slot.date} {slot.start_time}-{slot.end_time}")

if __name__ == '__main__':
    success = create_coach_schedules()
    
    if success:
        show_coach_schedules()
        
        print("\n🎯 PRÊT POUR LE TEST!")
        print("="*25)
        print("1. Aller sur: http://127.0.0.1:8000/auth/login/")
        print("2. Se connecter: <EMAIL> / player123")
        print("3. Aller dans une réservation de coach existante")
        print("4. Cliquer sur Update (✏️)")
        print("5. Cliquer sur 'Load Availability'")
        print("6. Voir tous les créneaux disponibles de tous les coachs!")
        
        print("\n✨ CRÉNEAUX DISPONIBLES:")
        print("="*25)
        print("🕘 Matin: 9h-10h, 10h-11h, 11h-12h")
        print("🕐 Après-midi: 14h-15h, 15h-16h, 16h-17h")
        print("📅 Dates: 7 prochains jours (lundi-vendredi)")
        print("👥 Tous les coachs ont des créneaux")
    else:
        print("\n❌ Échec de la création des horaires")
