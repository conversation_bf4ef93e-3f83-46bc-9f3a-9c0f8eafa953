{% load static %}
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MVP Voting - Elite Sports Club</title>
    
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.0/font/bootstrap-icons.css" rel="stylesheet">
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <style>
        :root {
            --primary-color: #6366f1;
            --secondary-color: #10b981;
            --accent-color: #f59e0b;
            --danger-color: #ef4444;
            --gray-50: #f9fafb;
            --gray-100: #f3f4f6;
            --gray-200: #e5e7eb;
            --gray-600: #4b5563;
            --gray-700: #374151;
            --gray-800: #1f2937;
            --gold: #fbbf24;
        }

        body {
            font-family: 'Inter', sans-serif;
            background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
            color: var(--gray-700);
            padding-top: 80px;
        }

        .voting-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 2rem;
        }

        .page-header {
            background: white;
            border-radius: 16px;
            padding: 2rem;
            margin-bottom: 2rem;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
            border: 1px solid var(--gray-100);
            text-align: center;
        }

        .page-title {
            font-family: 'Poppins', sans-serif;
            font-size: 2.5rem;
            font-weight: 700;
            color: var(--gray-800);
            margin: 0;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 1rem;
        }

        .title-icon {
            width: 56px;
            height: 56px;
            background: linear-gradient(135deg, var(--gold) 0%, var(--accent-color) 100%);
            border-radius: 16px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.5rem;
        }

        .voting-period {
            background: var(--primary-color);
            color: white;
            padding: 1rem 2rem;
            border-radius: 12px;
            margin: 1rem 0;
            display: inline-block;
        }

        .period-text {
            font-weight: 600;
            margin: 0;
        }

        .countdown {
            font-size: 0.875rem;
            opacity: 0.9;
            margin-top: 0.25rem;
        }

        .current-mvp {
            background: white;
            border-radius: 16px;
            padding: 2rem;
            margin-bottom: 2rem;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
            border: 1px solid var(--gray-100);
            text-align: center;
        }

        .mvp-crown {
            width: 80px;
            height: 80px;
            background: linear-gradient(135deg, var(--gold), var(--accent-color));
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 1rem;
            font-size: 2rem;
            color: white;
            position: relative;
        }

        .mvp-crown::before {
            content: '👑';
            position: absolute;
            top: -10px;
            font-size: 1.5rem;
        }

        .mvp-name {
            font-size: 1.5rem;
            font-weight: 700;
            color: var(--gray-800);
            margin-bottom: 0.5rem;
        }

        .mvp-title {
            color: var(--gold);
            font-weight: 600;
            margin-bottom: 1rem;
        }

        .mvp-stats {
            display: flex;
            justify-content: center;
            gap: 2rem;
            margin-top: 1rem;
        }

        .mvp-stat {
            text-align: center;
        }

        .stat-value {
            font-size: 1.25rem;
            font-weight: 700;
            color: var(--primary-color);
        }

        .stat-label {
            font-size: 0.875rem;
            color: var(--gray-600);
        }

        .nominees-section {
            background: white;
            border-radius: 16px;
            padding: 2rem;
            margin-bottom: 2rem;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
            border: 1px solid var(--gray-100);
        }

        .section-title {
            font-size: 1.5rem;
            font-weight: 600;
            color: var(--gray-800);
            margin-bottom: 1.5rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .nominees-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 1.5rem;
        }

        .nominee-card {
            background: var(--gray-50);
            border-radius: 12px;
            padding: 1.5rem;
            border: 2px solid var(--gray-100);
            transition: all 0.3s ease;
            position: relative;
        }

        .nominee-card:hover {
            transform: translateY(-4px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
        }

        .nominee-card.voted {
            border-color: var(--primary-color);
            background: rgba(99, 102, 241, 0.05);
        }

        .nominee-header {
            display: flex;
            align-items: center;
            gap: 1rem;
            margin-bottom: 1rem;
        }

        .nominee-avatar {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.5rem;
            font-weight: 700;
        }

        .nominee-info h3 {
            margin: 0;
            font-weight: 600;
            color: var(--gray-800);
            font-size: 1.1rem;
        }

        .nominee-sport {
            color: var(--gray-600);
            font-size: 0.875rem;
        }

        .nominee-achievements {
            margin-bottom: 1rem;
        }

        .achievement-item {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            margin-bottom: 0.5rem;
            font-size: 0.875rem;
            color: var(--gray-700);
        }

        .achievement-icon {
            color: var(--accent-color);
        }

        .vote-section {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .vote-count {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            color: var(--gray-600);
            font-size: 0.875rem;
        }

        .vote-btn {
            background: var(--primary-color);
            color: white;
            border: none;
            padding: 0.5rem 1rem;
            border-radius: 8px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s ease;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .vote-btn:hover {
            background: #5856eb;
            transform: scale(1.05);
        }

        .vote-btn:disabled {
            background: var(--gray-400);
            cursor: not-allowed;
            transform: none;
        }

        .vote-btn.voted {
            background: var(--secondary-color);
        }

        .voting-rules {
            background: white;
            border-radius: 16px;
            padding: 2rem;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
            border: 1px solid var(--gray-100);
        }

        .rules-list {
            list-style: none;
            padding: 0;
        }

        .rules-list li {
            display: flex;
            align-items: flex-start;
            gap: 0.75rem;
            margin-bottom: 1rem;
            padding: 0.75rem;
            background: var(--gray-50);
            border-radius: 8px;
        }

        .rule-number {
            background: var(--primary-color);
            color: white;
            width: 24px;
            height: 24px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 0.875rem;
            font-weight: 600;
            flex-shrink: 0;
        }

        .vote-progress {
            margin-top: 1rem;
        }

        .progress-bar {
            width: 100%;
            height: 8px;
            background: var(--gray-200);
            border-radius: 4px;
            overflow: hidden;
            margin-top: 0.5rem;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
            border-radius: 4px;
            transition: width 0.3s ease;
        }

        .progress-text {
            font-size: 0.875rem;
            color: var(--gray-600);
            margin-top: 0.25rem;
        }

        /* Responsive */
        @media (max-width: 768px) {
            .voting-container {
                padding: 1rem;
            }
            
            .page-title {
                font-size: 2rem;
                flex-direction: column;
            }
            
            .nominees-grid {
                grid-template-columns: 1fr;
            }
            
            .mvp-stats {
                flex-direction: column;
                gap: 1rem;
            }
        }

        /* Animation */
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .animate-in {
            animation: fadeInUp 0.6s ease-out forwards;
        }

        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); }
            100% { transform: scale(1); }
        }

        .mvp-crown {
            animation: pulse 2s infinite;
        }
    </style>
</head>
<body>
    <!-- Include Navbar -->
    {% include 'html/navbar.html' %}

    <div class="voting-container">
        <!-- Page Header -->
        <div class="page-header animate-in">
            <h1 class="page-title">
                <div class="title-icon">
                    <i class="bi bi-star"></i>
                </div>
                MVP Voting
            </h1>
            <p class="text-muted mt-2">Vote for this week's Most Valuable Player and celebrate outstanding performances</p>
            
            <div class="voting-period">
                <div class="period-text">Week of June 10-16, 2024</div>
                <div class="countdown">Voting ends in 2 days, 14 hours</div>
            </div>
        </div>

        <!-- Current MVP -->
        <div class="current-mvp animate-in" style="animation-delay: 0.1s;">
            <h2 class="section-title">
                <i class="bi bi-trophy"></i>
                Last Week's MVP
            </h2>
            <div class="mvp-crown">AJ</div>
            <h3 class="mvp-name">Alex Johnson</h3>
            <p class="mvp-title">Tennis Champion</p>
            <p class="text-muted">Outstanding performance with 3 consecutive wins and exceptional sportsmanship</p>
            
            <div class="mvp-stats">
                <div class="mvp-stat">
                    <div class="stat-value">156</div>
                    <div class="stat-label">Total Votes</div>
                </div>
                <div class="mvp-stat">
                    <div class="stat-value">67%</div>
                    <div class="stat-label">Vote Share</div>
                </div>
                <div class="mvp-stat">
                    <div class="stat-value">3</div>
                    <div class="stat-label">Wins</div>
                </div>
            </div>
        </div>

        <!-- This Week's Nominees -->
        <div class="nominees-section animate-in" style="animation-delay: 0.2s;">
            <h2 class="section-title">
                <i class="bi bi-people"></i>
                This Week's Nominees
            </h2>
            
            <div class="nominees-grid" id="nomineesGrid">
                <!-- Nominees will be populated here -->
            </div>
        </div>

        <!-- Voting Rules -->
        <div class="voting-rules animate-in" style="animation-delay: 0.3s;">
            <h2 class="section-title">
                <i class="bi bi-info-circle"></i>
                Voting Rules
            </h2>
            
            <ul class="rules-list">
                <li>
                    <div class="rule-number">1</div>
                    <div>Each member can vote once per week for their choice of MVP</div>
                </li>
                <li>
                    <div class="rule-number">2</div>
                    <div>Voting is based on performance, sportsmanship, and improvement shown during the week</div>
                </li>
                <li>
                    <div class="rule-number">3</div>
                    <div>Nominees are selected by coaches based on outstanding achievements</div>
                </li>
                <li>
                    <div class="rule-number">4</div>
                    <div>Voting closes every Sunday at midnight, results announced Monday</div>
                </li>
                <li>
                    <div class="rule-number">5</div>
                    <div>MVP winners receive special recognition and exclusive club merchandise</div>
                </li>
            </ul>
        </div>
    </div>

    <!-- JavaScript -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Sample nominees data
        const nominees = [
            {
                id: 1,
                name: "Sarah Williams",
                sport: "Swimming",
                avatar: "SW",
                achievements: [
                    "Set new club record in 100m freestyle",
                    "Helped teammates improve technique",
                    "Perfect attendance this week"
                ],
                votes: 89,
                totalVotes: 234
            },
            {
                id: 2,
                name: "Mike Chen",
                sport: "Basketball",
                avatar: "MC",
                achievements: [
                    "Led team to victory in 3 games",
                    "Averaged 18 points per game",
                    "Excellent team leadership"
                ],
                votes: 76,
                totalVotes: 234
            },
            {
                id: 3,
                name: "Emma Davis",
                sport: "Football",
                avatar: "ED",
                achievements: [
                    "Scored 5 goals this week",
                    "Assisted teammates in training",
                    "Showed great sportsmanship"
                ],
                votes: 69,
                totalVotes: 234
            },
            {
                id: 4,
                name: "James Wilson",
                sport: "Tennis",
                avatar: "JW",
                achievements: [
                    "Won junior tournament",
                    "Improved serve accuracy by 30%",
                    "Mentored new club members"
                ],
                votes: 45,
                totalVotes: 234
            },
            {
                id: 5,
                name: "Lisa Rodriguez",
                sport: "Swimming",
                avatar: "LR",
                achievements: [
                    "Completed first triathlon",
                    "Consistent training dedication",
                    "Positive attitude and motivation"
                ],
                votes: 38,
                totalVotes: 234
            },
            {
                id: 6,
                name: "David Kim",
                sport: "Basketball",
                avatar: "DK",
                achievements: [
                    "Improved free throw percentage",
                    "Great defensive plays",
                    "Team player of the week"
                ],
                votes: 32,
                totalVotes: 234
            }
        ];

        let userVote = null;

        function renderNominees() {
            const grid = document.getElementById('nomineesGrid');
            grid.innerHTML = '';

            nominees.forEach((nominee, index) => {
                const nomineeCard = createNomineeCard(nominee, index);
                grid.appendChild(nomineeCard);
            });
        }

        function createNomineeCard(nominee, index) {
            const card = document.createElement('div');
            card.className = `nominee-card animate-in ${userVote === nominee.id ? 'voted' : ''}`;
            card.style.animationDelay = `${(index * 0.1) + 0.4}s`;
            
            const votePercentage = Math.round((nominee.votes / nominee.totalVotes) * 100);
            
            card.innerHTML = `
                <div class="nominee-header">
                    <div class="nominee-avatar">${nominee.avatar}</div>
                    <div class="nominee-info">
                        <h3>${nominee.name}</h3>
                        <div class="nominee-sport">${nominee.sport}</div>
                    </div>
                </div>
                
                <div class="nominee-achievements">
                    ${nominee.achievements.map(achievement => `
                        <div class="achievement-item">
                            <i class="bi bi-star-fill achievement-icon"></i>
                            <span>${achievement}</span>
                        </div>
                    `).join('')}
                </div>
                
                <div class="vote-progress">
                    <div class="progress-bar">
                        <div class="progress-fill" style="width: ${votePercentage}%;"></div>
                    </div>
                    <div class="progress-text">${nominee.votes} votes (${votePercentage}%)</div>
                </div>
                
                <div class="vote-section">
                    <div class="vote-count">
                        <i class="bi bi-people"></i>
                        <span>${nominee.votes} votes</span>
                    </div>
                    <button class="vote-btn ${userVote === nominee.id ? 'voted' : ''}" 
                            onclick="vote(${nominee.id})" 
                            ${userVote && userVote !== nominee.id ? 'disabled' : ''}>
                        <i class="bi bi-${userVote === nominee.id ? 'check' : 'star'}"></i>
                        ${userVote === nominee.id ? 'Voted' : 'Vote'}
                    </button>
                </div>
            `;
            
            return card;
        }

        function vote(nomineeId) {
            if (userVote) return;
            
            userVote = nomineeId;
            
            // Update vote count
            const nominee = nominees.find(n => n.id === nomineeId);
            nominee.votes += 1;
            nominee.totalVotes += 1;
            
            // Update all nominees' total votes
            nominees.forEach(n => {
                if (n.id !== nomineeId) {
                    n.totalVotes += 1;
                }
            });
            
            // Re-render nominees
            renderNominees();
            
            // Show success message
            showVoteSuccess(nominee.name);
        }

        function showVoteSuccess(nomineeName) {
            // Create and show a success message
            const message = document.createElement('div');
            message.style.cssText = `
                position: fixed;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
                background: var(--secondary-color);
                color: white;
                padding: 1rem 2rem;
                border-radius: 12px;
                font-weight: 600;
                z-index: 1000;
                box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
            `;
            message.innerHTML = `
                <i class="bi bi-check-circle"></i>
                Vote cast for ${nomineeName}!
            `;
            
            document.body.appendChild(message);
            
            setTimeout(() => {
                message.remove();
            }, 3000);
        }

        // Countdown timer
        function updateCountdown() {
            const endDate = new Date();
            endDate.setDate(endDate.getDate() + 2);
            endDate.setHours(endDate.getHours() + 14);
            
            const now = new Date();
            const diff = endDate - now;
            
            const days = Math.floor(diff / (1000 * 60 * 60 * 24));
            const hours = Math.floor((diff % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
            const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60));
            
            const countdownElement = document.querySelector('.countdown');
            if (countdownElement) {
                countdownElement.textContent = `Voting ends in ${days} days, ${hours} hours, ${minutes} minutes`;
            }
        }

        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            renderNominees();
            updateCountdown();
            
            // Update countdown every minute
            setInterval(updateCountdown, 60000);
            
            // Add hover effects to nominee cards
            document.addEventListener('mouseover', function(e) {
                if (e.target.closest('.nominee-card')) {
                    const card = e.target.closest('.nominee-card');
                    if (!card.classList.contains('voted')) {
                        card.style.borderColor = 'var(--primary-color)';
                    }
                }
            });
            
            document.addEventListener('mouseout', function(e) {
                if (e.target.closest('.nominee-card')) {
                    const card = e.target.closest('.nominee-card');
                    if (!card.classList.contains('voted')) {
                        card.style.borderColor = 'var(--gray-100)';
                    }
                }
            });
        });
    </script>
</body>
</html>
