#!/usr/bin/env python
"""
Test de la nouvelle page d'accueil avec système de réservation intégré
"""
import requests
import j<PERSON>

def test_home_page_reservation_system():
    print("🎾 Test Page d'Accueil - Système de Réservation Intégré")
    print("="*60)
    
    # 1. Test accès à la page d'accueil
    print("\n1️⃣ Test Accès Page d'Accueil:")
    try:
        home_response = requests.get('http://127.0.0.1:8000/')
        
        if home_response.status_code == 200:
            html_content = home_response.text
            print("✅ Page d'accueil accessible")
            
            # Vérifier les éléments de la nouvelle page
            checks = [
                ("Hero Section", "Tennis Club Elite" in html_content),
                ("Reservation Section", "Quick Court Reservation" in html_content),
                ("Features Section", "Why Choose Tennis Club Elite" in html_content),
                ("Authentication Notice", "auth-notice" in html_content),
                ("Reservation Form", "quickReservationForm" in html_content),
                ("Court Selection", 'id="terrain"' in html_content),
                ("Date Input", 'id="date"' in html_content),
                ("Time Inputs", 'id="start_time"' in html_content and 'id="end_time"' in html_content),
                ("JavaScript Functions", "checkAuthenticationStatus" in html_content),
                ("Scroll Function", "scrollToReservation" in html_content),
                ("Validation Function", "validateReservationForm" in html_content),
                ("Submit Function", "submitReservation" in html_content),
                ("Notification System", "showNotification" in html_content),
                ("Modern Styling", "btn-tennis" in html_content),
                ("Responsive Design", "col-lg-" in html_content)
            ]
            
            all_passed = True
            for check_name, check_result in checks:
                if check_result:
                    print(f"   ✅ {check_name}")
                else:
                    print(f"   ❌ {check_name} manquant")
                    all_passed = False
            
            if all_passed:
                print("✅ Nouvelle page d'accueil complète!")
            else:
                print("⚠️  Certains éléments manquent")
                
        else:
            print(f"❌ Page d'accueil inaccessible: {home_response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Erreur accès page d'accueil: {e}")
        return False
    
    # 2. Test API terrains (pour le formulaire de réservation)
    print("\n2️⃣ Test API Terrains (pour formulaire):")
    try:
        # Test avec authentification
        login_response = requests.post('http://127.0.0.1:8000/auth/api/login/', json={
            'email': '<EMAIL>',
            'password': 'player123'
        })
        
        if login_response.status_code == 200:
            access_token = login_response.json().get('access')
            headers = {'Authorization': f'Bearer {access_token}'}
            
            terrains_response = requests.get('http://127.0.0.1:8000/res/api/terrains/', headers=headers)
            
            if terrains_response.status_code == 200:
                terrains_data = terrains_response.json()
                print(f"✅ API Terrains fonctionnelle")
                print(f"   🏟️ {len(terrains_data)} terrains disponibles")
                
                available_terrains = [t for t in terrains_data if t.get('available', False)]
                print(f"   ✅ {len(available_terrains)} terrains réservables")
                
                if len(available_terrains) > 0:
                    example_terrain = available_terrains[0]
                    print(f"   🔍 Exemple: {example_terrain['name']} - {example_terrain['location']} (${example_terrain['price_per_hour']}/hr)")
            else:
                print(f"❌ Erreur API Terrains: {terrains_response.status_code}")
                return False
        else:
            print(f"❌ Login échoué pour test API: {login_response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Erreur test API Terrains: {e}")
        return False
    
    # 3. Test API de réservation (simulation)
    print("\n3️⃣ Test API Réservation:")
    try:
        # Test avec données valides
        reservation_data = {
            'terrain_id': 1,
            'date': '2025-06-15',
            'start_time': '14:00',
            'end_time': '15:00'
        }
        
        reservation_response = requests.post(
            'http://127.0.0.1:8000/res/api/court-reservations/',
            headers=headers,
            json=reservation_data
        )
        
        if reservation_response.status_code in [200, 201]:
            reservation_result = reservation_response.json()
            print(f"✅ API Réservation fonctionnelle")
            print(f"   🎾 Réservation créée: {reservation_result.get('court_name')}")
            print(f"   📅 Date: {reservation_result.get('date')}")
            print(f"   ⏰ Heure: {reservation_result.get('start_time')}-{reservation_result.get('end_time')}")
            print(f"   💰 Prix: ${reservation_result.get('price')}")
        else:
            print(f"⚠️  API Réservation: {reservation_response.status_code}")
            print(f"   Message: {reservation_response.text}")
            # Ce n'est pas forcément une erreur si le créneau est déjà pris
    except Exception as e:
        print(f"❌ Erreur test API Réservation: {e}")
    
    # 4. Test sans authentification
    print("\n4️⃣ Test Accès Sans Authentification:")
    try:
        # Test API terrains sans token
        no_auth_response = requests.get('http://127.0.0.1:8000/res/api/terrains/')
        
        if no_auth_response.status_code == 401:
            print("✅ Protection authentification correcte")
        else:
            print(f"⚠️  API accessible sans auth: {no_auth_response.status_code}")
    except Exception as e:
        print(f"❌ Erreur test sans auth: {e}")
    
    return True

def show_manual_test_instructions():
    print("\n🎯 INSTRUCTIONS POUR TEST MANUEL:")
    print("="*40)
    print("1. 🌐 Aller sur: http://127.0.0.1:8000/")
    print("2. 🔍 Observer la nouvelle page d'accueil:")
    print("   - Section Hero avec titre 'Tennis Club Elite'")
    print("   - Bouton 'Reserve Court Now' qui scroll vers le formulaire")
    print("   - Section de réservation rapide")
    print("   - Section des fonctionnalités")
    print("3. 📱 Tester sans connexion:")
    print("   - Voir le message d'authentification")
    print("   - Formulaire de réservation caché")
    print("4. 🔑 Se connecter (<EMAIL> / player123)")
    print("5. 🎾 Tester la réservation:")
    print("   - Formulaire de réservation visible")
    print("   - Courts chargés automatiquement")
    print("   - Validation des champs")
    print("   - Soumission de réservation")
    print("   - Notifications de succès/erreur")
    
    print("\n🎨 FONCTIONNALITÉS À TESTER:")
    print("="*30)
    print("✓ Interface Moderne:")
    print("  - Design responsive avec Bootstrap")
    print("  - Couleurs tennis (vert lime)")
    print("  - Animations et effets hover")
    print("  - Sections bien organisées")
    print("✓ Système d'Authentification:")
    print("  - Détection automatique du statut de connexion")
    print("  - Affichage conditionnel du formulaire")
    print("  - Messages d'invitation à se connecter")
    print("  - Validation des tokens JWT")
    print("✓ Réservation Intégrée:")
    print("  - Chargement automatique des courts")
    print("  - Validation côté client")
    print("  - Soumission AJAX")
    print("  - Notifications en temps réel")
    print("  - Gestion des erreurs")

if __name__ == '__main__':
    print("🎾 TENNIS MANAGEMENT SYSTEM")
    print("🔧 Test Nouvelle Page d'Accueil avec Réservation")
    print("="*55)
    
    success = test_home_page_reservation_system()
    
    if success:
        print("\n🚀 NOUVELLE PAGE D'ACCUEIL FONCTIONNELLE!")
        print("="*45)
        print("✅ Interface moderne et responsive")
        print("✅ Système d'authentification intégré")
        print("✅ Formulaire de réservation fonctionnel")
        print("✅ API terrains accessible")
        print("✅ Validation et notifications")
        print("✅ Design professionnel tennis")
        
        show_manual_test_instructions()
        
        print("\n🎉 FÉLICITATIONS!")
        print("="*20)
        print("La page d'accueil permet maintenant à tous les utilisateurs")
        print("authentifiés de réserver un terrain directement!")
        print("- Interface moderne et intuitive")
        print("- Authentification automatique")
        print("- Réservation en un clic")
        print("- Design responsive et professionnel")
        
    else:
        print("\n⚠️  Des problèmes ont été détectés.")
        print("Vérifiez les erreurs ci-dessus et corrigez-les.")
