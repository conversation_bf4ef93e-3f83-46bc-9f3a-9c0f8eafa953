#!/usr/bin/env python
"""
Script to test coach dashboard equipment management
"""
import os
import sys
import django
import requests

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'tennis_manager.settings')
django.setup()

from django.contrib.auth import get_user_model
from reservations.models import Equipment

User = get_user_model()

def test_coach_equipment_access():
    print("🧪 Testing Coach Equipment Access...")
    
    try:
        # Check if we have a coach user
        coach_user = User.objects.filter(role='coach').first()
        if not coach_user:
            print("❌ No coach user found. Creating one...")
            coach_user = User.objects.create_user(
                username='test_coach',
                email='<EMAIL>',
                password='coach123',
                first_name='Test',
                last_name='Coach',
                role='coach'
            )
            print(f"✅ Created coach user: {coach_user.email}")
        else:
            print(f"✅ Found coach user: {coach_user.email}")
        
        # Test login with known coach credentials
        login_response = requests.post('http://127.0.0.1:8000/auth/api/login/', json={
            'email': '<EMAIL>',
            'password': 'coach123'
        })
        
        if login_response.status_code != 200:
            print(f"❌ Coach login failed: {login_response.status_code}")
            return False
        
        access_token = login_response.json().get('access')
        headers = {'Authorization': f'Bearer {access_token}'}
        
        # Test equipment API access
        equipment_response = requests.get('http://127.0.0.1:8000/res/api/equipment/', headers=headers)
        print(f"Equipment API Status: {equipment_response.status_code}")
        
        if equipment_response.status_code == 200:
            equipment_data = equipment_response.json()
            equipment_count = len(equipment_data.get('equipment', []))
            print(f"✅ Coach can access {equipment_count} equipment items")
            
            # Display equipment summary
            if equipment_count > 0:
                equipment_list = equipment_data.get('equipment', [])
                print(f"\n📋 Equipment Summary:")
                
                types = {}
                total_value = 0
                available_count = 0
                
                for item in equipment_list:
                    item_type = item.get('type', 'Unknown')
                    types[item_type] = types.get(item_type, 0) + 1

                    # Convert price and stock to float/int for calculation
                    price = float(item.get('price', 0))
                    stock = int(item.get('stock_quantity', 0))
                    total_value += price * stock

                    if item.get('available', False):
                        available_count += 1
                
                print(f"   Total Equipment Types: {equipment_count}")
                print(f"   Available Items: {available_count}")
                print(f"   Total Inventory Value: ${total_value:.2f}")
                
                print(f"\n📦 Equipment by Type:")
                for equipment_type, count in types.items():
                    type_icon = {
                        'RAQUETTE': '🎾',
                        'SAC': '👜',
                        'BALLE': '🎾',
                        'CHAUSSURE': '👟',
                        'ACCESSOIRE': '🔧'
                    }.get(equipment_type, '🛠️')
                    print(f"   {type_icon} {equipment_type}: {count} items")
                
                print(f"\n🔍 Sample Equipment:")
                for item in equipment_list[:3]:  # Show first 3 items
                    print(f"   • {item.get('name')} - ${item.get('price')} ({item.get('stock_quantity')} in stock)")
            
            return True
        else:
            print(f"❌ Equipment API failed: {equipment_response.text}")
            return False
        
    except Exception as e:
        print(f"❌ Error testing coach equipment access: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_coach_dashboard_access():
    print("\n🧪 Testing Coach Dashboard Access...")
    
    try:
        # Test coach dashboard page
        dashboard_response = requests.get('http://127.0.0.1:8000/auth/coach-dashboard/')
        print(f"Coach Dashboard Status: {dashboard_response.status_code}")
        
        if dashboard_response.status_code == 200:
            html_content = dashboard_response.text
            
            # Check for equipment management features
            checks = [
                ('Equipment section', 'Equipment Management' in html_content),
                ('Equipment list', 'equipmentList' in html_content),
                ('Search functionality', 'equipmentSearch' in html_content),
                ('Filter functionality', 'equipmentTypeFilter' in html_content),
                ('Request equipment', 'requestEquipment' in html_content),
                ('View details', 'viewEquipmentDetails' in html_content)
            ]
            
            all_passed = True
            for check_name, check_result in checks:
                if check_result:
                    print(f"   ✅ {check_name} found")
                else:
                    print(f"   ❌ {check_name} missing")
                    all_passed = False
            
            return all_passed
        else:
            print(f"❌ Coach dashboard access failed: {dashboard_response.status_code}")
            return False
        
    except Exception as e:
        print(f"❌ Error testing coach dashboard: {e}")
        return False

def check_equipment_data():
    print("\n🧪 Checking Equipment Database...")
    
    try:
        equipment_count = Equipment.objects.count()
        available_count = Equipment.objects.filter(available=True).count()
        
        print(f"   Total Equipment in DB: {equipment_count}")
        print(f"   Available Equipment: {available_count}")
        
        if equipment_count == 0:
            print("   ⚠️  No equipment in database. Run create_test_equipment.py first.")
            return False
        
        # Show equipment types
        types = Equipment.objects.values_list('type', flat=True).distinct()
        print(f"   Equipment Types: {list(types)}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error checking equipment data: {e}")
        return False

if __name__ == '__main__':
    print("🎾 Tennis Management System - Coach Equipment Management Test")
    print("="*70)
    
    # Run all tests
    test1 = check_equipment_data()
    test2 = test_coach_equipment_access()
    test3 = test_coach_dashboard_access()
    
    if all([test1, test2, test3]):
        print("\n🚀 All coach equipment management tests passed!")
        print("✅ Coach can view and request all equipment from database.")
        print("✅ Search and filter functionality is implemented.")
        print("✅ Equipment details and request system working.")
    else:
        print("\n⚠️  Some issues found. Check the specific errors above.")
        
    print("\n🎯 Test the coach dashboard at:")
    print("   http://127.0.0.1:8000/auth/login/")
    print("   Login: <EMAIL> / coach123")
    print("   Navigate to Equipment section")
