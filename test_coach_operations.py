#!/usr/bin/env python
"""
Script to test coach operations (create, read, update, delete)
"""
import os
import sys
import django

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'tennis_manager.settings')
django.setup()

from django.contrib.auth import get_user_model
from reservations.models import Coach, Schedule, ScheduleSlot

User = get_user_model()

def test_coach_operations():
    print("🧪 Testing Coach CRUD operations...")
    
    # Test 1: Create a test coach
    print("\n1️⃣ Testing Coach Creation...")
    try:
        test_coach = Coach.objects.create(
            name="Test Coach",
            email="<EMAIL>",
            phone="******-9999",
            price_per_hour=50.00,
            experience=5
        )
        print(f"✅ Created coach: {test_coach.name} (ID: {test_coach.id})")
    except Exception as e:
        print(f"❌ Error creating coach: {e}")
        return False
    
    # Test 2: Read coach
    print("\n2️⃣ Testing Coach Reading...")
    try:
        coaches = Coach.objects.all()
        print(f"✅ Found {coaches.count()} coaches:")
        for coach in coaches:
            print(f"   - {coach.name} (${coach.price_per_hour}/hour)")
    except Exception as e:
        print(f"❌ Error reading coaches: {e}")
        return False
    
    # Test 3: Create a schedule for the coach
    print("\n3️⃣ Testing Schedule Creation...")
    try:
        admin_user = User.objects.filter(role='admin').first()
        if not admin_user:
            admin_user = User.objects.filter(is_superuser=True).first()
        
        if admin_user:
            test_schedule = Schedule.objects.create(
                coach=test_coach,
                day_of_week='monday',
                start_time='09:00',
                end_time='10:00',
                created_by=admin_user
            )
            print(f"✅ Created schedule: {test_schedule}")
        else:
            print("⚠️  No admin user found, skipping schedule creation")
    except Exception as e:
        print(f"❌ Error creating schedule: {e}")
        return False
    
    # Test 4: Create a schedule slot
    print("\n4️⃣ Testing Schedule Slot Creation...")
    try:
        from datetime import date
        test_slot = ScheduleSlot.objects.create(
            coach=test_coach,
            date=date.today(),
            start_time='09:00',
            end_time='10:00',
            is_booked=False
        )
        print(f"✅ Created schedule slot: {test_slot}")
    except Exception as e:
        print(f"❌ Error creating schedule slot: {e}")
        return False
    
    # Test 5: Update coach
    print("\n5️⃣ Testing Coach Update...")
    try:
        test_coach.price_per_hour = 60.00
        test_coach.save()
        print(f"✅ Updated coach price to ${test_coach.price_per_hour}")
    except Exception as e:
        print(f"❌ Error updating coach: {e}")
        return False
    
    # Test 6: Delete coach (this was causing the original error)
    print("\n6️⃣ Testing Coach Deletion...")
    try:
        coach_id = test_coach.id
        coach_name = test_coach.name
        test_coach.delete()
        print(f"✅ Successfully deleted coach: {coach_name} (ID: {coach_id})")
    except Exception as e:
        print(f"❌ Error deleting coach: {e}")
        return False
    
    print("\n🎉 All coach operations completed successfully!")
    return True

def test_api_endpoints():
    print("\n🌐 Testing API endpoints...")
    
    import requests
    base_url = "http://127.0.0.1:8000"
    
    endpoints_to_test = [
        "/res/coaches/",
        "/res/terrains/",
    ]
    
    for endpoint in endpoints_to_test:
        try:
            response = requests.get(f"{base_url}{endpoint}", timeout=5)
            if response.status_code == 200:
                print(f"✅ {endpoint} - Status: {response.status_code}")
            else:
                print(f"⚠️  {endpoint} - Status: {response.status_code}")
        except requests.exceptions.ConnectionError:
            print(f"❌ {endpoint} - Server not running")
        except Exception as e:
            print(f"❌ {endpoint} - Error: {e}")

if __name__ == '__main__':
    print("🎾 Tennis Management System - Coach Operations Test")
    print("="*60)
    
    success = test_coach_operations()
    test_api_endpoints()
    
    if success:
        print("\n🚀 All tests passed! The coach deletion error is FIXED!")
        print("✅ The tennis management system is fully operational.")
    else:
        print("\n⚠️  Some tests failed. Check the errors above.")
