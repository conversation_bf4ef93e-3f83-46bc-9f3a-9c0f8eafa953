#!/usr/bin/env python
"""
Script to test API endpoints directly
"""
import os
import sys
import django
import requests
import json
from datetime import datetime, timedelta, date

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'tennis_manager.settings')
django.setup()

from django.contrib.auth import get_user_model
from reservations.models import Terrain

User = get_user_model()

def test_login_and_reservation():
    print("🧪 Testing Login and Court Reservation API...")
    
    try:
        # Test login first
        login_url = 'http://127.0.0.1:8000/auth/api/login/'
        login_data = {
            'email': '<EMAIL>',
            'password': 'player123'
        }
        
        print(f"1️⃣ Testing login...")
        login_response = requests.post(login_url, json=login_data)
        print(f"   Login Status: {login_response.status_code}")
        
        if login_response.status_code != 200:
            print(f"   Login failed: {login_response.text}")
            return False
        
        login_result = login_response.json()
        access_token = login_result.get('access')
        
        if not access_token:
            print(f"   No access token received: {login_result}")
            return False
        
        print(f"   ✅ Login successful, got access token")
        
        # Test court reservation
        print(f"\n2️⃣ Testing court reservation...")
        
        # Get available court
        courts = Terrain.objects.filter(available=True)
        if not courts.exists():
            print("   ❌ No available courts found")
            return False
        
        court = courts.first()
        print(f"   Using court: {court.name} (ID: {court.id})")
        
        # Prepare reservation data (use a different time to avoid conflicts)
        tomorrow = date.today() + timedelta(days=1)
        from datetime import datetime
        current_time = datetime.now()
        unique_hour = 12 + (current_time.minute % 6)  # Generate unique hour between 12-17

        reservation_data = {
            'terrain_id': court.id,
            'date': tomorrow.strftime('%Y-%m-%d'),
            'start_time': f'{unique_hour:02d}:00',
            'end_time': f'{unique_hour+1:02d}:00'
        }
        
        print(f"   Reservation data: {reservation_data}")
        
        # Make reservation request
        reservation_url = 'http://127.0.0.1:8000/res/api/court-reservations/'
        headers = {
            'Authorization': f'Bearer {access_token}',
            'Content-Type': 'application/json'
        }
        
        reservation_response = requests.post(
            reservation_url, 
            json=reservation_data, 
            headers=headers
        )
        
        print(f"   Reservation Status: {reservation_response.status_code}")
        print(f"   Reservation Response: {reservation_response.text}")
        
        if reservation_response.status_code == 200:
            print(f"   ✅ Reservation created successfully!")
            return True
        else:
            print(f"   ❌ Reservation failed")
            try:
                error_data = reservation_response.json()
                print(f"   Error details: {error_data}")
            except:
                print(f"   Raw error: {reservation_response.text}")
            return False
        
    except Exception as e:
        print(f"❌ Error testing API: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_get_courts():
    print("\n🧪 Testing Get Courts API...")

    try:
        # Test without authentication first (should require auth)
        courts_url = 'http://127.0.0.1:8000/res/api/terrains/'
        response = requests.get(courts_url)

        print(f"   Status: {response.status_code}")

        if response.status_code == 401:
            print(f"   ✅ Correctly requires authentication")
            return True
        elif response.status_code == 200:
            courts_data = response.json()
            print(f"   ✅ Got {len(courts_data.get('terrains', []))} courts")
            return True
        else:
            print(f"   ❌ Unexpected response: {response.text}")
            return False

    except Exception as e:
        print(f"❌ Error testing courts API: {e}")
        return False

def test_player_reservations():
    print("\n🧪 Testing Player Reservations API...")
    
    try:
        # Login first
        login_url = 'http://127.0.0.1:8000/auth/api/login/'
        login_data = {
            'email': '<EMAIL>',
            'password': 'player123'
        }
        
        login_response = requests.post(login_url, json=login_data)
        if login_response.status_code != 200:
            print(f"   ❌ Login failed for reservations test")
            return False
        
        access_token = login_response.json().get('access')
        headers = {
            'Authorization': f'Bearer {access_token}',
            'Content-Type': 'application/json'
        }
        
        # Test player reservations endpoint
        reservations_url = 'http://127.0.0.1:8000/res/api/player/reservations/'
        response = requests.get(reservations_url, headers=headers)
        
        print(f"   Status: {response.status_code}")
        
        if response.status_code == 200:
            reservations_data = response.json()
            print(f"   ✅ Got reservations data:")
            print(f"      Total: {reservations_data.get('total_reservations', 0)}")
            print(f"      Upcoming: {reservations_data.get('upcoming_count', 0)}")
            print(f"      Past: {reservations_data.get('past_count', 0)}")
            return True
        else:
            print(f"   ❌ Failed to get reservations: {response.text}")
            return False
        
    except Exception as e:
        print(f"❌ Error testing reservations API: {e}")
        return False

if __name__ == '__main__':
    print("🎾 Tennis Management System - Direct API Test")
    print("="*60)
    
    # Test basic endpoints
    test1 = test_get_courts()
    test2 = test_player_reservations()
    test3 = test_login_and_reservation()
    
    if test1 and test2 and test3:
        print("\n🚀 All API tests passed!")
        print("✅ The APIs are working correctly.")
    else:
        print("\n⚠️  Some API tests failed.")
        print("🔍 Check the specific error messages above.")
