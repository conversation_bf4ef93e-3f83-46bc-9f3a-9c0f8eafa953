from django.core.management.base import BaseCommand
from django.contrib.auth import get_user_model
from reservations.models import Coach

User = get_user_model()

class Command(BaseCommand):
    help = 'Link existing coach users to Coach profiles'

    def handle(self, *args, **kwargs):
        self.stdout.write("🔗 Linking existing coach users to Coach profiles...")
        
        # Get all users with role='coach'
        coach_users = User.objects.filter(role='coach')
        self.stdout.write(f"Found {coach_users.count()} users with coach role")
        
        created_count = 0
        updated_count = 0
        linked_count = 0
        
        for user in coach_users:
            try:
                # Check if coach profile already exists
                coach = Coach.objects.filter(user=user).first()
                
                if coach:
                    self.stdout.write(f"✅ Coach profile already linked: {user.username}")
                    linked_count += 1
                    continue
                
                # Check if there's a coach with the same email
                existing_coach = Coach.objects.filter(email=user.email).first()
                
                if existing_coach and not existing_coach.user:
                    # Link existing coach to user
                    existing_coach.user = user
                    existing_coach.name = f"{user.first_name} {user.last_name}".strip() or user.username
                    existing_coach.save()
                    self.stdout.write(f"🔗 Linked existing coach to user: {user.username}")
                    updated_count += 1
                    
                elif existing_coach and existing_coach.user:
                    self.stdout.write(f"⚠️  Coach with email {user.email} already linked to another user")
                    
                else:
                    # Create new coach profile
                    coach = Coach.objects.create(
                        user=user,
                        name=f"{user.first_name} {user.last_name}".strip() or user.username,
                        email=user.email,
                        price_per_hour=50.00,
                        experience=1,
                        bio=f"Professional tennis coach with expertise in player development.",
                        specialization="General Tennis Coaching",
                        is_active=True
                    )
                    self.stdout.write(f"✅ Created new coach profile: {user.username}")
                    created_count += 1
                    
            except Exception as e:
                self.stdout.write(f"❌ Error processing user {user.username}: {e}")
        
        # Summary
        self.stdout.write("\n📊 Summary:")
        self.stdout.write(f"   Already linked: {linked_count}")
        self.stdout.write(f"   Newly created: {created_count}")
        self.stdout.write(f"   Updated existing: {updated_count}")
        
        # Show all coaches now
        all_coaches = Coach.objects.all()
        self.stdout.write(f"\n👥 Total coaches in database: {all_coaches.count()}")
        
        for coach in all_coaches:
            user_info = f" (User: {coach.user.username})" if coach.user else " (No user linked)"
            self.stdout.write(f"   🎾 {coach.name} - ${coach.price_per_hour}/hour{user_info}")
        
        self.stdout.write("\n🚀 Coach user linking completed!")
        self.stdout.write("✅ All coach users should now be able to access coach dashboard and be available for reservations.")
