{% load static %}
<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Elite Sports Club - Home</title>

    <!-- Bootstrap Icons -->
    <link
      href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.0/font/bootstrap-icons.css"
      rel="stylesheet"
    />
    <!-- Bootstrap CSS -->
    <link
      href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css"
      rel="stylesheet"
    />
    <!-- Google Fonts -->
    <link
      href="https://fonts.googleapis.com/css2?family=Orbitron:wght@400;700;900&family=Rajdhani:wght@300;400;500;600;700&display=swap"
      rel="stylesheet"
    />

    <style>
      :root {
        --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        --secondary-gradient: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
        --accent-gradient: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
        --success-gradient: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);
        --warning-gradient: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
        --dark-gradient: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
        --sports-blue: #1e40af;
        --sports-orange: #f97316;
        --sports-green: #10b981;
        --sports-purple: #8b5cf6;
      }

      * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
      }

      body {
        font-family: "Rajdhani", sans-serif;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        min-height: 100vh;
        padding-top: 80px;
      }

      .sports-navbar {
        background: linear-gradient(
          135deg,
          rgba(30, 64, 175, 0.95) 0%,
          rgba(139, 92, 246, 0.95) 50%,
          rgba(16, 185, 129, 0.95) 100%
        );
        backdrop-filter: blur(20px);
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        transition: all 0.3s ease;
        padding: 0.75rem 0;
        position: fixed;
        top: 0;
        width: 100%;
        z-index: 1000;
      }

      .sports-navbar.scrolled {
        background: linear-gradient(
          135deg,
          rgba(30, 64, 175, 0.98) 0%,
          rgba(139, 92, 246, 0.98) 50%,
          rgba(16, 185, 129, 0.98) 100%
        );
        backdrop-filter: blur(25px);
        box-shadow: 0 12px 40px rgba(0, 0, 0, 0.2);
        padding: 0.5rem 0;
      }

      .sports-brand {
        font-family: "Orbitron", monospace;
        font-size: 1.8rem;
        font-weight: 900;
        background: linear-gradient(45deg, #fff, #f8f9fa, #e9ecef);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
        text-decoration: none;
        display: flex;
        align-items: center;
        transition: all 0.3s ease;
        text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
      }

      .sports-brand:hover {
        transform: scale(1.05);
        filter: brightness(1.2);
      }

      .brand-icon {
        width: 45px;
        height: 45px;
        margin-right: 15px;
        background: var(--accent-gradient);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 1.4rem;
        box-shadow: 0 4px 15px rgba(79, 172, 254, 0.4);
        animation: pulse 3s infinite;
        position: relative;
        overflow: hidden;
      }

      .brand-icon::before {
        content: "";
        position: absolute;
        top: -50%;
        left: -50%;
        width: 200%;
        height: 200%;
        background: linear-gradient(
          45deg,
          transparent,
          rgba(255, 255, 255, 0.3),
          transparent
        );
        transform: rotate(45deg);
        animation: shine 2s infinite;
      }

      @keyframes pulse {
        0% {
          box-shadow: 0 4px 15px rgba(79, 172, 254, 0.4);
        }
        50% {
          box-shadow: 0 4px 25px rgba(79, 172, 254, 0.7);
        }
        100% {
          box-shadow: 0 4px 15px rgba(79, 172, 254, 0.4);
        }
      }

      @keyframes shine {
        0% {
          transform: translateX(-100%) translateY(-100%) rotate(45deg);
        }
        100% {
          transform: translateX(100%) translateY(100%) rotate(45deg);
        }
      }

      .sports-nav-link {
        color: rgba(255, 255, 255, 0.9) !important;
        font-weight: 600;
        font-size: 1.1rem;
        padding: 0.75rem 1.5rem !important;
        border-radius: 30px;
        transition: all 0.3s ease;
        position: relative;
        overflow: hidden;
        margin: 0 0.25rem;
        text-decoration: none;
        display: flex;
        align-items: center;
        text-transform: uppercase;
        letter-spacing: 0.5px;
      }

      .sports-nav-link::before {
        content: "";
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(
          90deg,
          transparent,
          rgba(255, 255, 255, 0.2),
          transparent
        );
        transition: left 0.5s;
      }

      .sports-nav-link:hover::before {
        left: 100%;
      }

      .sports-nav-link:hover {
        color: white !important;
        background: rgba(255, 255, 255, 0.15);
        transform: translateY(-3px);
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
      }

      .sports-nav-link.active {
        background: var(--accent-gradient);
        color: white !important;
        box-shadow: 0 8px 25px rgba(79, 172, 254, 0.5);
      }

      .nav-icon {
        margin-right: 10px;
        font-size: 1.2rem;
        transition: transform 0.3s ease;
      }

      .sports-nav-link:hover .nav-icon {
        transform: scale(1.3) rotate(10deg);
      }

      .sports-dropdown {
        background: linear-gradient(
          135deg,
          rgba(255, 255, 255, 0.95) 0%,
          rgba(248, 249, 250, 0.95) 100%
        );
        backdrop-filter: blur(20px);
        border: 1px solid rgba(255, 255, 255, 0.2);
        border-radius: 20px;
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
        padding: 1rem 0;
        margin-top: 0.5rem;
      }

      .sports-dropdown-item {
        color: #2c3e50 !important;
        padding: 1rem 2rem;
        font-weight: 600;
        transition: all 0.3s ease;
        border-radius: 0;
        display: flex;
        align-items: center;
        font-size: 1.05rem;
      }

      .sports-dropdown-item:hover {
        background: var(--primary-gradient);
        color: white !important;
        transform: translateX(10px);
      }

      .sports-dropdown-item i {
        margin-right: 15px;
        width: 25px;
        text-align: center;
        font-size: 1.2rem;
      }

      .user-avatar {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        background: var(--success-gradient);
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 12px;
        color: white;
        font-weight: bold;
        font-size: 1.1rem;
        box-shadow: 0 4px 15px rgba(17, 153, 142, 0.4);
        border: 2px solid rgba(255, 255, 255, 0.3);
      }

      .auth-buttons .sports-nav-link {
        margin: 0 0.5rem;
        font-size: 1rem;
        padding: 0.6rem 1.8rem !important;
      }

      .login-btn {
        background: transparent;
        border: 2px solid rgba(255, 255, 255, 0.4);
      }

      .login-btn:hover {
        background: rgba(255, 255, 255, 0.15);
        border-color: rgba(255, 255, 255, 0.8);
      }

      .register-btn {
        background: var(--success-gradient);
        border: 2px solid transparent;
      }

      .register-btn:hover {
        background: var(--warning-gradient);
        transform: translateY(-3px);
        box-shadow: 0 10px 30px rgba(252, 182, 159, 0.5);
      }

      .navbar-toggler {
        border: none;
        padding: 0.75rem;
        border-radius: 15px;
        background: rgba(255, 255, 255, 0.1);
        transition: all 0.3s ease;
      }

      .navbar-toggler:hover {
        background: rgba(255, 255, 255, 0.2);
        transform: scale(1.1);
      }

      .navbar-toggler-icon {
        background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 30 30'%3e%3cpath stroke='rgba%28255, 255, 255, 0.9%29' stroke-linecap='round' stroke-miterlimit='10' stroke-width='2' d='m4 7h22M4 15h22M4 23h22'/%3e%3c/svg%3e");
      }

      /* Sports Icons Animation */
      .sports-icons {
        display: flex;
        gap: 15px;
        margin-left: 20px;
      }

      .sport-icon {
        width: 35px;
        height: 35px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 1rem;
        transition: all 0.3s ease;
        cursor: pointer;
      }

      .sport-icon.tennis {
        background: var(--success-gradient);
      }

      .sport-icon.football {
        background: var(--sports-orange);
      }

      .sport-icon.basketball {
        background: var(--sports-purple);
      }

      .sport-icon:hover {
        transform: scale(1.2) rotate(360deg);
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
      }
      /* Hero Section */
      .hero-section {
        height: 100vh;
        background: linear-gradient(
            135deg,
            rgba(30, 64, 175, 0.8) 0%,
            rgba(139, 92, 246, 0.8) 50%,
            rgba(16, 185, 129, 0.8) 100%
          ),
          url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1200 800"><defs><linearGradient id="sportsBg" x1="0%" y1="0%" x2="100%" y2="100%"><stop offset="0%" style="stop-color:%23667eea;stop-opacity:0.1"/><stop offset="50%" style="stop-color:%23764ba2;stop-opacity:0.1"/><stop offset="100%" style="stop-color:%2311998e;stop-opacity:0.1"/></linearGradient><pattern id="sportsPattern" x="0" y="0" width="120" height="120" patternUnits="userSpaceOnUse"><circle cx="30" cy="30" r="4" fill="rgba(255,255,255,0.1)"/><circle cx="90" cy="90" r="3" fill="rgba(255,255,255,0.08)"/><polygon points="60,15 75,40 45,40" fill="rgba(255,255,255,0.06)"/><rect x="75" y="70" width="10" height="10" rx="2" fill="rgba(255,255,255,0.05)"/><path d="M20,80 Q30,70 40,80 Q30,90 20,80" fill="rgba(255,255,255,0.04)"/></pattern></defs><rect width="100%" height="100%" fill="url(%23sportsBg)"/><rect width="100%" height="100%" fill="url(%23sportsPattern)"/></svg>')
            center/cover;
        display: flex;
        align-items: center;
        justify-content: center;
        position: relative;
        overflow: hidden;
      }

      /* Sports Pattern Overlay */
      .hero-section::before {
        content: "";
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: radial-gradient(
            circle at 20% 30%,
            rgba(255, 255, 255, 0.1) 0%,
            transparent 50%
          ),
          radial-gradient(
            circle at 80% 70%,
            rgba(255, 255, 255, 0.08) 0%,
            transparent 50%
          ),
          radial-gradient(
            circle at 40% 80%,
            rgba(255, 255, 255, 0.06) 0%,
            transparent 50%
          );
        z-index: 1;
        animation: shimmer 10s ease-in-out infinite;
      }

      @keyframes shimmer {
        0%,
        100% {
          opacity: 0.3;
        }
        50% {
          opacity: 0.6;
        }
      }

      .hero-content {
        text-align: center;
        color: white;
        z-index: 2;
        max-width: 800px;
        padding: 2rem;
      }

      .hero-title {
        font-family: "Orbitron", monospace;
        font-size: 4rem;
        font-weight: 900;
        margin-bottom: 1rem;
        text-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
        animation: fadeInUp 1s ease-out;
      }

      .hero-subtitle {
        font-size: 1.5rem;
        font-weight: 300;
        margin-bottom: 2rem;
        opacity: 0.9;
        animation: fadeInUp 1s ease-out 0.3s both;
      }

      .hero-buttons {
        display: flex;
        gap: 1.5rem;
        justify-content: center;
        flex-wrap: wrap;
        animation: fadeInUp 1s ease-out 0.6s both;
      }

      .hero-btn {
        padding: 1rem 2.5rem;
        font-size: 1.1rem;
        font-weight: 600;
        border: none;
        border-radius: 50px;
        text-decoration: none;
        transition: all 0.3s ease;
        text-transform: uppercase;
        letter-spacing: 1px;
        position: relative;
        overflow: hidden;
      }

      .hero-btn.primary {
        background: var(--success-gradient);
        color: white;
        box-shadow: 0 8px 25px rgba(16, 185, 129, 0.4);
      }

      .hero-btn.secondary {
        background: transparent;
        color: white;
        border: 2px solid rgba(255, 255, 255, 0.5);
      }

      .hero-btn:hover {
        transform: translateY(-3px);
        box-shadow: 0 15px 35px rgba(0, 0, 0, 0.3);
      }

      /* Floating Elements */
      .floating-element {
        position: absolute;
        opacity: 0.1;
        animation: float 6s ease-in-out infinite;
      }

      .floating-element:nth-child(1) {
        top: 20%;
        left: 10%;
        animation-delay: 0s;
      }
      .floating-element:nth-child(2) {
        top: 60%;
        right: 15%;
        animation-delay: 2s;
      }
      .floating-element:nth-child(3) {
        bottom: 30%;
        left: 20%;
        animation-delay: 4s;
      }

      @keyframes float {
        0%,
        100% {
          transform: translateY(0px) rotate(0deg);
        }
        50% {
          transform: translateY(-20px) rotate(180deg);
        }
      }

      @keyframes fadeInUp {
        from {
          opacity: 0;
          transform: translateY(30px);
        }
        to {
          opacity: 1;
          transform: translateY(0);
        }
      }

      /* Sports Cards Section */
      .sports-section {
        padding: 5rem 0;
        background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
      }

      .section-title {
        font-family: "Orbitron", monospace;
        font-size: 3rem;
        font-weight: 700;
        text-align: center;
        margin-bottom: 3rem;
        background: var(--primary-gradient);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
      }

      .sports-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: 2rem;
        max-width: 1200px;
        margin: 0 auto;
        padding: 0 2rem;
      }

      .sport-card {
        background: white;
        border-radius: 20px;
        padding: 2rem;
        text-align: center;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        transition: all 0.3s ease;
        position: relative;
        overflow: hidden;
      }

      .sport-card::before {
        content: "";
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 5px;
        background: var(--accent-gradient);
        transform: scaleX(0);
        transition: transform 0.3s ease;
      }

      .sport-card:hover::before {
        transform: scaleX(1);
      }

      .sport-card:hover {
        transform: translateY(-10px);
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
      }

      .sport-card-icon {
        width: 80px;
        height: 80px;
        margin: 0 auto 1.5rem;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 2rem;
        color: white;
        position: relative;
      }

      .sport-card.tennis .sport-card-icon {
        background: var(--success-gradient);
      }

      .sport-card.football .sport-card-icon {
        background: var(--secondary-gradient);
      }

      .sport-card.basketball .sport-card-icon {
        background: var(--sports-purple);
      }

      .sport-card.swimming .sport-card-icon {
        background: var(--accent-gradient);
      }

      .sport-card-title {
        font-size: 1.5rem;
        font-weight: 700;
        margin-bottom: 1rem;
        color: #2d3748;
      }

      .sport-card-description {
        color: #718096;
        line-height: 1.6;
        margin-bottom: 1.5rem;
      }

      .sport-card-btn {
        background: var(--primary-gradient);
        color: white;
        border: none;
        padding: 0.75rem 2rem;
        border-radius: 25px;
        font-weight: 600;
        text-decoration: none;
        display: inline-block;
        transition: all 0.3s ease;
      }

      .sport-card-btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 20px rgba(102, 126, 234, 0.4);
        color: white;
      }

      /* Mobile Responsive */
      @media (max-width: 768px) {
        .hero-title {
          font-size: 2.5rem;
        }

        .hero-subtitle {
          font-size: 1.2rem;
        }

        .hero-buttons {
          flex-direction: column;
          align-items: center;
        }

        .section-title {
          font-size: 2rem;
        }

        .sports-grid {
          grid-template-columns: 1fr;
          padding: 0 1rem;
        }
      }
    </style>
  </head>
  <body>
    <!-- Modern Sports Navbar -->
    <nav class="navbar navbar-expand-lg sports-navbar" id="sportsNavbar">
      <div class="container-fluid px-4">
        <!-- Brand -->
        <a class="sports-brand" href="{% url 'home' %}">
          <div class="brand-icon">
            <i class="bi bi-trophy-fill"></i>
          </div>
          <span>ELITE SPORTS</span>
        </a>

        <!-- Sports Icons -->
        <div class="sports-icons d-none d-lg-flex">
          <div class="sport-icon tennis" title="Tennis">
            <i class="bi bi-circle"></i>
          </div>
          <div class="sport-icon football" title="Football">
            <i class="bi bi-hexagon-fill"></i>
          </div>
          <div class="sport-icon basketball" title="Basketball">
            <i class="bi bi-circle-fill"></i>
          </div>
        </div>

        <!-- Mobile Toggle -->
        <button
          class="navbar-toggler"
          type="button"
          data-bs-toggle="collapse"
          data-bs-target="#navbarNav"
          aria-controls="navbarNav"
          aria-expanded="false"
          aria-label="Toggle navigation"
        >
          <span class="navbar-toggler-icon"></span>
        </button>

        <!-- Navigation Menu -->
        <div class="collapse navbar-collapse" id="navbarNav">
          <ul class="navbar-nav me-auto">
            <li class="nav-item">
              <a
                class="nav-link sports-nav-link active"
                href="{% url 'home' %}"
              >
                <i class="bi bi-house-door nav-icon"></i>Home
              </a>
            </li>
            <li class="nav-item">
              <a class="nav-link sports-nav-link" href="{% url 'services' %}">
                <i class="bi bi-gear-fill nav-icon"></i>Services
              </a>
            </li>
            <li class="nav-item">
              <a class="nav-link sports-nav-link" href="{% url 'blog' %}">
                <i class="bi bi-journal-text nav-icon"></i>Blog
              </a>
            </li>
            <li class="nav-item">
              <a class="nav-link sports-nav-link" href="{% url 'contact' %}">
                <i class="bi bi-envelope-fill nav-icon"></i>Contact
              </a>
            </li>
          </ul>

          <!-- User Authentication -->
          <ul class="navbar-nav auth-buttons">
            {% if user.is_authenticated %}
            <li class="nav-item dropdown">
              <a
                class="nav-link dropdown-toggle sports-nav-link"
                href="#"
                id="navbarDropdown"
                role="button"
                data-bs-toggle="dropdown"
                aria-expanded="false"
              >
                <div class="user-avatar">{{ user.username|first|upper }}</div>
                {{ user.username }}
              </a>
              <ul class="dropdown-menu sports-dropdown">
                {% if user.role == 'admin' %}
                <li>
                  <a
                    class="dropdown-item sports-dropdown-item"
                    href="{% url 'admin_dashboard' %}"
                  >
                    <i class="bi bi-speedometer2"></i>Admin Dashboard
                  </a>
                </li>
                {% elif user.role == 'coach' %}
                <li>
                  <a
                    class="dropdown-item sports-dropdown-item"
                    href="{% url 'coach_dashboard' %}"
                  >
                    <i class="bi bi-person-badge"></i>Coach Dashboard
                  </a>
                </li>
                {% else %}
                <li>
                  <a
                    class="dropdown-item sports-dropdown-item"
                    href="{% url 'joueur_dashboard' %}"
                  >
                    <i class="bi bi-person-workspace"></i>Player Dashboard
                  </a>
                </li>
                {% endif %}
                <li><hr class="dropdown-divider" /></li>
                <li>
                  <a
                    class="dropdown-item sports-dropdown-item"
                    href="{% url 'logout' %}"
                  >
                    <i class="bi bi-box-arrow-right"></i>Logout
                  </a>
                </li>
              </ul>
            </li>
            {% else %}
            <li class="nav-item">
              <a
                class="nav-link sports-nav-link login-btn"
                href="{% url 'login' %}"
              >
                <i class="bi bi-box-arrow-in-right nav-icon"></i>Login
              </a>
            </li>
            <li class="nav-item">
              <a
                class="nav-link sports-nav-link register-btn"
                href="{% url 'register' %}"
              >
                <i class="bi bi-person-plus nav-icon"></i>Register
              </a>
            </li>
            {% endif %}
          </ul>
        </div>
      </div>
    </nav>

    <!-- Hero Section -->
    <section class="hero-section">
      <!-- Floating Elements -->
      <div class="floating-element">
        <i class="bi bi-trophy-fill" style="font-size: 4rem"></i>
      </div>
      <div class="floating-element">
        <i class="bi bi-award-fill" style="font-size: 3rem"></i>
      </div>
      <div class="floating-element">
        <i class="bi bi-star-fill" style="font-size: 3.5rem"></i>
      </div>

      <div class="hero-content">
        <h1 class="hero-title">ELITE SPORTS CLUB</h1>
        <p class="hero-subtitle">
          Where Champions Are Made - Excellence in Every Sport
        </p>
        <div class="hero-buttons" id="heroButtons">
          <!-- Buttons will be populated by JavaScript based on authentication status -->
        </div>
      </div>
    </section>

    <!-- Sports Section -->
    <section class="sports-section">
      <div class="container">
        <h2 class="section-title">Our Sports</h2>
        <div class="sports-grid">
          <div class="sport-card tennis">
            <div class="sport-card-icon">
              <i class="bi bi-circle"></i>
            </div>
            <h3 class="sport-card-title">Tennis</h3>
            <p class="sport-card-description">
              Professional tennis courts with expert coaching. Master your
              serve, perfect your backhand, and dominate the court.
            </p>
            <a href="{% url 'joueur_dashboard' %}" class="sport-card-btn"
              >Book Court</a
            >
          </div>

          <div class="sport-card football">
            <div class="sport-card-icon">
              <i class="bi bi-hexagon-fill"></i>
            </div>
            <h3 class="sport-card-title">Football</h3>
            <p class="sport-card-description">
              State-of-the-art football facilities with professional training
              programs for all skill levels.
            </p>
            <a href="{% url 'services' %}" class="sport-card-btn">Join Team</a>
          </div>

          <div class="sport-card basketball">
            <div class="sport-card-icon">
              <i class="bi bi-circle-fill"></i>
            </div>
            <h3 class="sport-card-title">Basketball</h3>
            <p class="sport-card-description">
              Indoor basketball courts with professional coaching and
              competitive leagues for all ages.
            </p>
            <a href="{% url 'services' %}" class="sport-card-btn">Play Now</a>
          </div>

          <div class="sport-card swimming">
            <div class="sport-card-icon">
              <i class="bi bi-water"></i>
            </div>
            <h3 class="sport-card-title">Swimming</h3>
            <p class="sport-card-description">
              Olympic-size swimming pool with certified instructors and
              competitive swimming programs.
            </p>
            <a href="{% url 'services' %}" class="sport-card-btn">Dive In</a>
          </div>
        </div>
      </div>
    </section>

    <!-- Stats Section -->
    <section
      class="stats-section"
      style="padding: 4rem 0; background: var(--dark-gradient); color: white"
    >
      <div class="container">
        <div
          class="stats-grid"
          style="
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 2rem;
            max-width: 1000px;
            margin: 0 auto;
            padding: 0 2rem;
          "
        >
          <div class="stat-item" style="text-align: center; padding: 2rem">
            <span
              class="stat-number"
              style="
                font-family: 'Orbitron', monospace;
                font-size: 3rem;
                font-weight: 900;
                color: #4facfe;
                margin-bottom: 0.5rem;
                display: block;
              "
              >500+</span
            >
            <span
              class="stat-label"
              style="
                font-size: 1.1rem;
                opacity: 0.8;
                text-transform: uppercase;
                letter-spacing: 1px;
              "
              >Active Members</span
            >
          </div>
          <div class="stat-item" style="text-align: center; padding: 2rem">
            <span
              class="stat-number"
              style="
                font-family: 'Orbitron', monospace;
                font-size: 3rem;
                font-weight: 900;
                color: #4facfe;
                margin-bottom: 0.5rem;
                display: block;
              "
              >15</span
            >
            <span
              class="stat-label"
              style="
                font-size: 1.1rem;
                opacity: 0.8;
                text-transform: uppercase;
                letter-spacing: 1px;
              "
              >Sports Available</span
            >
          </div>
          <div class="stat-item" style="text-align: center; padding: 2rem">
            <span
              class="stat-number"
              style="
                font-family: 'Orbitron', monospace;
                font-size: 3rem;
                font-weight: 900;
                color: #4facfe;
                margin-bottom: 0.5rem;
                display: block;
              "
              >25</span
            >
            <span
              class="stat-label"
              style="
                font-size: 1.1rem;
                opacity: 0.8;
                text-transform: uppercase;
                letter-spacing: 1px;
              "
              >Expert Coaches</span
            >
          </div>
          <div class="stat-item" style="text-align: center; padding: 2rem">
            <span
              class="stat-number"
              style="
                font-family: 'Orbitron', monospace;
                font-size: 3rem;
                font-weight: 900;
                color: #4facfe;
                margin-bottom: 0.5rem;
                display: block;
              "
              >10</span
            >
            <span
              class="stat-label"
              style="
                font-size: 1.1rem;
                opacity: 0.8;
                text-transform: uppercase;
                letter-spacing: 1px;
              "
              >Years Experience</span
            >
          </div>
        </div>
      </div>
    </section>

    <!-- JavaScript for Navbar Effects -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
      // Navbar scroll effect
      let lastScrollTop = 0;
      const navbar = document.getElementById("sportsNavbar");

      window.addEventListener("scroll", function () {
        let scrollTop =
          window.pageYOffset || document.documentElement.scrollTop;

        if (scrollTop > 100) {
          navbar.classList.add("scrolled");
        } else {
          navbar.classList.remove("scrolled");
        }

        // Hide/show navbar on scroll
        if (scrollTop > lastScrollTop && scrollTop > 200) {
          navbar.classList.add("navbar-scroll-hidden");
        } else {
          navbar.classList.remove("navbar-scroll-hidden");
        }
        lastScrollTop = scrollTop;
      });

      // Active link highlighting
      const currentLocation = location.pathname;
      const menuItems = document.querySelectorAll(".sports-nav-link");

      menuItems.forEach((item) => {
        if (item.getAttribute("href") === currentLocation) {
          item.classList.add("active");
        }
      });

      // Sports icons animation
      const sportIcons = document.querySelectorAll(".sport-icon");
      sportIcons.forEach((icon) => {
        icon.addEventListener("click", function () {
          this.style.transform = "scale(1.5) rotate(720deg)";
          setTimeout(() => {
            this.style.transform = "";
          }, 600);
        });
      });

      // Counter animation for stats
      function animateCounters() {
        const counters = document.querySelectorAll(".stat-number");

        counters.forEach((counter) => {
          const target = parseInt(counter.textContent);
          const increment = target / 100;
          let current = 0;

          const timer = setInterval(() => {
            current += increment;
            if (current >= target) {
              counter.textContent =
                target + (counter.textContent.includes("+") ? "+" : "");
              clearInterval(timer);
            } else {
              counter.textContent =
                Math.floor(current) +
                (counter.textContent.includes("+") ? "+" : "");
            }
          }, 20);
        });
      }

      // Trigger animation when stats section is visible
      const observer = new IntersectionObserver((entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            animateCounters();
            observer.unobserve(entry.target);
          }
        });
      });

      const statsSection = document.querySelector(".stats-section");
      if (statsSection) {
        observer.observe(statsSection);
      }

      // Smooth scrolling for anchor links
      document.querySelectorAll('a[href^="#"]').forEach((anchor) => {
        anchor.addEventListener("click", function (e) {
          e.preventDefault();
          const target = document.querySelector(this.getAttribute("href"));
          if (target) {
            target.scrollIntoView({
              behavior: "smooth",
              block: "start",
            });
          }
        });
      });

      // Add parallax effect to hero section
      window.addEventListener("scroll", function () {
        const scrolled = window.pageYOffset;
        const parallax = document.querySelector(".hero-section");
        if (parallax) {
          const speed = scrolled * 0.5;
          parallax.style.transform = `translateY(${speed}px)`;
        }
      });

      // Add hover effects to sport cards
      const sportCards = document.querySelectorAll(".sport-card");
      sportCards.forEach((card) => {
        card.addEventListener("mouseenter", function () {
          this.style.transform = "translateY(-15px) scale(1.02)";
        });

        card.addEventListener("mouseleave", function () {
          this.style.transform = "translateY(0) scale(1)";
        });
      });

      // Dynamic hero buttons based on authentication status
      function updateHeroButtons() {
        const token = localStorage.getItem("token");
        const isAuthenticated = token && token !== "null";
        const heroButtons = document.getElementById("heroButtons");

        if (isAuthenticated) {
          // Authenticated user buttons
          heroButtons.innerHTML = `
            <a href="/member/dashboard/" class="hero-btn primary">
              <i class="bi bi-speedometer2" style="margin-right: 8px;"></i>My Dashboard
            </a>
            <a href="/member/profiles/" class="hero-btn secondary">
              <i class="bi bi-people" style="margin-right: 8px;"></i>View Players
            </a>
            <a href="/events/calendar/" class="hero-btn secondary">
              <i class="bi bi-calendar-event" style="margin-right: 8px;"></i>Events Calendar
            </a>
          `;
        } else {
          // Non-authenticated user buttons
          heroButtons.innerHTML = `
            <a href="/auth/register/" class="hero-btn primary">
              <i class="bi bi-person-plus" style="margin-right: 8px;"></i>Join Now
            </a>
            <a href="/services/" class="hero-btn secondary">
              <i class="bi bi-gear-fill" style="margin-right: 8px;"></i>Explore Sports
            </a>
          `;
        }
      }

      // Initialize hero buttons on page load
      document.addEventListener("DOMContentLoaded", function () {
        updateHeroButtons();
      });

      // Listen for authentication changes
      window.addEventListener("storage", function (e) {
        if (e.key === "token") {
          updateHeroButtons();
        }
      });
    </script>
  </body>
</html>
