{% load static %}
<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Player Profiles - Elite Sports Club</title>

    <!-- Bootstrap Icons -->
    <link
      href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.0/font/bootstrap-icons.css"
      rel="stylesheet"
    />
    <!-- Bootstrap CSS -->
    <link
      href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css"
      rel="stylesheet"
    />
    <!-- Google Fonts -->
    <link
      href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Poppins:wght@300;400;500;600;700&display=swap"
      rel="stylesheet"
    />
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

    <style>
      :root {
        --primary-color: #6366f1;
        --secondary-color: #10b981;
        --accent-color: #f59e0b;
        --danger-color: #ef4444;
        --gray-50: #f9fafb;
        --gray-100: #f3f4f6;
        --gray-200: #e5e7eb;
        --gray-600: #4b5563;
        --gray-700: #374151;
        --gray-800: #1f2937;
      }

      body {
        font-family: "Inter", sans-serif;
        background: linear-gradient(135deg, #f0fdfa 0%, #ecfdf5 100%);
        color: var(--gray-700);
        padding-top: 80px;
      }

      .profiles-container {
        max-width: 1400px;
        margin: 0 auto;
        padding: 2rem;
      }

      .page-header {
        background: white;
        border-radius: 16px;
        padding: 2rem;
        margin-bottom: 2rem;
        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
        border: 1px solid var(--gray-100);
      }

      .page-title {
        font-family: "Poppins", sans-serif;
        font-size: 2.5rem;
        font-weight: 700;
        color: var(--gray-800);
        margin: 0;
        display: flex;
        align-items: center;
        gap: 1rem;
      }

      .title-icon {
        width: 56px;
        height: 56px;
        background: linear-gradient(
          135deg,
          var(--primary-color) 0%,
          var(--secondary-color) 100%
        );
        border-radius: 16px;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 1.5rem;
      }

      .search-filters {
        background: white;
        border-radius: 16px;
        padding: 1.5rem;
        margin-bottom: 2rem;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        border: 1px solid var(--gray-100);
      }

      .player-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
        gap: 2rem;
      }

      .player-card {
        background: white;
        border-radius: 20px;
        padding: 2rem;
        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
        border: 1px solid var(--gray-100);
        transition: all 0.3s ease;
        position: relative;
        overflow: hidden;
      }

      .player-card::before {
        content: "";
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 4px;
        background: linear-gradient(
          90deg,
          var(--primary-color),
          var(--secondary-color)
        );
      }

      .player-card:hover {
        transform: translateY(-8px);
        box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
      }

      .player-header {
        display: flex;
        align-items: center;
        gap: 1rem;
        margin-bottom: 1.5rem;
      }

      .player-avatar {
        width: 80px;
        height: 80px;
        border-radius: 50%;
        background: linear-gradient(
          135deg,
          var(--primary-color),
          var(--secondary-color)
        );
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 2rem;
        font-weight: 700;
        position: relative;
        overflow: hidden;
      }

      .player-avatar img {
        width: 100%;
        height: 100%;
        object-fit: cover;
        border-radius: 50%;
      }

      .player-info h3 {
        font-family: "Poppins", sans-serif;
        font-weight: 600;
        color: var(--gray-800);
        margin: 0;
        font-size: 1.25rem;
      }

      .player-role {
        color: var(--gray-600);
        font-size: 0.875rem;
        margin: 0.25rem 0;
      }

      .player-level {
        display: inline-block;
        padding: 0.25rem 0.75rem;
        border-radius: 20px;
        font-size: 0.75rem;
        font-weight: 600;
        text-transform: uppercase;
      }

      .level-beginner {
        background: #dbeafe;
        color: #1e40af;
      }

      .level-intermediate {
        background: #fef3c7;
        color: #d97706;
      }

      .level-advanced {
        background: #dcfce7;
        color: #16a34a;
      }

      .level-expert {
        background: #fce7f3;
        color: #be185d;
      }

      .stats-grid {
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        gap: 1rem;
        margin: 1.5rem 0;
      }

      .stat-item {
        text-align: center;
        padding: 1rem;
        background: var(--gray-50);
        border-radius: 12px;
        border: 1px solid var(--gray-100);
      }

      .stat-value {
        font-size: 1.5rem;
        font-weight: 700;
        color: var(--primary-color);
        margin-bottom: 0.25rem;
      }

      .stat-label {
        font-size: 0.75rem;
        color: var(--gray-600);
        text-transform: uppercase;
        font-weight: 500;
      }

      .achievements {
        margin-top: 1.5rem;
      }

      .achievements h4 {
        font-size: 1rem;
        font-weight: 600;
        color: var(--gray-800);
        margin-bottom: 0.75rem;
        display: flex;
        align-items: center;
        gap: 0.5rem;
      }

      .achievement-badges {
        display: flex;
        gap: 0.5rem;
        flex-wrap: wrap;
      }

      .achievement-badge {
        width: 32px;
        height: 32px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 0.875rem;
        color: white;
        position: relative;
        cursor: pointer;
      }

      .achievement-badge.gold {
        background: linear-gradient(135deg, #fbbf24, #f59e0b);
      }

      .achievement-badge.silver {
        background: linear-gradient(135deg, #e5e7eb, #9ca3af);
      }

      .achievement-badge.bronze {
        background: linear-gradient(135deg, #d97706, #92400e);
      }

      .player-actions {
        display: flex;
        gap: 0.5rem;
        margin-top: 1.5rem;
      }

      .btn-action {
        flex: 1;
        padding: 0.5rem;
        border: none;
        border-radius: 8px;
        font-weight: 500;
        font-size: 0.875rem;
        transition: all 0.2s ease;
        cursor: pointer;
      }

      .btn-view {
        background: var(--primary-color);
        color: white;
      }

      .btn-view:hover {
        background: #5856eb;
        color: white;
      }

      .btn-message {
        background: var(--gray-100);
        color: var(--gray-700);
      }

      .btn-message:hover {
        background: var(--gray-200);
      }

      .search-input {
        border-radius: 12px;
        border: 2px solid var(--gray-200);
        padding: 0.75rem 1rem;
        font-size: 1rem;
        transition: all 0.2s ease;
      }

      .search-input:focus {
        border-color: var(--primary-color);
        box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
      }

      .filter-select {
        border-radius: 12px;
        border: 2px solid var(--gray-200);
        padding: 0.75rem 1rem;
        font-size: 1rem;
      }

      /* Modal Styles */
      .player-modal .modal-content {
        border-radius: 20px;
        border: none;
        box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
      }

      .player-modal .modal-header {
        border-bottom: 1px solid var(--gray-100);
        border-radius: 20px 20px 0 0;
        padding: 2rem 2rem 1rem;
      }

      .player-modal .modal-body {
        padding: 2rem;
      }

      .performance-chart {
        height: 300px;
        margin: 1rem 0;
      }

      /* Responsive */
      @media (max-width: 768px) {
        .profiles-container {
          padding: 1rem;
        }

        .page-title {
          font-size: 2rem;
        }

        .player-grid {
          grid-template-columns: 1fr;
        }

        .search-filters {
          padding: 1rem;
        }
      }
    </style>
  </head>
  <body>
    <!-- Include Navbar -->
    {% include 'html/navbar.html' %}

    <div class="profiles-container">
      <!-- Page Header -->
      <div class="page-header">
        <h1 class="page-title">
          <div class="title-icon">
            <i class="bi bi-people"></i>
          </div>
          Player Profiles
        </h1>
        <p class="text-muted mt-2">
          Discover our talented athletes, their achievements, and performance
          statistics
        </p>
      </div>

      <!-- Search and Filters -->
      <div class="search-filters">
        <div class="row g-3">
          <div class="col-md-4">
            <input
              type="text"
              class="form-control search-input"
              id="searchInput"
              placeholder="Search players..."
            />
          </div>
          <div class="col-md-3">
            <select class="form-select filter-select" id="sportFilter">
              <option value="">All Sports</option>
              <option value="tennis">Tennis</option>
              <option value="football">Football</option>
              <option value="basketball">Basketball</option>
              <option value="swimming">Swimming</option>
            </select>
          </div>
          <div class="col-md-3">
            <select class="form-select filter-select" id="levelFilter">
              <option value="">All Levels</option>
              <option value="beginner">Beginner</option>
              <option value="intermediate">Intermediate</option>
              <option value="advanced">Advanced</option>
              <option value="expert">Expert</option>
            </select>
          </div>
          <div class="col-md-2">
            <button class="btn btn-primary w-100" id="clearFilters">
              Clear
            </button>
          </div>
        </div>
      </div>

      <!-- Player Grid -->
      <div class="player-grid" id="playerGrid">
        <!-- Players will be populated here by JavaScript -->
      </div>
    </div>

    <!-- Player Detail Modal -->
    <div class="modal fade player-modal" id="playerModal" tabindex="-1">
      <div class="modal-dialog modal-xl">
        <div class="modal-content">
          <div class="modal-header">
            <h5 class="modal-title" id="playerModalTitle">Player Details</h5>
            <button
              type="button"
              class="btn-close"
              data-bs-dismiss="modal"
            ></button>
          </div>
          <div class="modal-body" id="playerModalBody">
            <!-- Player details will be populated here -->
          </div>
        </div>
      </div>
    </div>

    <!-- JavaScript -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
      // Players data will be loaded from API
      let players = [];
      let currentUser = null;

      // Load players from API
      async function loadPlayers() {
        try {
          const token = localStorage.getItem("token");
          if (!token) {
            console.log("No token found, showing static data");
            return;
          }

          const response = await fetch("/res/api/players/", {
            headers: {
              Authorization: `Bearer ${token}`,
              "Content-Type": "application/json",
            },
          });

          if (response.ok) {
            const data = await response.json();
            players = data.players.map((player) => ({
              id: player.id,
              name: player.full_name,
              sport: player.profile.preferred_sport.toLowerCase(),
              level: player.profile.skill_level,
              avatar: null,
              role: `${
                player.profile.skill_level.charAt(0).toUpperCase() +
                player.profile.skill_level.slice(1)
              } Player`,
              stats: {
                matches: player.profile.total_matches,
                wins: player.profile.matches_won,
                ranking: Math.floor(Math.random() * 50) + 1, // Random ranking for now
                experience: Math.floor(
                  (new Date() - new Date(player.date_joined)) /
                    (365.25 * 24 * 60 * 60 * 1000)
                ),
              },
              achievements: generateAchievements(player.profile),
              performance: generatePerformanceData(player.profile),
              bio: player.profile.bio || "No bio available",
              email: player.email,
              joinDate: player.date_joined,
            }));

            filteredPlayers = [...players];
            renderPlayers(filteredPlayers);
          } else {
            console.error("Failed to load players");
          }
        } catch (error) {
          console.error("Error loading players:", error);
        }
      }

      function generateAchievements(profile) {
        const achievements = [];
        if (profile.total_matches > 20) {
          achievements.push({ type: "bronze", title: "Veteran Player" });
        }
        if (profile.win_rate > 70) {
          achievements.push({ type: "gold", title: "Champion" });
        } else if (profile.win_rate > 50) {
          achievements.push({ type: "silver", title: "Skilled Player" });
        }
        if (profile.total_training_hours > 100) {
          achievements.push({ type: "gold", title: "Dedicated Trainer" });
        }
        return achievements;
      }

      function generatePerformanceData(profile) {
        // Generate realistic performance data based on skill level
        const baseScore =
          {
            beginner: 40,
            intermediate: 65,
            advanced: 80,
            professional: 90,
          }[profile.skill_level] || 50;

        return Array.from(
          { length: 6 },
          () => baseScore + Math.floor(Math.random() * 20) - 10
        );
      }

      // Static fallback data
      const staticPlayers = [
        {
          id: 1,
          name: "Alex Johnson",
          sport: "tennis",
          level: "advanced",
          avatar: null,
          role: "Professional Player",
          stats: {
            matches: 45,
            wins: 38,
            ranking: 12,
            experience: 8,
          },
          achievements: [
            { type: "gold", title: "Regional Champion 2024" },
            { type: "silver", title: "State Tournament Runner-up" },
            { type: "bronze", title: "Club Championship" },
          ],
          performance: [85, 88, 92, 89, 94, 91],
        },
        {
          id: 2,
          name: "Sarah Williams",
          sport: "swimming",
          level: "expert",
          avatar: null,
          role: "Olympic Hopeful",
          stats: {
            matches: 32,
            wins: 29,
            ranking: 3,
            experience: 12,
          },
          achievements: [
            { type: "gold", title: "National Champion" },
            { type: "gold", title: "Record Holder" },
            { type: "silver", title: "International Medal" },
          ],
          performance: [92, 94, 96, 98, 95, 97],
        },
        {
          id: 3,
          name: "Mike Chen",
          sport: "basketball",
          level: "intermediate",
          avatar: null,
          role: "Team Captain",
          stats: {
            matches: 28,
            wins: 22,
            ranking: 8,
            experience: 5,
          },
          achievements: [
            { type: "silver", title: "League Runner-up" },
            { type: "bronze", title: "Best Team Player" },
          ],
          performance: [78, 82, 85, 87, 84, 89],
        },
        {
          id: 4,
          name: "Emma Davis",
          sport: "football",
          level: "advanced",
          avatar: null,
          role: "Striker",
          stats: {
            matches: 38,
            wins: 31,
            ranking: 6,
            experience: 7,
          },
          achievements: [
            { type: "gold", title: "Top Scorer 2024" },
            { type: "bronze", title: "MVP Award" },
          ],
          performance: [88, 90, 87, 92, 94, 91],
        },
        {
          id: 5,
          name: "James Wilson",
          sport: "tennis",
          level: "beginner",
          avatar: null,
          role: "Rising Star",
          stats: {
            matches: 15,
            wins: 8,
            ranking: 25,
            experience: 2,
          },
          achievements: [{ type: "bronze", title: "Most Improved Player" }],
          performance: [65, 68, 72, 75, 78, 82],
        },
        {
          id: 6,
          name: "Lisa Rodriguez",
          sport: "swimming",
          level: "intermediate",
          avatar: null,
          role: "Freestyle Specialist",
          stats: {
            matches: 22,
            wins: 18,
            ranking: 15,
            experience: 4,
          },
          achievements: [
            { type: "silver", title: "Regional Medalist" },
            { type: "bronze", title: "Club Record" },
          ],
          performance: [82, 85, 88, 86, 90, 87],
        },
      ];

      let filteredPlayers = [...players];

      function renderPlayers(playersToRender) {
        const grid = document.getElementById("playerGrid");
        grid.innerHTML = "";

        playersToRender.forEach((player) => {
          const playerCard = createPlayerCard(player);
          grid.appendChild(playerCard);
        });
      }

      function createPlayerCard(player) {
        const card = document.createElement("div");
        card.className = "player-card";

        const winRate = Math.round(
          (player.stats.wins / player.stats.matches) * 100
        );

        card.innerHTML = `
                <div class="player-header">
                    <div class="player-avatar">
                        ${
                          player.avatar
                            ? `<img src="${player.avatar}" alt="${player.name}">`
                            : player.name.charAt(0)
                        }
                    </div>
                    <div class="player-info">
                        <h3>${player.name}</h3>
                        <div class="player-role">${player.role}</div>
                        <span class="player-level level-${player.level}">${
          player.level
        }</span>
                    </div>
                </div>
                
                <div class="stats-grid">
                    <div class="stat-item">
                        <div class="stat-value">${player.stats.matches}</div>
                        <div class="stat-label">Matches</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-value">${winRate}%</div>
                        <div class="stat-label">Win Rate</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-value">#${player.stats.ranking}</div>
                        <div class="stat-label">Ranking</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-value">${
                          player.stats.experience
                        }y</div>
                        <div class="stat-label">Experience</div>
                    </div>
                </div>
                
                <div class="achievements">
                    <h4><i class="bi bi-trophy"></i> Achievements</h4>
                    <div class="achievement-badges">
                        ${player.achievements
                          .map(
                            (achievement) =>
                              `<div class="achievement-badge ${achievement.type}" title="${achievement.title}">
                                <i class="bi bi-trophy"></i>
                            </div>`
                          )
                          .join("")}
                    </div>
                </div>
                
                <div class="player-actions">
                    <button class="btn-action btn-view" onclick="showPlayerDetails(${
                      player.id
                    })">
                        <i class="bi bi-eye"></i> View Profile
                    </button>
                    <button class="btn-action btn-message" onclick="startConversation(${
                      player.id
                    })">
                        <i class="bi bi-chat"></i> Message
                    </button>
                    ${
                      player.id === getCurrentUserId()
                        ? `<button class="btn-action btn-dashboard" onclick="goToDashboard()">
                            <i class="bi bi-speedometer2"></i> Dashboard
                        </button>`
                        : `<button class="btn-action btn-challenge">
                            <i class="bi bi-trophy"></i> Challenge
                        </button>`
                    }
                </div>
            `;

        return card;
      }

      function showPlayerDetails(playerId) {
        const player = players.find((p) => p.id === playerId);
        if (!player) return;

        const modal = new bootstrap.Modal(
          document.getElementById("playerModal")
        );
        const title = document.getElementById("playerModalTitle");
        const body = document.getElementById("playerModalBody");

        title.textContent = `${player.name} - Detailed Profile`;

        body.innerHTML = `
                <div class="row">
                    <div class="col-md-4">
                        <div class="text-center">
                            <div class="player-avatar mx-auto mb-3" style="width: 120px; height: 120px; font-size: 3rem;">
                                ${
                                  player.avatar
                                    ? `<img src="${player.avatar}" alt="${player.name}">`
                                    : player.name.charAt(0)
                                }
                            </div>
                            <h3>${player.name}</h3>
                            <p class="text-muted">${player.role}</p>
                            <span class="player-level level-${player.level}">${
          player.level
        }</span>
                        </div>
                    </div>
                    <div class="col-md-8">
                        <h5>Performance Chart</h5>
                        <canvas id="performanceChart" class="performance-chart"></canvas>
                        
                        <div class="row mt-4">
                            <div class="col-md-6">
                                <h6>Career Statistics</h6>
                                <ul class="list-unstyled">
                                    <li><strong>Total Matches:</strong> ${
                                      player.stats.matches
                                    }</li>
                                    <li><strong>Wins:</strong> ${
                                      player.stats.wins
                                    }</li>
                                    <li><strong>Win Rate:</strong> ${Math.round(
                                      (player.stats.wins /
                                        player.stats.matches) *
                                        100
                                    )}%</li>
                                    <li><strong>Current Ranking:</strong> #${
                                      player.stats.ranking
                                    }</li>
                                    <li><strong>Experience:</strong> ${
                                      player.stats.experience
                                    } years</li>
                                </ul>
                            </div>
                            <div class="col-md-6">
                                <h6>Achievements</h6>
                                <ul class="list-unstyled">
                                    ${player.achievements
                                      .map(
                                        (achievement) =>
                                          `<li><i class="bi bi-trophy text-${
                                            achievement.type === "gold"
                                              ? "warning"
                                              : achievement.type === "silver"
                                              ? "secondary"
                                              : "dark"
                                          }"></i> ${achievement.title}</li>`
                                      )
                                      .join("")}
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            `;

        modal.show();

        // Create performance chart
        setTimeout(() => {
          const ctx = document
            .getElementById("performanceChart")
            .getContext("2d");
          new Chart(ctx, {
            type: "line",
            data: {
              labels: ["Jan", "Feb", "Mar", "Apr", "May", "Jun"],
              datasets: [
                {
                  label: "Performance Score",
                  data: player.performance,
                  borderColor: "#6366f1",
                  backgroundColor: "rgba(99, 102, 241, 0.1)",
                  tension: 0.4,
                  fill: true,
                },
              ],
            },
            options: {
              responsive: true,
              maintainAspectRatio: false,
              scales: {
                y: {
                  beginAtZero: true,
                  max: 100,
                },
              },
            },
          });
        }, 100);
      }

      // Get current user ID from token
      function getCurrentUserId() {
        try {
          const token = localStorage.getItem("token");
          if (!token) return null;

          // Decode JWT token to get user ID
          const payload = JSON.parse(atob(token.split(".")[1]));
          return payload.user_id;
        } catch (error) {
          console.error("Error getting current user ID:", error);
          return null;
        }
      }

      // Go to player dashboard
      function goToDashboard() {
        window.location.href = "/member/dashboard/";
      }

      // Start conversation with a player
      async function startConversation(playerId) {
        try {
          const token = localStorage.getItem("token");
          if (!token) {
            alert("Please log in to send messages");
            return;
          }

          const response = await fetch("/res/api/messaging/conversations/", {
            method: "POST",
            headers: {
              Authorization: `Bearer ${token}`,
              "Content-Type": "application/json",
            },
            body: JSON.stringify({
              user_id: playerId,
            }),
          });

          if (response.ok) {
            const data = await response.json();
            // Redirect to messaging page with conversation ID
            window.location.href = `/member/messages/?conversation=${data.conversation_id}`;
          } else {
            const error = await response.json();
            alert(error.error || "Failed to start conversation");
          }
        } catch (error) {
          console.error("Error starting conversation:", error);
          alert("Failed to start conversation");
        }
      }

      // Filter functionality
      function filterPlayers() {
        const searchTerm = document
          .getElementById("searchInput")
          .value.toLowerCase();
        const sportFilter = document.getElementById("sportFilter").value;
        const levelFilter = document.getElementById("levelFilter").value;

        filteredPlayers = players.filter((player) => {
          const matchesSearch =
            player.name.toLowerCase().includes(searchTerm) ||
            player.role.toLowerCase().includes(searchTerm);
          const matchesSport = !sportFilter || player.sport === sportFilter;
          const matchesLevel = !levelFilter || player.level === levelFilter;

          return matchesSearch && matchesSport && matchesLevel;
        });

        renderPlayers(filteredPlayers);
      }

      // Event listeners
      document
        .getElementById("searchInput")
        .addEventListener("input", filterPlayers);
      document
        .getElementById("sportFilter")
        .addEventListener("change", filterPlayers);
      document
        .getElementById("levelFilter")
        .addEventListener("change", filterPlayers);
      document
        .getElementById("clearFilters")
        .addEventListener("click", function () {
          document.getElementById("searchInput").value = "";
          document.getElementById("sportFilter").value = "";
          document.getElementById("levelFilter").value = "";
          filteredPlayers = [...players];
          renderPlayers(filteredPlayers);
        });

      // Initial render
      document.addEventListener("DOMContentLoaded", function () {
        // Try to load real data first, fallback to static data
        loadPlayers().then(() => {
          if (players.length === 0) {
            // Use static data as fallback
            players = staticPlayers;
            filteredPlayers = [...players];
            renderPlayers(filteredPlayers);
          }
        });
      });

      // Add some animation effects
      document.addEventListener("DOMContentLoaded", function () {
        const cards = document.querySelectorAll(".player-card");
        cards.forEach((card, index) => {
          card.style.animationDelay = `${index * 0.1}s`;
          card.classList.add("animate-in");
        });
      });

      // Add CSS animation
      const style = document.createElement("style");
      style.textContent = `
            @keyframes fadeInUp {
                from {
                    opacity: 0;
                    transform: translateY(30px);
                }
                to {
                    opacity: 1;
                    transform: translateY(0);
                }
            }
            .animate-in {
                animation: fadeInUp 0.6s ease-out forwards;
            }
        `;
      document.head.appendChild(style);
    </script>
  </body>
</html>
