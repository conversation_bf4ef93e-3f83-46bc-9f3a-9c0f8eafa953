#!/usr/bin/env python
"""
Script pour créer des demandes de match de test
"""
import os
import sys
import django
from datetime import datetime, timedelta

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'tennis_manager.settings')
django.setup()

from django.contrib.auth import get_user_model
from reservations.models import MatchRequest

User = get_user_model()

def create_test_users():
    """Créer des utilisateurs de test"""
    print("👥 Création d'utilisateurs de test...")
    
    test_users = [
        {
            'username': 'player1',
            'email': '<EMAIL>',
            'first_name': '<PERSON>',
            'last_name': '<PERSON>',
            'role': 'joueur'
        },
        {
            'username': 'player2',
            'email': '<EMAIL>',
            'first_name': '<PERSON>',
            'last_name': '<PERSON>',
            'role': 'joueur'
        },
        {
            'username': 'player3',
            'email': '<EMAIL>',
            'first_name': '<PERSON>',
            'last_name': '<PERSON>',
            'role': 'joueur'
        },
        {
            'username': 'player4',
            'email': '<EMAIL>',
            'first_name': 'Emma',
            'last_name': 'Wilson',
            'role': 'joueur'
        }
    ]
    
    created_users = []
    for user_data in test_users:
        user, created = User.objects.get_or_create(
            username=user_data['username'],
            defaults=user_data
        )
        
        if created:
            user.set_password('password123')
            user.save()
            print(f"   ✅ Utilisateur créé: {user.username} ({user.first_name} {user.last_name})")
        else:
            print(f"   ✅ Utilisateur existe: {user.username} ({user.first_name} {user.last_name})")
        
        created_users.append(user)
    
    return created_users

def create_test_match_requests():
    print("🎾 Création de demandes de match de test...")
    
    # Get test users
    users = create_test_users()
    
    if len(users) < 4:
        print("❌ Pas assez d'utilisateurs pour créer les demandes de match")
        return []
    
    # Delete existing match requests to start fresh
    MatchRequest.objects.all().delete()
    print("🗑️ Demandes de match existantes supprimées")
    
    # Create test match requests
    match_requests_data = [
        {
            'creator': users[0],  # John Smith
            'match_type': 'friendly',
            'date': datetime.now().date() + timedelta(days=3),
            'time': datetime.strptime('14:00', '%H:%M').time(),
            'location': 'Court A - Tennis Club Elite',
            'notes': 'Looking for a fun and relaxed match. All skill levels welcome!',
            'status': 'open'
        },
        {
            'creator': users[1],  # Sarah Johnson
            'match_type': 'competitive',
            'date': datetime.now().date() + timedelta(days=5),
            'time': datetime.strptime('10:00', '%H:%M').time(),
            'location': 'Court Central',
            'notes': 'Seeking a competitive match. Intermediate to advanced players preferred.',
            'status': 'open'
        },
        {
            'creator': users[2],  # Mike Davis
            'match_type': 'practice',
            'date': datetime.now().date() + timedelta(days=2),
            'time': datetime.strptime('16:30', '%H:%M').time(),
            'location': 'Practice Court B',
            'notes': 'Practice session focused on improving serve and volley techniques.',
            'status': 'open'
        },
        {
            'creator': users[0],  # John Smith (another request)
            'match_type': 'friendly',
            'date': datetime.now().date() + timedelta(days=7),
            'time': datetime.strptime('09:00', '%H:%M').time(),
            'location': 'Court A',
            'notes': 'Early morning match before work. Coffee afterwards!',
            'status': 'open'
        },
        {
            'creator': users[3],  # Emma Wilson
            'match_type': 'competitive',
            'date': datetime.now().date() + timedelta(days=4),
            'time': datetime.strptime('18:00', '%H:%M').time(),
            'location': 'Court Central - Evening Session',
            'notes': 'Evening competitive match. Looking for a challenging opponent!',
            'status': 'open'
        },
        {
            'creator': users[1],  # Sarah Johnson (practice)
            'match_type': 'practice',
            'date': datetime.now().date() + timedelta(days=6),
            'time': datetime.strptime('11:30', '%H:%M').time(),
            'location': 'Practice Court A',
            'notes': 'Working on backhand technique. Partner needed for drills.',
            'status': 'open'
        }
    ]
    
    created_requests = []
    for request_data in match_requests_data:
        match_request = MatchRequest.objects.create(**request_data)
        created_requests.append(match_request)
        
        print(f"✅ Demande créée: {match_request.creator.first_name} {match_request.creator.last_name}")
        print(f"   📅 {match_request.get_match_type_display()} - {match_request.date} à {match_request.time}")
        print(f"   📍 {match_request.location}")
        print(f"   📝 {match_request.notes}")
        print()
    
    print(f"🎉 {len(created_requests)} demandes de match créées avec succès!")
    return created_requests

def show_match_requests_info():
    print("\n📊 RÉSUMÉ DES DEMANDES DE MATCH:")
    print("="*50)
    
    match_requests = MatchRequest.objects.all().order_by('date', 'time')
    
    for i, request in enumerate(match_requests, 1):
        print(f"{i}. {request.creator.first_name} {request.creator.last_name}")
        print(f"   Type: {request.get_match_type_display()}")
        print(f"   Date: {request.date.strftime('%Y-%m-%d')} à {request.time.strftime('%H:%M')}")
        print(f"   Lieu: {request.location}")
        print(f"   Notes: {request.notes}")
        print(f"   Status: {request.get_status_display()}")
        print()
    
    print("🎯 COMPTES DE TEST CRÉÉS:")
    print("="*30)
    users = User.objects.filter(role='joueur').order_by('username')
    for user in users:
        print(f"   👤 {user.username} - {user.first_name} {user.last_name}")
        print(f"      Email: {user.email}")
        print(f"      Mot de passe: password123")
        print()
    
    print("🎯 INSTRUCTIONS DE TEST:")
    print("="*25)
    print("1. 🔐 Connectez-vous avec un des comptes:")
    print("   - player1 / password123 (John Smith)")
    print("   - player2 / password123 (Sarah Johnson)")
    print("   - player3 / password123 (Mike Davis)")
    print("   - player4 / password123 (Emma Wilson)")
    
    print("\n2. 🏠 Allez au dashboard joueur:")
    print("   - URL: http://127.0.0.1:8000/auth/joueur-dashboard/")
    
    print("\n3. 🎾 Testez les fonctionnalités:")
    print("   - Cliquez sur 'Find Matches'")
    print("   - Voyez les demandes des autres joueurs")
    print("   - Créez votre propre demande")
    print("   - Rejoignez une demande d'un autre joueur")
    
    print("\n4. 🔄 Testez avec différents comptes:")
    print("   - Déconnectez-vous et connectez-vous avec un autre compte")
    print("   - Voyez les demandes depuis une autre perspective")
    print("   - Testez l'interaction entre joueurs")

if __name__ == '__main__':
    print("🎾 TENNIS MANAGEMENT SYSTEM")
    print("🔧 Création de Demandes de Match de Test")
    print("="*50)
    
    try:
        match_requests = create_test_match_requests()
        show_match_requests_info()
        
        print("\n🚀 DEMANDES DE MATCH PRÊTES!")
        print("="*35)
        print("✅ 6 demandes de match créées")
        print("✅ 4 utilisateurs de test créés")
        print("✅ Types variés: Friendly, Competitive, Practice")
        print("✅ Dates et heures réalistes")
        print("✅ Lieux et notes descriptifs")
        print("✅ Prêt pour les tests d'interaction")
        
        print("\n🎯 FONCTIONNALITÉS À TESTER:")
        print("="*30)
        print("1. 📋 Affichage des demandes disponibles")
        print("2. 🎾 Création de nouvelles demandes")
        print("3. 🤝 Rejoindre les demandes d'autres joueurs")
        print("4. 📊 Mise à jour des compteurs")
        print("5. 🔄 Interaction entre différents comptes")
        print("6. ✅ Validation et gestion d'erreurs")
        
    except Exception as e:
        print(f"❌ Erreur lors de la création: {e}")
        import traceback
        traceback.print_exc()
