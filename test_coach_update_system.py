#!/usr/bin/env python
"""
Test du système de mise à jour des réservations de coach avec sélection de coach
"""
import os
import sys
import django
import requests
from datetime import datetime, timedelta

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'tennis_manager.settings')
django.setup()

from django.contrib.auth import get_user_model
from reservations.models import Coach, ReservationCoach

User = get_user_model()

def test_coach_update_system():
    print("🎾 Test du Système de Mise à Jour des Réservations de Coach")
    print("="*60)
    
    # 1. Login joueur
    print("\n1️⃣ Login Joueur:")
    try:
        login_response = requests.post('http://127.0.0.1:8000/auth/api/login/', json={
            'email': '<EMAIL>',
            'password': 'player123'
        })
        
        if login_response.status_code == 200:
            print("✅ Login joueur réussi")
            access_token = login_response.json().get('access')
            headers = {'Authorization': f'Bearer {access_token}'}
        else:
            print(f"❌ Login joueur échoué: {login_response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Erreur login: {e}")
        return False
    
    # 2. Créer une réservation de coach pour tester la mise à jour
    print("\n2️⃣ Création d'une Réservation de Coach:")
    try:
        # Obtenir la liste des coachs
        coaches_response = requests.get('http://127.0.0.1:8000/res/api/coaches/', headers=headers)
        coaches = coaches_response.json().get('coaches', [])
        
        if len(coaches) < 2:
            print("❌ Besoin d'au moins 2 coachs pour tester le changement de coach")
            return False
        
        # Sélectionner le premier coach pour la réservation initiale
        initial_coach = coaches[0]
        alternative_coach = coaches[1]
        
        # Créer une réservation
        tomorrow = (datetime.now() + timedelta(days=1)).strftime('%Y-%m-%d')
        booking_data = {
            'coach_id': initial_coach['id'],
            'date': tomorrow,
            'start_time': '10:00',
            'end_time': '11:00'
        }
        
        booking_response = requests.post('http://127.0.0.1:8000/res/api/coach/book-slot/', 
                                       json=booking_data, headers=headers)
        
        if booking_response.status_code in [200, 201]:
            reservation_data = booking_response.json().get('reservation', {})
            reservation_id = reservation_data.get('id')
            print(f"✅ Réservation créée avec succès!")
            print(f"   ID: {reservation_id}")
            print(f"   Coach initial: {initial_coach['name']}")
            print(f"   Date: {tomorrow}")
            print(f"   Heure: 10:00-11:00")
        else:
            print(f"❌ Erreur création réservation: {booking_response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Erreur création réservation: {e}")
        return False
    
    # 3. Test mise à jour avec changement d'heure seulement
    print("\n3️⃣ Test Mise à Jour - Changement d'Heure:")
    try:
        update_data = {
            'date': tomorrow,
            'start_time': '14:00',
            'end_time': '15:00'
        }
        
        update_response = requests.put(f'http://127.0.0.1:8000/res/api/coach-reservations/{reservation_id}/update/', 
                                     json=update_data, headers=headers)
        
        if update_response.status_code == 200:
            updated_data = update_response.json().get('reservation', {})
            print("✅ Mise à jour d'heure réussie!")
            print(f"   Nouvelle heure: {updated_data.get('start_time')}-{updated_data.get('end_time')}")
            print(f"   Nouveau prix: ${updated_data.get('price')}")
        else:
            print(f"❌ Erreur mise à jour heure: {update_response.status_code}")
            print(f"   Message: {update_response.text}")
            return False
    except Exception as e:
        print(f"❌ Erreur mise à jour heure: {e}")
        return False
    
    # 4. Test mise à jour avec changement de coach
    print("\n4️⃣ Test Mise à Jour - Changement de Coach:")
    try:
        update_data = {
            'date': tomorrow,
            'start_time': '16:00',
            'end_time': '17:00',
            'coach_id': alternative_coach['id']  # Changer de coach
        }
        
        update_response = requests.put(f'http://127.0.0.1:8000/res/api/coach-reservations/{reservation_id}/update/', 
                                     json=update_data, headers=headers)
        
        if update_response.status_code == 200:
            updated_data = update_response.json().get('reservation', {})
            print("✅ Changement de coach réussi!")
            print(f"   Nouveau coach: {updated_data.get('coach_name')}")
            print(f"   Nouvelle heure: {updated_data.get('start_time')}-{updated_data.get('end_time')}")
            print(f"   Nouveau prix: ${updated_data.get('price')}")
            print(f"   Durée: {updated_data.get('duration_hours')} heures")
        else:
            print(f"❌ Erreur changement de coach: {update_response.status_code}")
            print(f"   Message: {update_response.text}")
            return False
    except Exception as e:
        print(f"❌ Erreur changement de coach: {e}")
        return False
    
    # 5. Test interface dashboard
    print("\n5️⃣ Test Interface Dashboard:")
    try:
        dashboard_response = requests.get('http://127.0.0.1:8000/auth/joueur-dashboard/')
        
        if dashboard_response.status_code == 200:
            html_content = dashboard_response.text
            
            # Vérifier que les nouvelles fonctions sont présentes
            checks = [
                ('showCoachUpdateModal function', 'showCoachUpdateModal' in html_content),
                ('selectCoachForUpdate function', 'selectCoachForUpdate' in html_content),
                ('loadCoachAvailabilityForUpdate function', 'loadCoachAvailabilityForUpdate' in html_content),
                ('handleCoachReservationUpdate function', 'handleCoachReservationUpdate' in html_content),
                ('Coach selection in update', 'updateCoachSelect' in html_content),
                ('Coach availability display', 'selectedCoachAvailability' in html_content)
            ]
            
            all_passed = True
            for check_name, check_result in checks:
                if check_result:
                    print(f"   ✅ {check_name}")
                else:
                    print(f"   ❌ {check_name} manquant")
                    all_passed = False
            
            if all_passed:
                print("✅ Interface dashboard complète!")
            else:
                print("⚠️  Certaines fonctionnalités manquent dans l'interface")
        else:
            print(f"❌ Dashboard inaccessible: {dashboard_response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Erreur test dashboard: {e}")
        return False
    
    # 6. Test upcoming reservations
    print("\n6️⃣ Test Upcoming Reservations:")
    try:
        upcoming_response = requests.get('http://127.0.0.1:8000/res/api/player/upcoming-reservations/', headers=headers)
        
        if upcoming_response.status_code == 200:
            upcoming_data = upcoming_response.json()
            upcoming_reservations = upcoming_data.get('upcoming_reservations', [])
            
            # Vérifier que notre réservation mise à jour est présente
            updated_reservation_found = False
            for reservation in upcoming_reservations:
                if reservation.get('type') == 'coach' and reservation.get('id') == reservation_id:
                    updated_reservation_found = True
                    print(f"✅ Réservation mise à jour trouvée:")
                    print(f"   Titre: {reservation.get('title')}")
                    print(f"   Coach: {reservation.get('coach_name')}")
                    print(f"   Date: {reservation.get('date')}")
                    print(f"   Heure: {reservation.get('start_time')}-{reservation.get('end_time')}")
                    break
            
            if not updated_reservation_found:
                print("⚠️  Réservation mise à jour non trouvée")
        else:
            print(f"❌ Erreur upcoming reservations: {upcoming_response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Erreur upcoming reservations: {e}")
        return False
    
    # 7. Nettoyage - Supprimer la réservation de test
    print("\n7️⃣ Nettoyage:")
    try:
        delete_response = requests.delete(f'http://127.0.0.1:8000/res/api/coach-reservations/{reservation_id}/cancel/', 
                                        headers=headers)
        
        if delete_response.status_code == 200:
            print("✅ Réservation de test supprimée")
        else:
            print(f"⚠️  Erreur suppression réservation: {delete_response.status_code}")
    except Exception as e:
        print(f"⚠️  Erreur nettoyage: {e}")
    
    print("\n🎉 RÉSUMÉ DU TEST:")
    print("✅ Création de réservation de coach")
    print("✅ Mise à jour avec changement d'heure")
    print("✅ Mise à jour avec changement de coach")
    print("✅ Interface dashboard avec sélection de coach")
    print("✅ Affichage dans upcoming reservations")
    print("✅ API backend supportant changement de coach")
    
    return True

if __name__ == '__main__':
    success = test_coach_update_system()
    
    if success:
        print("\n🚀 SYSTÈME DE MISE À JOUR AVEC SÉLECTION DE COACH FONCTIONNEL!")
        print("\n🎯 Instructions pour tester manuellement:")
        print("1. Aller sur: http://127.0.0.1:8000/auth/login/")
        print("2. Se connecter: <EMAIL> / player123")
        print("3. Créer une réservation de coach")
        print("4. Dans Upcoming Reservations, cliquer sur le bouton Update (✏️)")
        print("5. Voir la liste des coachs disponibles")
        print("6. Sélectionner un nouveau coach et voir sa disponibilité")
        print("7. Modifier la date/heure et confirmer")
        print("8. Vérifier que la réservation est mise à jour avec le nouveau coach")
    else:
        print("\n⚠️  Des problèmes ont été détectés dans le système.")
