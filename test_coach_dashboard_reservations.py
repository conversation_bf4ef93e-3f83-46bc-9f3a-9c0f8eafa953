#!/usr/bin/env python
"""
Test du dashboard coach pour afficher les réservations des joueurs
"""
import requests
import json

def test_coach_dashboard_reservations():
    print("🎾 Test Dashboard Coach - Réservations des Joueurs")
    print("="*50)
    
    # 1. Test login coach
    print("\n1️⃣ Test Login Coach:")
    try:
        login_response = requests.post('http://127.0.0.1:8000/auth/api/login/', json={
            'email': '<EMAIL>',
            'password': 'coach123'
        })
        
        if login_response.status_code == 200:
            print("✅ Login coach réussi")
            access_token = login_response.json().get('access')
            headers = {'Authorization': f'Bearer {access_token}'}
        else:
            print(f"❌ Login coach échoué: {login_response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Erreur login coach: {e}")
        return False
    
    # 2. Test API Coach Reservations
    print("\n2️⃣ Test API Coach Reservations:")
    try:
        reservations_response = requests.get('http://127.0.0.1:8000/res/api/coach/reservations/', headers=headers)
        
        if reservations_response.status_code == 200:
            reservations_data = reservations_response.json()
            coach_name = reservations_data.get('coach_name')
            total_reservations = reservations_data.get('total_reservations', 0)
            upcoming_reservations = reservations_data.get('upcoming_reservations', [])
            past_reservations = reservations_data.get('past_reservations', [])
            upcoming_count = reservations_data.get('upcoming_count', 0)
            past_count = reservations_data.get('past_count', 0)
            
            print(f"✅ API Coach Reservations fonctionnelle")
            print(f"   👨‍🏫 Coach: {coach_name}")
            print(f"   📊 {total_reservations} réservations au total")
            print(f"   📅 {upcoming_count} réservations à venir")
            print(f"   📋 {past_count} réservations passées")
            
            # Afficher quelques exemples de réservations
            if len(upcoming_reservations) > 0:
                print(f"   🔍 Exemples de réservations à venir:")
                for i, reservation in enumerate(upcoming_reservations[:3]):
                    print(f"      {i+1}. {reservation['player_name']} - {reservation['date']} {reservation['start_time']}-{reservation['end_time']} (${reservation['total_price']})")
            
            if len(past_reservations) > 0:
                print(f"   🔍 Exemples de réservations passées:")
                for i, reservation in enumerate(past_reservations[:2]):
                    print(f"      {i+1}. {reservation['player_name']} - {reservation['date']} {reservation['start_time']}-{reservation['end_time']} (${reservation['total_price']})")
                
        else:
            print(f"❌ Erreur API Coach Reservations: {reservations_response.status_code}")
            print(f"   Message: {reservations_response.text}")
            return False
    except Exception as e:
        print(f"❌ Erreur test API Coach Reservations: {e}")
        return False
    
    # 3. Test API Coach Dashboard Stats
    print("\n3️⃣ Test API Coach Dashboard Stats:")
    try:
        stats_response = requests.get('http://127.0.0.1:8000/res/api/coach/dashboard-stats/', headers=headers)
        
        if stats_response.status_code == 200:
            stats_data = stats_response.json()
            coach_info = stats_data.get('coach_info', {})
            stats = stats_data.get('stats', {})
            earnings = stats_data.get('earnings', {})
            today_schedule = stats_data.get('today_schedule', [])
            schedule_stats = stats_data.get('schedule_stats', {})
            
            print(f"✅ API Coach Dashboard Stats fonctionnelle")
            print(f"   👨‍🏫 Coach: {coach_info.get('name')} - ${coach_info.get('price_per_hour')}/hr")
            print(f"   📊 Sessions: {stats.get('today_sessions')} aujourd'hui, {stats.get('week_sessions')} cette semaine")
            print(f"   💰 Gains: ${earnings.get('today'):.2f} aujourd'hui, ${earnings.get('total'):.2f} au total")
            print(f"   📅 Horaires aujourd'hui: {schedule_stats.get('total_slots')} créneaux ({schedule_stats.get('booked_slots')} réservés)")
            
            # Afficher l'horaire d'aujourd'hui
            if len(today_schedule) > 0:
                print(f"   🔍 Horaires d'aujourd'hui:")
                for slot in today_schedule:
                    status = "Réservé" if slot['is_booked'] else "Disponible"
                    booking_info = ""
                    if slot['is_booked'] and slot['booking_details']:
                        booking_info = f" - {slot['booking_details']['player_name']} (${slot['booking_details']['price']})"
                    print(f"      ⏰ {slot['start_time']}-{slot['end_time']}: {status}{booking_info}")
            else:
                print(f"   ℹ️  Aucun horaire défini pour aujourd'hui")
                
        else:
            print(f"❌ Erreur API Coach Dashboard Stats: {stats_response.status_code}")
            print(f"   Message: {stats_response.text}")
            return False
    except Exception as e:
        print(f"❌ Erreur test API Coach Dashboard Stats: {e}")
        return False
    
    # 4. Test Interface Coach Dashboard
    print("\n4️⃣ Test Interface Coach Dashboard:")
    try:
        dashboard_response = requests.get('http://127.0.0.1:8000/auth/coach-dashboard/')
        
        if dashboard_response.status_code == 200:
            html_content = dashboard_response.text
            
            # Vérifier les éléments de l'interface
            checks = [
                ("Reservations Table", "reservationsTableBody" in html_content),
                ("Table Headers Updated", "Email" in html_content and "Price" in html_content),
                ("Load My Reservations Function", "loadMyReservations" in html_content),
                ("Coach Reservations API Call", "/res/api/coach/reservations/" in html_content),
                ("Dashboard Stats API Call", "/res/api/coach/dashboard-stats/" in html_content),
                ("Mark Complete Function", "markComplete" in html_content),
                ("Add Notes Function", "addNotes" in html_content),
                ("Upcoming/Past Sections", "upcoming_reservations" in html_content),
                ("Player Email Display", "player_email" in html_content),
                ("Total Price Display", "total_price" in html_content)
            ]
            
            all_passed = True
            for check_name, check_result in checks:
                if check_result:
                    print(f"   ✅ {check_name}")
                else:
                    print(f"   ❌ {check_name} manquant")
                    all_passed = False
            
            if all_passed:
                print("✅ Interface Coach Dashboard complète!")
            else:
                print("⚠️  Certains éléments manquent dans l'interface")
        else:
            print(f"❌ Dashboard coach inaccessible: {dashboard_response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Erreur test interface coach: {e}")
        return False
    
    return True

def show_manual_test_instructions():
    print("\n🎯 INSTRUCTIONS POUR TEST MANUEL:")
    print("="*40)
    print("1. 🌐 Aller sur: http://127.0.0.1:8000/auth/login/")
    print("2. 🔑 Se connecter: <EMAIL> / coach123")
    print("3. 📊 Observer le Dashboard Coach")
    print("4. 🔍 Vérifier la section 'My Reservations':")
    print("   - Tableau avec colonnes: Student, Email, Date, Time, Price, Actions")
    print("   - Section 'Upcoming Sessions' avec réservations futures")
    print("   - Section 'Past Sessions' avec historique")
    print("   - Informations complètes des joueurs (nom, email)")
    print("   - Prix de chaque session")
    print("   - Boutons d'action (Complete, Notes)")
    print("5. 📈 Vérifier les statistiques du dashboard:")
    print("   - Sessions d'aujourd'hui et de la semaine")
    print("   - Gains totaux et quotidiens")
    print("   - Horaires d'aujourd'hui avec statut")
    
    print("\n🎨 FONCTIONNALITÉS À TESTER:")
    print("="*30)
    print("✓ Section My Reservations:")
    print("  - Affichage des réservations des joueurs")
    print("  - Séparation upcoming/past sessions")
    print("  - Informations complètes (nom, email, prix)")
    print("  - Actions disponibles sur chaque réservation")
    print("✓ Dashboard Statistics:")
    print("  - Compteurs de sessions mis à jour")
    print("  - Gains calculés automatiquement")
    print("  - Horaires d'aujourd'hui avec réservations")
    print("✓ Interface Améliorée:")
    print("  - Tableau avec colonnes supplémentaires")
    print("  - Messages informatifs si aucune réservation")
    print("  - Chargement avec spinners")

if __name__ == '__main__':
    print("🎾 TENNIS MANAGEMENT SYSTEM")
    print("🔧 Test Dashboard Coach - Réservations des Joueurs")
    print("="*55)
    
    success = test_coach_dashboard_reservations()
    
    if success:
        print("\n🚀 DASHBOARD COACH RÉSERVATIONS FONCTIONNEL!")
        print("="*45)
        print("✅ API Coach Reservations complète")
        print("✅ API Coach Dashboard Stats complète")
        print("✅ Interface My Reservations mise à jour")
        print("✅ Affichage des joueurs et leurs réservations")
        print("✅ Séparation upcoming/past sessions")
        print("✅ Informations complètes (email, prix)")
        print("✅ Statistiques dashboard mises à jour")
        
        show_manual_test_instructions()
        
        print("\n🎉 FÉLICITATIONS!")
        print("="*20)
        print("Le dashboard coach affiche maintenant:")
        print("- Toutes les réservations des joueurs")
        print("- Informations complètes des sessions")
        print("- Statistiques en temps réel")
        print("- Interface moderne et fonctionnelle")
        
    else:
        print("\n⚠️  Des problèmes ont été détectés.")
        print("Vérifiez les erreurs ci-dessus et corrigez-les.")
