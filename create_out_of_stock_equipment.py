#!/usr/bin/env python
"""
<PERSON>ript to create some out-of-stock equipment for testing
"""
import os
import sys
import django

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'tennis_manager.settings')
django.setup()

from reservations.models import Equipment

def create_out_of_stock_equipment():
    print("🧪 Creating Out-of-Stock Equipment for Testing...")
    
    try:
        # Create some out-of-stock equipment
        out_of_stock_equipment = [
            {
                'name': 'Premium Tennis Racket Pro',
                'type': 'RAQUETTE',
                'brand': 'Babolat',
                'price': 200.00,
                'stock_quantity': 0,
                'description': 'Professional grade tennis racket for advanced players - Currently out of stock',
                'available': False
            },
            {
                'name': 'Professional Tennis Shoes Elite',
                'type': 'CHAUSSURE',
                'brand': 'Adidas',
                'price': 180.00,
                'stock_quantity': 0,
                'description': 'Elite tennis shoes with superior grip - Currently out of stock',
                'available': False
            },
            {
                'name': 'Tournament Tennis Balls (Pack of 4)',
                'type': 'BALLE',
                'brand': 'Wilson',
                'price': 15.00,
                'stock_quantity': 0,
                'description': 'Official tournament tennis balls - Currently out of stock',
                'available': False
            }
        ]
        
        created_equipment = []
        for item_data in out_of_stock_equipment:
            equipment = Equipment.objects.create(**item_data)
            created_equipment.append(equipment)
            print(f"❌ Created OUT OF STOCK: {equipment.name} - ${equipment.price}")
        
        print(f"\n📊 Summary:")
        print(f"   Out-of-Stock Equipment Created: {len(created_equipment)}")
        
        # Show current equipment status
        total_equipment = Equipment.objects.count()
        available_equipment = Equipment.objects.filter(available=True).count()
        out_of_stock_equipment = Equipment.objects.filter(available=False).count()
        
        print(f"\n📋 Current Equipment Status:")
        print(f"   Total Equipment: {total_equipment}")
        print(f"   ✅ Available: {available_equipment}")
        print(f"   ❌ Out of Stock: {out_of_stock_equipment}")
        
        # Show equipment by availability
        print(f"\n🔍 Available Equipment:")
        for equipment in Equipment.objects.filter(available=True)[:3]:
            print(f"   ✅ {equipment.name} - ${equipment.price} ({equipment.stock_quantity} in stock)")
        
        print(f"\n❌ Out of Stock Equipment:")
        for equipment in Equipment.objects.filter(available=False):
            print(f"   ❌ {equipment.name} - ${equipment.price} (0 in stock)")
        
        print(f"\n🎯 Test the coach dashboard at:")
        print(f"   http://127.0.0.1:8000/auth/login/")
        print(f"   Login: <EMAIL> / coach123")
        print(f"   Navigate to Equipment section")
        print(f"   Try requesting both available and out-of-stock items!")
        
        return True
        
    except Exception as e:
        print(f"❌ Error creating out-of-stock equipment: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == '__main__':
    print("🎾 Tennis Management System - Out-of-Stock Equipment Creator")
    print("="*60)
    
    success = create_out_of_stock_equipment()
    
    if success:
        print("\n🚀 Out-of-stock equipment created successfully!")
        print("✅ Coaches can now test requesting both available and out-of-stock items.")
    else:
        print("\n⚠️  Out-of-stock equipment creation failed.")
