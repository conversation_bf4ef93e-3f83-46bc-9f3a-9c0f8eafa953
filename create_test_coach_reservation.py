#!/usr/bin/env python
"""
Créer une réservation de coach pour tester la mise à jour
"""
import os
import sys
import django
from datetime import datetime, timedelta

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'tennis_manager.settings')
django.setup()

from django.contrib.auth import get_user_model
from reservations.models import Coach, ReservationCoach

User = get_user_model()

def create_test_coach_reservation():
    print("🎾 Création d'une Réservation de Coach pour Test")
    print("="*50)
    
    try:
        # Get player user
        player = User.objects.filter(email='<EMAIL>').first()
        if not player:
            print("❌ Joueur <EMAIL> non trouvé")
            return False
        
        # Get coaches
        coaches = Coach.objects.all()[:2]  # Get first 2 coaches
        if len(coaches) < 2:
            print("❌ Besoin d'au moins 2 coachs")
            return False
        
        coach1 = coaches[0]
        coach2 = coaches[1]
        
        # Create reservation for tomorrow
        tomorrow = datetime.now().date() + timedelta(days=1)
        
        # Check if reservation already exists
        from datetime import time
        start_time = time(10, 0)  # 10:00
        end_time = time(11, 0)    # 11:00

        existing = ReservationCoach.objects.filter(
            user=player,
            coach=coach1,
            date=tomorrow,
            start_time=start_time
        ).first()

        if existing:
            print(f"✅ Réservation existante trouvée: ID {existing.id}")
            reservation = existing
        else:
            # Create new reservation
            reservation = ReservationCoach.objects.create(
                user=player,
                coach=coach1,
                date=tomorrow,
                start_time=start_time,
                end_time=end_time,
                total_price=coach1.price_per_hour
            )
            print(f"✅ Nouvelle réservation créée: ID {reservation.id}")
        
        print(f"📋 Détails de la réservation:")
        print(f"   Joueur: {player.username} ({player.email})")
        print(f"   Coach: {coach1.name}")
        print(f"   Date: {reservation.date}")
        print(f"   Heure: {reservation.start_time} - {reservation.end_time}")
        print(f"   Prix: ${reservation.total_price}")
        
        print(f"\n🔄 Pour tester la mise à jour:")
        print(f"   1. Se connecter avec: <EMAIL> / player123")
        print(f"   2. Aller dans Dashboard ou Upcoming Reservations")
        print(f"   3. Cliquer sur Update (✏️) pour la réservation ID {reservation.id}")
        print(f"   4. Tester le changement vers le coach: {coach2.name}")
        print(f"   5. Modifier l'heure et confirmer")
        
        return True
        
    except Exception as e:
        print(f"❌ Erreur: {e}")
        return False

def show_all_reservations():
    print("\n📋 Toutes les Réservations de Coach:")
    print("="*35)
    
    reservations = ReservationCoach.objects.all().order_by('-date', '-start_time')
    
    if not reservations:
        print("Aucune réservation de coach trouvée")
        return
    
    for reservation in reservations:
        status = "À venir" if reservation.date >= datetime.now().date() else "Passée"
        print(f"ID {reservation.id}: {reservation.user.username} -> {reservation.coach.name}")
        print(f"   Date: {reservation.date} {reservation.start_time}-{reservation.end_time}")
        print(f"   Prix: ${reservation.total_price} | Status: {status}")
        print()

if __name__ == '__main__':
    success = create_test_coach_reservation()
    
    if success:
        show_all_reservations()
        
        print("\n🎯 PRÊT POUR LE TEST!")
        print("="*25)
        print("1. Aller sur: http://127.0.0.1:8000/auth/login/")
        print("2. Se connecter: <EMAIL> / player123")
        print("3. Tester la mise à jour de réservation de coach!")
        
        print("\n✨ FONCTIONNALITÉS À TESTER:")
        print("="*30)
        print("🔄 Modal de mise à jour avec:")
        print("  ✓ Liste des coachs (colonne droite)")
        print("  ✓ Sélection de coach (dropdown)")
        print("  ✓ Disponibilité du coach sélectionné")
        print("  ✓ Modification date/heure")
        print("  ✓ Changement de coach")
        print("  ✓ Recalcul automatique du prix")
        print("  ✓ Validation des conflits")
    else:
        print("\n❌ Échec de la création de réservation de test")
