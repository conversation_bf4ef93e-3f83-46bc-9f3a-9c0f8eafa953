#!/usr/bin/env python
"""
Script to test all models and verify database table mappings
"""
import os
import sys
import django

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'tennis_manager.settings')
django.setup()

from django.contrib.auth import get_user_model
from reservations.models import (
    Coach, Terrain, Reservation, ReservationCoach, Schedule, ScheduleSlot,
    Equipment, EquipmentOrder, Subscription, Tournament, TournamentRegistration,
    Notification, Payment
)

User = get_user_model()

def test_models():
    print("🧪 Testing all models and database table mappings...")
    
    models_to_test = [
        ('User', User),
        ('Coach', Coach),
        ('Terrain', Terrain),
        ('Reservation', Reservation),
        ('ReservationCoach', ReservationCoach),
        ('Schedule', Schedule),
        ('ScheduleSlot', ScheduleSlot),
        ('Equipment', Equipment),
        ('EquipmentOrder', EquipmentOrder),
        ('Subscription', Subscription),
        ('Tournament', Tournament),
        ('TournamentRegistration', TournamentRegistration),
        ('Notification', Notification),
        ('Payment', Payment),
    ]
    
    results = []
    
    for model_name, model_class in models_to_test:
        try:
            # Test basic query
            count = model_class.objects.count()
            table_name = model_class._meta.db_table
            results.append({
                'model': model_name,
                'status': '✅ OK',
                'count': count,
                'table': table_name,
                'error': None
            })
            print(f"✅ {model_name:20} | Table: {table_name:25} | Count: {count}")
            
        except Exception as e:
            results.append({
                'model': model_name,
                'status': '❌ ERROR',
                'count': 0,
                'table': getattr(model_class._meta, 'db_table', 'default'),
                'error': str(e)
            })
            print(f"❌ {model_name:20} | Error: {str(e)[:50]}...")
    
    print("\n" + "="*80)
    print("📊 SUMMARY:")
    print("="*80)
    
    success_count = len([r for r in results if r['status'] == '✅ OK'])
    error_count = len([r for r in results if r['status'] == '❌ ERROR'])
    
    print(f"✅ Working models: {success_count}")
    print(f"❌ Broken models:  {error_count}")
    
    if error_count > 0:
        print("\n🔧 ERRORS TO FIX:")
        for result in results:
            if result['status'] == '❌ ERROR':
                print(f"   ❌ {result['model']} (Table: {result['table']})")
                print(f"      Error: {result['error']}")
    
    print("\n📋 DATABASE TABLES:")
    from django.db import connection
    cursor = connection.cursor()
    cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
    tables = [row[0] for row in cursor.fetchall()]
    
    for table in sorted(tables):
        if not table.startswith('django_') and not table.startswith('auth_') and not table.startswith('sqlite_'):
            print(f"   📁 {table}")
    
    return error_count == 0

if __name__ == '__main__':
    success = test_models()
    if success:
        print("\n🎉 All models are working correctly!")
        print("🚀 The tennis management system is ready!")
    else:
        print("\n⚠️  Some models need fixing.")
        print("🔧 Check the errors above and fix the table mappings.")
