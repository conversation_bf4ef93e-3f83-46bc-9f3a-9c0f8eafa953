{% load static %}
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Messages - Elite Sports Club</title>
    
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.0/font/bootstrap-icons.css" rel="stylesheet">
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <style>
        :root {
            --primary-color: #8b5cf6;
            --secondary-color: #06b6d4;
            --accent-color: #10b981;
            --warning-color: #f59e0b;
            --danger-color: #ef4444;
            --gray-50: #f9fafb;
            --gray-100: #f3f4f6;
            --gray-200: #e5e7eb;
            --gray-600: #4b5563;
            --gray-700: #374151;
            --gray-800: #1f2937;
        }

        body {
            font-family: 'Inter', sans-serif;
            background: linear-gradient(135deg, #faf5ff 0%, #f3e8ff 100%);
            color: var(--gray-700);
            padding-top: 80px;
            height: 100vh;
            overflow: hidden;
        }

        .messaging-container {
            height: calc(100vh - 80px);
            display: flex;
            background: white;
            border-radius: 16px;
            margin: 1rem;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
            border: 1px solid var(--gray-100);
            overflow: hidden;
        }

        .conversations-sidebar {
            width: 350px;
            border-right: 1px solid var(--gray-200);
            display: flex;
            flex-direction: column;
        }

        .sidebar-header {
            padding: 1.5rem;
            border-bottom: 1px solid var(--gray-200);
            background: var(--gray-50);
        }

        .sidebar-title {
            font-size: 1.25rem;
            font-weight: 600;
            color: var(--gray-800);
            margin: 0;
        }

        .search-container {
            position: relative;
            margin-top: 1rem;
        }

        .search-input {
            width: 100%;
            padding: 0.75rem 1rem 0.75rem 2.5rem;
            border: 2px solid var(--gray-200);
            border-radius: 12px;
            font-size: 0.875rem;
            transition: all 0.2s ease;
        }

        .search-input:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(139, 92, 246, 0.1);
            outline: none;
        }

        .search-icon {
            position: absolute;
            left: 0.75rem;
            top: 50%;
            transform: translateY(-50%);
            color: var(--gray-600);
        }

        .conversations-list {
            flex: 1;
            overflow-y: auto;
        }

        .conversation-item {
            display: flex;
            align-items: center;
            gap: 1rem;
            padding: 1rem 1.5rem;
            cursor: pointer;
            transition: all 0.2s ease;
            border-bottom: 1px solid var(--gray-100);
        }

        .conversation-item:hover {
            background: var(--gray-50);
        }

        .conversation-item.active {
            background: var(--primary-color);
            color: white;
        }

        .conversation-avatar {
            width: 48px;
            height: 48px;
            border-radius: 50%;
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: 600;
            font-size: 1.1rem;
            flex-shrink: 0;
        }

        .conversation-info {
            flex: 1;
            min-width: 0;
        }

        .conversation-name {
            font-weight: 600;
            margin-bottom: 0.25rem;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        .conversation-preview {
            font-size: 0.875rem;
            opacity: 0.8;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        .conversation-meta {
            display: flex;
            flex-direction: column;
            align-items: flex-end;
            gap: 0.25rem;
        }

        .conversation-time {
            font-size: 0.75rem;
            opacity: 0.7;
        }

        .unread-badge {
            background: var(--danger-color);
            color: white;
            border-radius: 50%;
            width: 20px;
            height: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 0.75rem;
            font-weight: 600;
        }

        .chat-area {
            flex: 1;
            display: flex;
            flex-direction: column;
        }

        .chat-header {
            padding: 1.5rem;
            border-bottom: 1px solid var(--gray-200);
            background: var(--gray-50);
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .chat-avatar {
            width: 48px;
            height: 48px;
            border-radius: 50%;
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: 600;
            font-size: 1.1rem;
        }

        .chat-info h3 {
            margin: 0;
            font-size: 1.1rem;
            font-weight: 600;
            color: var(--gray-800);
        }

        .chat-role {
            font-size: 0.875rem;
            color: var(--gray-600);
        }

        .messages-container {
            flex: 1;
            overflow-y: auto;
            padding: 1rem;
            display: flex;
            flex-direction: column;
            gap: 1rem;
        }

        .message {
            display: flex;
            gap: 0.75rem;
            max-width: 70%;
        }

        .message.sent {
            align-self: flex-end;
            flex-direction: row-reverse;
        }

        .message-avatar {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            background: var(--gray-300);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: 600;
            font-size: 0.875rem;
            flex-shrink: 0;
        }

        .message.sent .message-avatar {
            background: var(--primary-color);
        }

        .message-content {
            flex: 1;
        }

        .message-bubble {
            background: var(--gray-100);
            padding: 0.75rem 1rem;
            border-radius: 16px;
            margin-bottom: 0.25rem;
            word-wrap: break-word;
        }

        .message.sent .message-bubble {
            background: var(--primary-color);
            color: white;
        }

        .message-time {
            font-size: 0.75rem;
            color: var(--gray-500);
            text-align: right;
        }

        .message.sent .message-time {
            text-align: left;
        }

        .message-input-area {
            padding: 1rem 1.5rem;
            border-top: 1px solid var(--gray-200);
            background: white;
        }

        .message-input-container {
            display: flex;
            gap: 0.75rem;
            align-items: flex-end;
        }

        .message-input {
            flex: 1;
            border: 2px solid var(--gray-200);
            border-radius: 12px;
            padding: 0.75rem 1rem;
            resize: none;
            max-height: 100px;
            font-family: inherit;
            transition: all 0.2s ease;
        }

        .message-input:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(139, 92, 246, 0.1);
            outline: none;
        }

        .send-btn {
            width: 44px;
            height: 44px;
            background: var(--primary-color);
            color: white;
            border: none;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .send-btn:hover {
            background: #7c3aed;
            transform: scale(1.05);
        }

        .send-btn:disabled {
            background: var(--gray-300);
            cursor: not-allowed;
            transform: none;
        }

        .empty-state {
            flex: 1;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            text-align: center;
            padding: 2rem;
        }

        .empty-icon {
            font-size: 4rem;
            color: var(--gray-300);
            margin-bottom: 1rem;
        }

        .new-conversation-btn {
            background: var(--primary-color);
            color: white;
            border: none;
            padding: 0.75rem 1.5rem;
            border-radius: 12px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s ease;
            margin-top: 1rem;
        }

        .new-conversation-btn:hover {
            background: #7c3aed;
        }

        /* Responsive */
        @media (max-width: 768px) {
            .messaging-container {
                margin: 0.5rem;
                border-radius: 8px;
            }
            
            .conversations-sidebar {
                width: 100%;
                position: absolute;
                z-index: 10;
                background: white;
                transform: translateX(-100%);
                transition: transform 0.3s ease;
            }
            
            .conversations-sidebar.show {
                transform: translateX(0);
            }
            
            .chat-area {
                width: 100%;
            }
        }

        /* Animation */
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .message {
            animation: fadeInUp 0.3s ease-out;
        }
    </style>
</head>
<body>
    <!-- Include Navbar -->
    {% include 'html/navbar.html' %}

    <div class="messaging-container">
        <!-- Conversations Sidebar -->
        <div class="conversations-sidebar">
            <div class="sidebar-header">
                <h2 class="sidebar-title">Messages</h2>
                <div class="search-container">
                    <i class="bi bi-search search-icon"></i>
                    <input type="text" class="search-input" placeholder="Search conversations..." id="searchInput">
                </div>
            </div>
            
            <div class="conversations-list" id="conversationsList">
                <!-- Conversations will be loaded here -->
            </div>
        </div>

        <!-- Chat Area -->
        <div class="chat-area" id="chatArea">
            <div class="empty-state">
                <i class="bi bi-chat-dots empty-icon"></i>
                <h3>Select a conversation</h3>
                <p class="text-muted">Choose a conversation from the sidebar to start messaging</p>
                <button class="new-conversation-btn" onclick="showNewConversationModal()">
                    <i class="bi bi-plus"></i> Start New Conversation
                </button>
            </div>
        </div>
    </div>

    <!-- New Conversation Modal -->
    <div class="modal fade" id="newConversationModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Start New Conversation</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="mb-3">
                        <label class="form-label">Search Users</label>
                        <input type="text" class="form-control" id="userSearchInput" placeholder="Search players, coaches, or admins...">
                    </div>
                    <div id="usersList" class="list-group">
                        <!-- Users will be loaded here -->
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- JavaScript -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        let conversations = [];
        let currentConversation = null;
        let currentMessages = [];
        let availableUsers = [];
        
        // Load conversations from API
        async function loadConversations() {
            try {
                const token = localStorage.getItem('token');
                if (!token) {
                    showLoginMessage();
                    return;
                }
                
                const response = await fetch('/res/api/messaging/conversations/', {
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json'
                    }
                });
                
                if (response.ok) {
                    const data = await response.json();
                    conversations = data.conversations;
                    renderConversations();
                    
                    // Check if there's a conversation ID in URL
                    const urlParams = new URLSearchParams(window.location.search);
                    const conversationId = urlParams.get('conversation');
                    if (conversationId) {
                        selectConversation(parseInt(conversationId));
                    }
                } else {
                    console.error('Failed to load conversations');
                    showLoginMessage();
                }
            } catch (error) {
                console.error('Error loading conversations:', error);
                showLoginMessage();
            }
        }
        
        function showLoginMessage() {
            document.getElementById('conversationsList').innerHTML = `
                <div class="text-center p-4">
                    <i class="bi bi-lock" style="font-size: 3rem; color: var(--gray-400);"></i>
                    <h5 class="mt-3">Please Log In</h5>
                    <p class="text-muted">You need to be logged in to access messages.</p>
                    <a href="/auth/login/" class="btn btn-primary">Log In</a>
                </div>
            `;
        }
        
        function renderConversations() {
            const container = document.getElementById('conversationsList');
            
            if (conversations.length === 0) {
                container.innerHTML = `
                    <div class="text-center p-4">
                        <i class="bi bi-chat-dots" style="font-size: 3rem; color: var(--gray-400);"></i>
                        <h5 class="mt-3">No conversations yet</h5>
                        <p class="text-muted">Start a conversation with other members</p>
                        <button class="new-conversation-btn" onclick="showNewConversationModal()">
                            <i class="bi bi-plus"></i> Start Conversation
                        </button>
                    </div>
                `;
                return;
            }
            
            container.innerHTML = '';
            
            conversations.forEach(conversation => {
                const conversationElement = document.createElement('div');
                conversationElement.className = `conversation-item ${currentConversation === conversation.id ? 'active' : ''}`;
                conversationElement.onclick = () => selectConversation(conversation.id);
                
                const otherParticipant = conversation.other_participant;
                const avatar = otherParticipant ? 
                    otherParticipant.full_name.split(' ').map(n => n[0]).join('').toUpperCase() : 
                    '?';
                
                conversationElement.innerHTML = `
                    <div class="conversation-avatar">${avatar}</div>
                    <div class="conversation-info">
                        <div class="conversation-name">${otherParticipant ? otherParticipant.full_name : 'Unknown User'}</div>
                        <div class="conversation-preview">${conversation.last_message ? conversation.last_message.content : 'No messages yet'}</div>
                    </div>
                    <div class="conversation-meta">
                        <div class="conversation-time">${conversation.last_message ? formatTime(conversation.last_message.timestamp) : ''}</div>
                        ${conversation.unread_count > 0 ? `<div class="unread-badge">${conversation.unread_count}</div>` : ''}
                    </div>
                `;
                
                container.appendChild(conversationElement);
            });
        }
        
        async function selectConversation(conversationId) {
            currentConversation = conversationId;
            const conversation = conversations.find(c => c.id === conversationId);
            
            if (!conversation) return;
            
            // Load messages for this conversation
            await loadMessages(conversationId);
            
            // Update UI
            renderConversations();
            renderChatArea(conversation);
        }
        
        async function loadMessages(conversationId) {
            try {
                const token = localStorage.getItem('token');
                const response = await fetch(`/res/api/messaging/conversations/${conversationId}/messages/`, {
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json'
                    }
                });
                
                if (response.ok) {
                    const data = await response.json();
                    currentMessages = data.messages;
                } else {
                    console.error('Failed to load messages');
                    currentMessages = [];
                }
            } catch (error) {
                console.error('Error loading messages:', error);
                currentMessages = [];
            }
        }
        
        function renderChatArea(conversation) {
            const chatArea = document.getElementById('chatArea');
            const otherParticipant = conversation.other_participant;
            
            if (!otherParticipant) return;
            
            const avatar = otherParticipant.full_name.split(' ').map(n => n[0]).join('').toUpperCase();
            
            chatArea.innerHTML = `
                <div class="chat-header">
                    <div class="chat-avatar">${avatar}</div>
                    <div class="chat-info">
                        <h3>${otherParticipant.full_name}</h3>
                        <div class="chat-role">${otherParticipant.role}</div>
                    </div>
                </div>
                
                <div class="messages-container" id="messagesContainer">
                    ${renderMessages()}
                </div>
                
                <div class="message-input-area">
                    <div class="message-input-container">
                        <textarea class="message-input" placeholder="Type a message..." id="messageInput" rows="1"></textarea>
                        <button class="send-btn" id="sendBtn" onclick="sendMessage()">
                            <i class="bi bi-send"></i>
                        </button>
                    </div>
                </div>
            `;
            
            // Setup message input
            setupMessageInput();
            
            // Scroll to bottom
            scrollToBottom();
        }
        
        function renderMessages() {
            return currentMessages.map(message => {
                const avatar = message.is_mine ? 'You'[0] : message.sender.full_name.split(' ').map(n => n[0]).join('').toUpperCase();
                
                return `
                    <div class="message ${message.is_mine ? 'sent' : 'received'}">
                        <div class="message-avatar">${avatar}</div>
                        <div class="message-content">
                            <div class="message-bubble">${message.content}</div>
                            <div class="message-time">${formatTime(message.timestamp)}</div>
                        </div>
                    </div>
                `;
            }).join('');
        }
        
        function setupMessageInput() {
            const messageInput = document.getElementById('messageInput');
            
            // Auto-resize textarea
            messageInput.addEventListener('input', function() {
                this.style.height = 'auto';
                this.style.height = Math.min(this.scrollHeight, 100) + 'px';
            });
            
            // Send on Enter
            messageInput.addEventListener('keypress', function(e) {
                if (e.key === 'Enter' && !e.shiftKey) {
                    e.preventDefault();
                    sendMessage();
                }
            });
        }
        
        async function sendMessage() {
            const messageInput = document.getElementById('messageInput');
            const content = messageInput.value.trim();
            
            if (!content || !currentConversation) return;
            
            try {
                const token = localStorage.getItem('token');
                const response = await fetch(`/res/api/messaging/conversations/${currentConversation}/messages/`, {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        content: content
                    })
                });
                
                if (response.ok) {
                    const data = await response.json();
                    
                    // Add message to current messages
                    currentMessages.push(data.message_data);
                    
                    // Update messages display
                    const messagesContainer = document.getElementById('messagesContainer');
                    const messageElement = document.createElement('div');
                    messageElement.className = 'message sent';
                    messageElement.innerHTML = `
                        <div class="message-avatar">Y</div>
                        <div class="message-content">
                            <div class="message-bubble">${content}</div>
                            <div class="message-time">${formatTime(data.message_data.timestamp)}</div>
                        </div>
                    `;
                    
                    messagesContainer.appendChild(messageElement);
                    
                    // Clear input
                    messageInput.value = '';
                    messageInput.style.height = 'auto';
                    
                    // Scroll to bottom
                    scrollToBottom();
                    
                    // Reload conversations to update last message
                    loadConversations();
                } else {
                    const error = await response.json();
                    alert(error.error || 'Failed to send message');
                }
            } catch (error) {
                console.error('Error sending message:', error);
                alert('Failed to send message');
            }
        }
        
        function scrollToBottom() {
            const messagesContainer = document.getElementById('messagesContainer');
            if (messagesContainer) {
                messagesContainer.scrollTop = messagesContainer.scrollHeight;
            }
        }
        
        function formatTime(timestamp) {
            const date = new Date(timestamp);
            const now = new Date();
            const diffInHours = (now - date) / (1000 * 60 * 60);
            
            if (diffInHours < 1) {
                return 'Just now';
            } else if (diffInHours < 24) {
                return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
            } else if (diffInHours < 48) {
                return 'Yesterday';
            } else {
                return date.toLocaleDateString();
            }
        }
        
        // New conversation functionality
        async function showNewConversationModal() {
            await loadAvailableUsers();
            const modal = new bootstrap.Modal(document.getElementById('newConversationModal'));
            modal.show();
        }
        
        async function loadAvailableUsers() {
            try {
                const token = localStorage.getItem('token');
                const response = await fetch('/res/api/messaging/users/', {
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json'
                    }
                });
                
                if (response.ok) {
                    const data = await response.json();
                    availableUsers = data.users;
                    renderAvailableUsers();
                } else {
                    console.error('Failed to load users');
                }
            } catch (error) {
                console.error('Error loading users:', error);
            }
        }
        
        function renderAvailableUsers(searchTerm = '') {
            const container = document.getElementById('usersList');
            const filteredUsers = availableUsers.filter(user => 
                user.full_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                user.username.toLowerCase().includes(searchTerm.toLowerCase()) ||
                user.role.toLowerCase().includes(searchTerm.toLowerCase())
            );
            
            container.innerHTML = '';
            
            filteredUsers.forEach(user => {
                const userElement = document.createElement('div');
                userElement.className = 'list-group-item list-group-item-action d-flex align-items-center gap-3';
                userElement.style.cursor = 'pointer';
                userElement.onclick = () => startConversationWithUser(user.id);
                
                const avatar = user.full_name.split(' ').map(n => n[0]).join('').toUpperCase();
                const roleColor = {
                    'coach': 'success',
                    'admin': 'danger',
                    'joueur': 'primary'
                }[user.role] || 'secondary';
                
                userElement.innerHTML = `
                    <div class="conversation-avatar" style="width: 40px; height: 40px; font-size: 0.9rem;">${avatar}</div>
                    <div class="flex-grow-1">
                        <div class="fw-semibold">${user.full_name}</div>
                        <small class="text-muted">@${user.username}</small>
                    </div>
                    <span class="badge bg-${roleColor}">${user.role}</span>
                    ${user.has_conversation ? '<i class="bi bi-chat-fill text-success"></i>' : ''}
                `;
                
                container.appendChild(userElement);
            });
        }
        
        async function startConversationWithUser(userId) {
            try {
                const token = localStorage.getItem('token');
                const response = await fetch('/res/api/messaging/conversations/', {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        user_id: userId
                    })
                });
                
                if (response.ok) {
                    const data = await response.json();
                    
                    // Close modal
                    const modal = bootstrap.Modal.getInstance(document.getElementById('newConversationModal'));
                    modal.hide();
                    
                    // Reload conversations and select the new one
                    await loadConversations();
                    selectConversation(data.conversation_id);
                } else {
                    const error = await response.json();
                    alert(error.error || 'Failed to start conversation');
                }
            } catch (error) {
                console.error('Error starting conversation:', error);
                alert('Failed to start conversation');
            }
        }
        
        // Search functionality
        document.getElementById('searchInput').addEventListener('input', function(e) {
            const searchTerm = e.target.value.toLowerCase();
            const conversationItems = document.querySelectorAll('.conversation-item');
            
            conversationItems.forEach(item => {
                const name = item.querySelector('.conversation-name').textContent.toLowerCase();
                const preview = item.querySelector('.conversation-preview').textContent.toLowerCase();
                
                if (name.includes(searchTerm) || preview.includes(searchTerm)) {
                    item.style.display = 'flex';
                } else {
                    item.style.display = 'none';
                }
            });
        });
        
        // User search in modal
        document.getElementById('userSearchInput').addEventListener('input', function(e) {
            renderAvailableUsers(e.target.value);
        });
        
        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            loadConversations();
            
            // Auto-refresh conversations every 30 seconds
            setInterval(loadConversations, 30000);
        });
    </script>
</body>
</html>
