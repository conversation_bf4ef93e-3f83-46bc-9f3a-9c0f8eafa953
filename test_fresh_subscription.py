#!/usr/bin/env python
"""
Script to test subscription with a fresh user
"""
import os
import sys
import django
from datetime import timedelta

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'tennis_manager.settings')
django.setup()

from django.contrib.auth import get_user_model
from reservations.models import Subscription, Payment, Notification
from django.utils import timezone

User = get_user_model()

def test_fresh_subscription():
    print("🧪 Testing Subscription with Fresh User...")
    
    try:
        # Create a fresh test user
        username = 'test_player_fresh'
        
        # Delete existing test user if exists
        User.objects.filter(username=username).delete()
        
        # Create new user
        user = User.objects.create_user(
            username=username,
            email='<EMAIL>',
            password='test123',
            first_name='Fresh',
            last_name='Player',
            role='player'
        )
        
        print(f"✅ Created fresh user: {user.username} (ID: {user.id})")
        
        # Verify no existing subscription
        existing_sub = Subscription.objects.filter(user=user).first()
        if existing_sub:
            print(f"⚠️  User has existing subscription: {existing_sub}")
            existing_sub.delete()
            print("✅ Deleted existing subscription")
        else:
            print("✅ No existing subscription - perfect for testing")
        
        # Test subscription creation
        print(f"\n🆕 Creating new subscription...")
        
        plan_type = 'premium'
        duration_months = 1
        monthly_price = 49.99
        end_date = timezone.now() + timedelta(days=30 * duration_months)
        
        # Create subscription
        subscription = Subscription.objects.create(
            user=user,
            plan_type=plan_type,
            end_date=end_date,
            monthly_price=monthly_price,
            status='active'
        )
        
        print(f"✅ Created subscription: {subscription}")
        print(f"   ID: {subscription.id}")
        print(f"   Plan: {subscription.plan_type}")
        print(f"   Price: ${subscription.monthly_price}")
        print(f"   Status: {subscription.status}")
        print(f"   Active: {subscription.is_active}")
        print(f"   Start: {subscription.start_date}")
        print(f"   End: {subscription.end_date}")
        
        # Create payment
        payment = Payment.objects.create(
            user=user,
            payment_type='subscription',
            amount=monthly_price * duration_months,
            status='completed',
            transaction_id=f'SUB_{subscription.id}_{int(timezone.now().timestamp())}',
            description=f'{plan_type.title()} subscription for {duration_months} month(s)'
        )
        
        print(f"✅ Created payment: {payment}")
        
        # Create notification
        notification = Notification.objects.create(
            user=user,
            title='Subscription Activated',
            message=f'Your {plan_type.title()} subscription has been activated successfully!',
            notification_type='subscription'
        )
        
        print(f"✅ Created notification: {notification}")
        
        # Test trying to create another subscription (should fail)
        print(f"\n🚫 Testing duplicate subscription creation...")
        try:
            duplicate_sub = Subscription.objects.create(
                user=user,
                plan_type='basic',
                end_date=timezone.now() + timedelta(days=30),
                monthly_price=29.99,
                status='active'
            )
            print(f"❌ ERROR: Duplicate subscription was created: {duplicate_sub}")
        except Exception as e:
            print(f"✅ Correctly prevented duplicate subscription: {e}")
        
        print(f"\n📊 Final Status:")
        print(f"   User: {user.username}")
        print(f"   Subscriptions: {Subscription.objects.filter(user=user).count()}")
        print(f"   Payments: {Payment.objects.filter(user=user).count()}")
        print(f"   Notifications: {Notification.objects.filter(user=user).count()}")
        
        print(f"\n🎯 Test Login Credentials:")
        print(f"   Username: {username}")
        print(f"   Password: test123")
        print(f"   URL: http://127.0.0.1:8000/auth/login/")
        
        print("\n🎉 Fresh subscription test completed successfully!")
        return True
        
    except Exception as e:
        print(f"❌ Error during fresh subscription test: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == '__main__':
    print("🎾 Tennis Management System - Fresh Subscription Test")
    print("="*60)
    
    success = test_fresh_subscription()
    
    if success:
        print("\n🚀 Fresh subscription creation works perfectly!")
        print("✅ The subscription system is functioning correctly.")
        print("⚠️  500 errors are likely from users with existing subscriptions.")
    else:
        print("\n⚠️  Fresh subscription creation failed.")
