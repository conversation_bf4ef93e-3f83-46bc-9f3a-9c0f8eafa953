#!/usr/bin/env python
"""
Script to test dashboard fixes
"""
import os
import sys
import django
import requests

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'tennis_manager.settings')
django.setup()

def test_static_files():
    print("🧪 Testing Static Files...")
    
    try:
        # Test CSS file
        css_response = requests.get('http://127.0.0.1:8000/static/css/style.css')
        print(f"   CSS Status: {css_response.status_code}")
        
        if css_response.status_code == 200:
            print("   ✅ CSS file loads correctly")
        else:
            print("   ❌ CSS file not found")
        
        return css_response.status_code == 200
        
    except Exception as e:
        print(f"❌ Error testing static files: {e}")
        return False

def test_equipment_api():
    print("\n🧪 Testing Equipment API...")
    
    try:
        # Login first
        login_response = requests.post('http://127.0.0.1:8000/auth/api/login/', json={
            'email': '<EMAIL>',
            'password': 'player123'
        })
        
        if login_response.status_code != 200:
            print(f"   ❌ Login failed: {login_response.status_code}")
            return False
        
        access_token = login_response.json().get('access')
        headers = {'Authorization': f'Bearer {access_token}'}
        
        # Test equipment list
        equipment_response = requests.get('http://127.0.0.1:8000/res/api/equipment/', headers=headers)
        print(f"   Equipment List Status: {equipment_response.status_code}")
        
        if equipment_response.status_code == 200:
            equipment_data = equipment_response.json()
            equipment_count = len(equipment_data.get('equipment', []))
            print(f"   ✅ Found {equipment_count} equipment items")
            return True
        else:
            print(f"   ❌ Equipment API failed: {equipment_response.text}")
            return False
        
    except Exception as e:
        print(f"❌ Error testing equipment API: {e}")
        return False

def test_dashboard_access():
    print("\n🧪 Testing Dashboard Access...")
    
    try:
        # Test dashboard page
        dashboard_response = requests.get('http://127.0.0.1:8000/auth/joueur-dashboard/')
        print(f"   Dashboard Status: {dashboard_response.status_code}")
        
        if dashboard_response.status_code == 200:
            print("   ✅ Dashboard loads correctly")
            
            # Check for key elements in the HTML
            html_content = dashboard_response.text
            
            checks = [
                ('Equipment form', 'equipmentSubscriptionForm' in html_content),
                ('Match section', 'joinMatch' in html_content),
                ('Court reservations', 'courtReservationForm' in html_content),
                ('Subscriptions', 'subscriptions' in html_content)
            ]
            
            all_passed = True
            for check_name, check_result in checks:
                if check_result:
                    print(f"   ✅ {check_name} found")
                else:
                    print(f"   ❌ {check_name} missing")
                    all_passed = False
            
            return all_passed
        else:
            print(f"   ❌ Dashboard access failed: {dashboard_response.status_code}")
            return False
        
    except Exception as e:
        print(f"❌ Error testing dashboard access: {e}")
        return False

def check_file_structure():
    print("\n🧪 Checking File Structure...")
    
    files_to_check = [
        'static/css/style.css',
        'static/images/tennis-ball.png',
        'reservations/templates/html/joueur_dashboard.html'
    ]
    
    all_exist = True
    for file_path in files_to_check:
        if os.path.exists(file_path):
            print(f"   ✅ {file_path} exists")
        else:
            print(f"   ❌ {file_path} missing")
            all_exist = False
    
    return all_exist

if __name__ == '__main__':
    print("🎾 Tennis Management System - Dashboard Fixes Test")
    print("="*60)
    
    # Run all tests
    test1 = check_file_structure()
    test2 = test_static_files()
    test3 = test_equipment_api()
    test4 = test_dashboard_access()
    
    if all([test1, test2, test3, test4]):
        print("\n🚀 All dashboard fixes are working!")
        print("✅ Equipment purchases should now work correctly.")
        print("✅ Match joining functionality is implemented.")
        print("✅ JavaScript errors should be resolved.")
        print("✅ Static files are loading properly.")
    else:
        print("\n⚠️  Some issues remain. Check the specific errors above.")
        
    print("\n🎯 Test the dashboard at:")
    print("   http://127.0.0.1:8000/auth/login/")
    print("   Login: <EMAIL> / player123")
