#!/usr/bin/env python
"""
Script to test reservation management (delete and update functionality)
"""
import os
import sys
import django
import requests
from datetime import datetime, timedelta

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'tennis_manager.settings')
django.setup()

from django.contrib.auth import get_user_model
from reservations.models import Reservation, ReservationCoach, Terrain, Coach

User = get_user_model()

def create_test_reservation():
    """Create a test court reservation for testing"""
    print("🧪 Creating Test Court Reservation...")
    
    try:
        # Get a player user
        player = User.objects.filter(role='player').first()
        if not player:
            print("❌ No player user found")
            return None
        
        # Get a terrain
        terrain = Terrain.objects.first()
        if not terrain:
            print("❌ No terrain found")
            return None
        
        # Create a reservation for tomorrow
        tomorrow = datetime.now().date() + timedelta(days=1)
        
        reservation = Reservation.objects.create(
            user=player,
            terrain=terrain,
            date=tomorrow,
            start_time='10:00',
            end_time='11:00'
        )
        
        print(f"✅ Created test reservation: {reservation.id}")
        print(f"   Player: {player.username}")
        print(f"   Terrain: {terrain.name}")
        print(f"   Date: {reservation.date}")
        print(f"   Time: {reservation.start_time} - {reservation.end_time}")
        
        return reservation
        
    except Exception as e:
        print(f"❌ Error creating test reservation: {e}")
        return None

def test_upcoming_reservations_display():
    """Test that upcoming reservations are displayed with buttons"""
    print("\n🧪 Testing Upcoming Reservations Display...")
    
    try:
        # Login as player
        login_response = requests.post('http://127.0.0.1:8000/auth/api/login/', json={
            'email': '<EMAIL>',
            'password': 'player123'
        })
        
        if login_response.status_code != 200:
            print(f"❌ Player login failed: {login_response.status_code}")
            return False
        
        access_token = login_response.json().get('access')
        headers = {'Authorization': f'Bearer {access_token}'}
        
        # Test upcoming reservations API
        upcoming_response = requests.get('http://127.0.0.1:8000/res/api/player/upcoming-reservations/', headers=headers)
        print(f"Upcoming Reservations API Status: {upcoming_response.status_code}")
        
        if upcoming_response.status_code == 200:
            data = upcoming_response.json()
            reservations = data.get('upcoming_reservations', [])
            count = data.get('count', 0)
            
            print(f"✅ Found {count} upcoming reservations")
            
            for reservation in reservations:
                print(f"   📅 {reservation['title']} - {reservation['date']} {reservation['start_time']}-{reservation['end_time']}")
                print(f"      Type: {reservation['type']}, ID: {reservation['id']}, Price: ${reservation['price']}")
            
            return count > 0
        else:
            print(f"❌ Upcoming reservations API failed: {upcoming_response.text}")
            return False
        
    except Exception as e:
        print(f"❌ Error testing upcoming reservations: {e}")
        return False

def test_delete_reservation():
    """Test deleting a reservation"""
    print("\n🧪 Testing Reservation Deletion...")
    
    try:
        # Create a test reservation
        reservation = create_test_reservation()
        if not reservation:
            return False
        
        # Login as the reservation owner
        login_response = requests.post('http://127.0.0.1:8000/auth/api/login/', json={
            'email': reservation.user.email,
            'password': 'player123'
        })
        
        if login_response.status_code != 200:
            print(f"❌ Login failed: {login_response.status_code}")
            return False
        
        access_token = login_response.json().get('access')
        headers = {'Authorization': f'Bearer {access_token}'}
        
        # Test deleting the reservation
        delete_response = requests.delete(f'http://127.0.0.1:8000/res/api/reservations/{reservation.id}/cancel/', headers=headers)
        print(f"Delete Reservation Status: {delete_response.status_code}")
        
        if delete_response.status_code == 200:
            print("✅ Reservation deleted successfully")
            
            # Verify it's actually deleted
            try:
                Reservation.objects.get(id=reservation.id)
                print("❌ Reservation still exists after deletion")
                return False
            except Reservation.DoesNotExist:
                print("✅ Reservation confirmed deleted from database")
                return True
        else:
            print(f"❌ Delete failed: {delete_response.text}")
            return False
        
    except Exception as e:
        print(f"❌ Error testing reservation deletion: {e}")
        return False

def test_update_reservation():
    """Test updating a reservation"""
    print("\n🧪 Testing Reservation Update...")
    
    try:
        # Create a test reservation
        reservation = create_test_reservation()
        if not reservation:
            return False
        
        # Login as the reservation owner
        login_response = requests.post('http://127.0.0.1:8000/auth/api/login/', json={
            'email': reservation.user.email,
            'password': 'player123'
        })
        
        if login_response.status_code != 200:
            print(f"❌ Login failed: {login_response.status_code}")
            return False
        
        access_token = login_response.json().get('access')
        headers = {'Authorization': f'Bearer {access_token}'}
        
        # Test updating the reservation
        new_date = (datetime.now().date() + timedelta(days=2)).strftime('%Y-%m-%d')
        update_data = {
            'date': new_date,
            'start_time': '14:00',
            'end_time': '15:00'
        }
        
        update_response = requests.put(
            f'http://127.0.0.1:8000/res/api/court-reservations/{reservation.id}/update/', 
            json=update_data, 
            headers=headers
        )
        print(f"Update Reservation Status: {update_response.status_code}")
        
        if update_response.status_code == 200:
            print("✅ Reservation updated successfully")
            
            # Verify the update
            updated_reservation = Reservation.objects.get(id=reservation.id)
            print(f"   New date: {updated_reservation.date}")
            print(f"   New time: {updated_reservation.start_time} - {updated_reservation.end_time}")
            
            # Clean up
            updated_reservation.delete()
            
            return True
        else:
            print(f"❌ Update failed: {update_response.text}")
            # Clean up
            reservation.delete()
            return False
        
    except Exception as e:
        print(f"❌ Error testing reservation update: {e}")
        return False

def test_dashboard_access():
    """Test that the dashboard loads with the new buttons"""
    print("\n🧪 Testing Dashboard Access...")
    
    try:
        # Test dashboard page
        dashboard_response = requests.get('http://127.0.0.1:8000/auth/joueur-dashboard/')
        print(f"Player Dashboard Status: {dashboard_response.status_code}")
        
        if dashboard_response.status_code == 200:
            html_content = dashboard_response.text
            
            # Check for the new functions
            checks = [
                ('Delete function', 'deleteReservation' in html_content),
                ('Update function', 'updateReservation' in html_content),
                ('Close modal function', 'closeUpdateModal' in html_content),
                ('Update buttons', 'btn-outline-primary' in html_content and 'bi-pencil-square' in html_content),
                ('Delete buttons', 'btn-outline-danger' in html_content and 'bi-trash' in html_content)
            ]
            
            all_passed = True
            for check_name, check_result in checks:
                if check_result:
                    print(f"   ✅ {check_name} found")
                else:
                    print(f"   ❌ {check_name} missing")
                    all_passed = False
            
            return all_passed
        else:
            print(f"❌ Dashboard access failed: {dashboard_response.status_code}")
            return False
        
    except Exception as e:
        print(f"❌ Error testing dashboard: {e}")
        return False

if __name__ == '__main__':
    print("🎾 Tennis Management System - Reservation Management Test")
    print("="*60)
    
    # Run all tests
    test1 = test_dashboard_access()
    test2 = test_upcoming_reservations_display()
    test3 = test_delete_reservation()
    test4 = test_update_reservation()
    
    if all([test1, test2, test3, test4]):
        print("\n🚀 All reservation management tests passed!")
        print("✅ Players can now delete and update their reservations.")
        print("✅ Update modal works with proper validation.")
        print("✅ Delete confirmation prevents accidental deletions.")
        print("✅ API endpoints handle both court and coach reservations.")
    else:
        print("\n⚠️  Some issues found. Check the specific errors above.")
        
    print("\n🎯 Test the reservation management at:")
    print("   http://127.0.0.1:8000/auth/login/")
    print("   Login: <EMAIL> / player123")
    print("   Make a reservation, then try updating and deleting it!")
