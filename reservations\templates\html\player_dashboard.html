{% load static %}
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Player Dashboard - Elite Sports Club</title>
    
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.0/font/bootstrap-icons.css" rel="stylesheet">
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    
    <style>
        :root {
            --primary-color: #06b6d4;
            --secondary-color: #10b981;
            --accent-color: #f59e0b;
            --danger-color: #ef4444;
            --gray-50: #f9fafb;
            --gray-100: #f3f4f6;
            --gray-200: #e5e7eb;
            --gray-600: #4b5563;
            --gray-700: #374151;
            --gray-800: #1f2937;
        }

        body {
            font-family: 'Inter', sans-serif;
            background: linear-gradient(135deg, #ecfeff 0%, #cffafe 100%);
            color: var(--gray-700);
            padding-top: 80px;
        }

        .dashboard-container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 2rem;
        }

        .welcome-section {
            background: white;
            border-radius: 16px;
            padding: 2rem;
            margin-bottom: 2rem;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
            border: 1px solid var(--gray-100);
            position: relative;
            overflow: hidden;
        }

        .welcome-section::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
        }

        .welcome-content {
            display: flex;
            align-items: center;
            gap: 2rem;
        }

        .player-avatar {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 2rem;
            font-weight: 700;
            flex-shrink: 0;
        }

        .welcome-text h1 {
            font-family: 'Poppins', sans-serif;
            font-size: 2rem;
            font-weight: 700;
            color: var(--gray-800);
            margin: 0 0 0.5rem 0;
        }

        .welcome-subtitle {
            color: var(--gray-600);
            margin: 0;
        }

        .quick-actions {
            display: flex;
            gap: 1rem;
            margin-top: 1.5rem;
        }

        .quick-action-btn {
            background: var(--primary-color);
            color: white;
            border: none;
            padding: 0.75rem 1.5rem;
            border-radius: 12px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s ease;
            text-decoration: none;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .quick-action-btn:hover {
            background: #0891b2;
            color: white;
            transform: translateY(-2px);
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
        }

        .stat-card {
            background: white;
            border-radius: 16px;
            padding: 1.5rem;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
            border: 1px solid var(--gray-100);
            transition: all 0.3s ease;
        }

        .stat-card:hover {
            transform: translateY(-4px);
            box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1);
        }

        .stat-header {
            display: flex;
            align-items: center;
            gap: 1rem;
            margin-bottom: 1rem;
        }

        .stat-icon {
            width: 48px;
            height: 48px;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.25rem;
        }

        .stat-icon.primary {
            background: linear-gradient(135deg, var(--primary-color), #0891b2);
        }

        .stat-icon.secondary {
            background: linear-gradient(135deg, var(--secondary-color), #059669);
        }

        .stat-icon.accent {
            background: linear-gradient(135deg, var(--accent-color), #d97706);
        }

        .stat-icon.danger {
            background: linear-gradient(135deg, var(--danger-color), #dc2626);
        }

        .stat-info h3 {
            margin: 0;
            font-size: 0.875rem;
            color: var(--gray-600);
            text-transform: uppercase;
            font-weight: 500;
        }

        .stat-value {
            font-size: 2rem;
            font-weight: 700;
            color: var(--gray-800);
            margin: 0.5rem 0;
        }

        .stat-change {
            font-size: 0.875rem;
            font-weight: 500;
        }

        .stat-change.positive {
            color: var(--secondary-color);
        }

        .stat-change.negative {
            color: var(--danger-color);
        }

        .main-content {
            display: grid;
            grid-template-columns: 2fr 1fr;
            gap: 2rem;
        }

        .content-section {
            background: white;
            border-radius: 16px;
            padding: 2rem;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
            border: 1px solid var(--gray-100);
        }

        .section-header {
            display: flex;
            justify-content: between;
            align-items: center;
            margin-bottom: 1.5rem;
        }

        .section-title {
            font-size: 1.25rem;
            font-weight: 600;
            color: var(--gray-800);
            margin: 0;
        }

        .section-action {
            color: var(--primary-color);
            text-decoration: none;
            font-weight: 500;
            font-size: 0.875rem;
        }

        .section-action:hover {
            color: #0891b2;
        }

        .performance-chart {
            height: 300px;
            margin-bottom: 1rem;
        }

        .activity-item {
            display: flex;
            align-items: center;
            gap: 1rem;
            padding: 1rem;
            background: var(--gray-50);
            border-radius: 12px;
            margin-bottom: 1rem;
        }

        .activity-icon {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1rem;
            flex-shrink: 0;
        }

        .activity-icon.court {
            background: var(--primary-color);
        }

        .activity-icon.coach {
            background: var(--secondary-color);
        }

        .activity-icon.tournament {
            background: var(--accent-color);
        }

        .activity-details h4 {
            margin: 0 0 0.25rem 0;
            font-size: 0.875rem;
            font-weight: 600;
            color: var(--gray-800);
        }

        .activity-details p {
            margin: 0;
            font-size: 0.8rem;
            color: var(--gray-600);
        }

        .activity-time {
            font-size: 0.75rem;
            color: var(--gray-500);
            margin-left: auto;
        }

        .profile-section {
            display: flex;
            flex-direction: column;
            gap: 1.5rem;
        }

        .profile-card {
            background: var(--gray-50);
            border-radius: 12px;
            padding: 1.5rem;
        }

        .profile-field {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1rem;
        }

        .profile-field:last-child {
            margin-bottom: 0;
        }

        .field-label {
            font-weight: 500;
            color: var(--gray-700);
            font-size: 0.875rem;
        }

        .field-value {
            color: var(--gray-600);
            font-size: 0.875rem;
        }

        .edit-profile-btn {
            background: var(--secondary-color);
            color: white;
            border: none;
            padding: 0.75rem 1.5rem;
            border-radius: 12px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s ease;
            width: 100%;
            margin-top: 1rem;
        }

        .edit-profile-btn:hover {
            background: #059669;
        }

        /* Responsive */
        @media (max-width: 768px) {
            .dashboard-container {
                padding: 1rem;
            }
            
            .welcome-content {
                flex-direction: column;
                text-align: center;
            }
            
            .main-content {
                grid-template-columns: 1fr;
            }
            
            .quick-actions {
                flex-direction: column;
            }
        }

        /* Animation */
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .animate-in {
            animation: fadeInUp 0.6s ease-out forwards;
        }
    </style>
</head>
<body>
    <!-- Include Navbar -->
    {% include 'html/navbar.html' %}

    <div class="dashboard-container">
        <!-- Welcome Section -->
        <div class="welcome-section animate-in">
            <div class="welcome-content">
                <div class="player-avatar" id="playerAvatar">P</div>
                <div class="welcome-text">
                    <h1 id="welcomeTitle">Welcome back, Player!</h1>
                    <p class="welcome-subtitle" id="welcomeSubtitle">Ready for your next training session?</p>
                </div>
            </div>
            <div class="quick-actions">
                <a href="/reservation" class="quick-action-btn">
                    <i class="bi bi-calendar-plus"></i> Book Court
                </a>
                <a href="/res/coach-reservation/" class="quick-action-btn">
                    <i class="bi bi-person-plus"></i> Book Coach
                </a>
                <a href="/member/messages/" class="quick-action-btn">
                    <i class="bi bi-chat"></i> Messages
                </a>
            </div>
        </div>

        <!-- Stats Grid -->
        <div class="stats-grid">
            <div class="stat-card animate-in" style="animation-delay: 0.1s;">
                <div class="stat-header">
                    <div class="stat-icon primary">
                        <i class="bi bi-trophy"></i>
                    </div>
                    <div class="stat-info">
                        <h3>Total Matches</h3>
                    </div>
                </div>
                <div class="stat-value" id="totalMatches">0</div>
                <div class="stat-change positive" id="matchesChange">+0 this month</div>
            </div>

            <div class="stat-card animate-in" style="animation-delay: 0.2s;">
                <div class="stat-header">
                    <div class="stat-icon secondary">
                        <i class="bi bi-target"></i>
                    </div>
                    <div class="stat-info">
                        <h3>Win Rate</h3>
                    </div>
                </div>
                <div class="stat-value" id="winRate">0%</div>
                <div class="stat-change positive" id="winRateChange">+0% this month</div>
            </div>

            <div class="stat-card animate-in" style="animation-delay: 0.3s;">
                <div class="stat-header">
                    <div class="stat-icon accent">
                        <i class="bi bi-clock"></i>
                    </div>
                    <div class="stat-info">
                        <h3>Training Hours</h3>
                    </div>
                </div>
                <div class="stat-value" id="trainingHours">0</div>
                <div class="stat-change positive" id="hoursChange">+0 this month</div>
            </div>

            <div class="stat-card animate-in" style="animation-delay: 0.4s;">
                <div class="stat-header">
                    <div class="stat-icon danger">
                        <i class="bi bi-calendar-event"></i>
                    </div>
                    <div class="stat-info">
                        <h3>Sessions</h3>
                    </div>
                </div>
                <div class="stat-value" id="totalSessions">0</div>
                <div class="stat-change positive" id="sessionsChange">+0 this month</div>
            </div>
        </div>

        <!-- Main Content -->
        <div class="main-content">
            <!-- Performance Section -->
            <div class="content-section animate-in" style="animation-delay: 0.5s;">
                <div class="section-header">
                    <h2 class="section-title">Performance Overview</h2>
                    <a href="#" class="section-action">View Details</a>
                </div>
                <div class="performance-chart">
                    <canvas id="performanceChart"></canvas>
                </div>
                
                <h3 class="section-title mt-4">Recent Activity</h3>
                <div id="recentActivity">
                    <!-- Activity items will be loaded here -->
                </div>
            </div>

            <!-- Profile & Sidebar -->
            <div class="profile-section">
                <!-- Profile Info -->
                <div class="content-section animate-in" style="animation-delay: 0.6s;">
                    <div class="section-header">
                        <h2 class="section-title">Profile</h2>
                        <a href="#" class="section-action" onclick="editProfile()">Edit</a>
                    </div>
                    <div class="profile-card" id="profileInfo">
                        <!-- Profile fields will be loaded here -->
                    </div>
                    <button class="edit-profile-btn" onclick="editProfile()">
                        <i class="bi bi-pencil"></i> Edit Profile
                    </button>
                </div>

                <!-- Quick Stats -->
                <div class="content-section animate-in" style="animation-delay: 0.7s;">
                    <h2 class="section-title">This Week</h2>
                    <div class="activity-item">
                        <div class="activity-icon court">
                            <i class="bi bi-calendar-check"></i>
                        </div>
                        <div class="activity-details">
                            <h4>Upcoming Sessions</h4>
                            <p id="upcomingSessions">Loading...</p>
                        </div>
                    </div>
                    <div class="activity-item">
                        <div class="activity-icon coach">
                            <i class="bi bi-person-check"></i>
                        </div>
                        <div class="activity-details">
                            <h4>Coach Sessions</h4>
                            <p id="coachSessions">Loading...</p>
                        </div>
                    </div>
                    <div class="activity-item">
                        <div class="activity-icon tournament">
                            <i class="bi bi-trophy"></i>
                        </div>
                        <div class="activity-details">
                            <h4>Tournaments</h4>
                            <p id="tournaments">Loading...</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- JavaScript -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        let currentUser = null;
        let playerProfile = null;
        let performanceChart = null;

        // Load player data
        async function loadPlayerData() {
            try {
                const token = localStorage.getItem('token');
                if (!token) {
                    window.location.href = '/auth/login/';
                    return;
                }

                // Get current user profile
                const response = await fetch('/res/api/players/me/', {
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json'
                    }
                });

                if (response.ok) {
                    const data = await response.json();
                    currentUser = data.player;
                    playerProfile = data.profile;
                    
                    updateWelcomeSection();
                    updateStatsCards();
                    updateProfileSection();
                    updateRecentActivity(data.recent_activity);
                    createPerformanceChart();
                } else {
                    console.error('Failed to load player data');
                    window.location.href = '/auth/login/';
                }
            } catch (error) {
                console.error('Error loading player data:', error);
                window.location.href = '/auth/login/';
            }
        }

        function updateWelcomeSection() {
            if (!currentUser) return;
            
            const avatar = document.getElementById('playerAvatar');
            const title = document.getElementById('welcomeTitle');
            const subtitle = document.getElementById('welcomeSubtitle');
            
            const initials = currentUser.full_name.split(' ').map(n => n[0]).join('').toUpperCase();
            avatar.textContent = initials;
            title.textContent = `Welcome back, ${currentUser.first_name || currentUser.username}!`;
            subtitle.textContent = `Level: ${playerProfile.skill_level.charAt(0).toUpperCase() + playerProfile.skill_level.slice(1)} • Sport: ${playerProfile.preferred_sport}`;
        }

        function updateStatsCards() {
            if (!playerProfile) return;
            
            document.getElementById('totalMatches').textContent = playerProfile.total_matches;
            document.getElementById('winRate').textContent = `${playerProfile.win_rate}%`;
            document.getElementById('trainingHours').textContent = playerProfile.total_training_hours;
            document.getElementById('totalSessions').textContent = playerProfile.total_matches + Math.floor(playerProfile.total_training_hours / 2);
            
            // Add some sample changes (in a real app, these would come from the API)
            document.getElementById('matchesChange').textContent = '+2 this month';
            document.getElementById('winRateChange').textContent = '+5% this month';
            document.getElementById('hoursChange').textContent = '+8 this month';
            document.getElementById('sessionsChange').textContent = '+5 this month';
        }

        function updateProfileSection() {
            if (!currentUser || !playerProfile) return;
            
            const profileInfo = document.getElementById('profileInfo');
            profileInfo.innerHTML = `
                <div class="profile-field">
                    <span class="field-label">Full Name</span>
                    <span class="field-value">${currentUser.full_name}</span>
                </div>
                <div class="profile-field">
                    <span class="field-label">Email</span>
                    <span class="field-value">${currentUser.email}</span>
                </div>
                <div class="profile-field">
                    <span class="field-label">Skill Level</span>
                    <span class="field-value">${playerProfile.skill_level.charAt(0).toUpperCase() + playerProfile.skill_level.slice(1)}</span>
                </div>
                <div class="profile-field">
                    <span class="field-label">Preferred Sport</span>
                    <span class="field-value">${playerProfile.preferred_sport}</span>
                </div>
                <div class="profile-field">
                    <span class="field-label">Phone</span>
                    <span class="field-value">${playerProfile.phone || 'Not provided'}</span>
                </div>
                <div class="profile-field">
                    <span class="field-label">Member Since</span>
                    <span class="field-value">${new Date(currentUser.date_joined).toLocaleDateString()}</span>
                </div>
            `;
        }

        function updateRecentActivity(activities) {
            const container = document.getElementById('recentActivity');
            
            if (!activities || activities.length === 0) {
                container.innerHTML = `
                    <div class="activity-item">
                        <div class="activity-icon court">
                            <i class="bi bi-info-circle"></i>
                        </div>
                        <div class="activity-details">
                            <h4>No recent activity</h4>
                            <p>Start booking sessions to see your activity here</p>
                        </div>
                    </div>
                `;
                return;
            }
            
            container.innerHTML = activities.slice(0, 5).map(activity => `
                <div class="activity-item">
                    <div class="activity-icon ${activity.type === 'court_reservation' ? 'court' : 'coach'}">
                        <i class="bi bi-${activity.type === 'court_reservation' ? 'calendar-check' : 'person-check'}"></i>
                    </div>
                    <div class="activity-details">
                        <h4>${activity.description}</h4>
                        <p>${activity.date} at ${activity.time}</p>
                    </div>
                    <div class="activity-time">${formatTimeAgo(activity.date)}</div>
                </div>
            `).join('');
        }

        function createPerformanceChart() {
            const ctx = document.getElementById('performanceChart').getContext('2d');
            
            // Sample performance data (in a real app, this would come from the API)
            const performanceData = [65, 70, 75, 80, 85, 82];
            const labels = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun'];
            
            performanceChart = new Chart(ctx, {
                type: 'line',
                data: {
                    labels: labels,
                    datasets: [{
                        label: 'Performance Score',
                        data: performanceData,
                        borderColor: '#06b6d4',
                        backgroundColor: 'rgba(6, 182, 212, 0.1)',
                        borderWidth: 3,
                        fill: true,
                        tension: 0.4,
                        pointBackgroundColor: '#06b6d4',
                        pointBorderColor: '#ffffff',
                        pointBorderWidth: 2,
                        pointRadius: 6
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: false
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            max: 100,
                            grid: {
                                color: '#f3f4f6'
                            },
                            ticks: {
                                color: '#6b7280'
                            }
                        },
                        x: {
                            grid: {
                                color: '#f3f4f6'
                            },
                            ticks: {
                                color: '#6b7280'
                            }
                        }
                    }
                }
            });
        }

        function formatTimeAgo(dateString) {
            const date = new Date(dateString);
            const now = new Date();
            const diffInHours = (now - date) / (1000 * 60 * 60);
            
            if (diffInHours < 24) {
                return 'Today';
            } else if (diffInHours < 48) {
                return 'Yesterday';
            } else {
                return `${Math.floor(diffInHours / 24)} days ago`;
            }
        }

        function editProfile() {
            // In a real app, this would open a modal or redirect to an edit page
            alert('Profile editing functionality would be implemented here');
        }

        // Initialize dashboard
        document.addEventListener('DOMContentLoaded', function() {
            loadPlayerData();
            
            // Update some sample weekly stats
            document.getElementById('upcomingSessions').textContent = '3 sessions scheduled';
            document.getElementById('coachSessions').textContent = '2 with Coach Martinez';
            document.getElementById('tournaments').textContent = '1 tournament registered';
        });
    </script>
</body>
</html>
