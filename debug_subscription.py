#!/usr/bin/env python
"""
Script to debug subscription creation issues
"""
import os
import sys
import django
from datetime import timedelta

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'tennis_manager.settings')
django.setup()

from django.contrib.auth import get_user_model
from reservations.models import Subscription, Payment, Notification
from django.utils import timezone

User = get_user_model()

def debug_subscription_creation():
    print("🔍 Debugging Subscription Creation...")
    
    try:
        # Get test user
        user = User.objects.filter(username='player_bob').first()
        if not user:
            print("❌ Test user 'player_bob' not found")
            return False
        
        print(f"✅ Found test user: {user.username} (ID: {user.id})")
        
        # Check current subscription
        existing_sub = Subscription.objects.filter(user=user).first()
        if existing_sub:
            print(f"ℹ️  Existing subscription: {existing_sub}")
            print(f"   Status: {existing_sub.status}")
            print(f"   Active: {existing_sub.is_active}")
            print(f"   Plan: {existing_sub.plan_type}")
            print(f"   End Date: {existing_sub.end_date}")
        else:
            print("ℹ️  No existing subscription found")
        
        # Test subscription creation parameters
        plan_type = 'premium'
        duration_months = 1
        monthly_price = 49.99
        
        print(f"\n📋 Testing subscription creation with:")
        print(f"   User: {user.username}")
        print(f"   Plan: {plan_type}")
        print(f"   Duration: {duration_months} months")
        print(f"   Price: ${monthly_price}")
        
        # Calculate end date
        end_date = timezone.now() + timedelta(days=30 * duration_months)
        print(f"   End Date: {end_date}")
        
        # Test if we can create the subscription
        if existing_sub:
            if existing_sub.is_active:
                print("⚠️  User already has active subscription - would return error")
                return True
            else:
                print("🔄 Would update existing subscription...")
                # Test update
                existing_sub.plan_type = plan_type
                existing_sub.end_date = end_date
                existing_sub.monthly_price = monthly_price
                existing_sub.status = 'active'
                existing_sub.auto_renew = True
                existing_sub.save()
                print("✅ Successfully updated existing subscription")
        else:
            print("🆕 Would create new subscription...")
            # Test creation
            subscription = Subscription.objects.create(
                user=user,
                plan_type=plan_type,
                end_date=end_date,
                monthly_price=monthly_price,
                status='active'
            )
            print(f"✅ Successfully created new subscription: {subscription}")
        
        # Test payment creation
        print("\n💰 Testing payment creation...")
        payment = Payment.objects.create(
            user=user,
            payment_type='subscription',
            amount=monthly_price * duration_months,
            status='completed',
            transaction_id=f'TEST_{int(timezone.now().timestamp())}',
            description=f'{plan_type.title()} subscription for {duration_months} month(s)'
        )
        print(f"✅ Successfully created payment: {payment}")
        
        # Test notification creation
        print("\n🔔 Testing notification creation...")
        notification = Notification.objects.create(
            user=user,
            title='Test Subscription',
            message=f'Test {plan_type.title()} subscription created successfully!',
            notification_type='subscription'
        )
        print(f"✅ Successfully created notification: {notification}")
        
        print("\n🎉 All subscription operations work correctly!")
        return True
        
    except Exception as e:
        print(f"❌ Error during subscription debugging: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == '__main__':
    print("🎾 Tennis Management System - Subscription Debug")
    print("="*60)
    
    success = debug_subscription_creation()
    
    if success:
        print("\n🚀 Subscription creation should work!")
        print("✅ Check the API endpoint for any remaining issues.")
    else:
        print("\n⚠️  Subscription creation has issues.")
