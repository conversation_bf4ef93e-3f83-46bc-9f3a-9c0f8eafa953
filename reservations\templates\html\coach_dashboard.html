{% load static %}
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Coach Dashboard - Elite Sports Club</title>
    
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.0/font/bootstrap-icons.css" rel="stylesheet">
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <style>
        :root {
            /* Soft, eye-friendly colors - Coach theme */
            --primary-color: #8b5cf6;
            --primary-light: #c4b5fd;
            --secondary-color: #06b6d4;
            --secondary-light: #67e8f9;
            --accent-color: #10b981;
            --accent-light: #6ee7b7;
            --warning-color: #f59e0b;
            --warning-light: #fbbf24;
            
            /* Neutral colors */
            --gray-50: #f9fafb;
            --gray-100: #f3f4f6;
            --gray-200: #e5e7eb;
            --gray-300: #d1d5db;
            --gray-400: #9ca3af;
            --gray-500: #6b7280;
            --gray-600: #4b5563;
            --gray-700: #374151;
            --gray-800: #1f2937;
            --gray-900: #111827;
            
            /* Background gradients */
            --bg-gradient: linear-gradient(135deg, #faf5ff 0%, #f3e8ff 100%);
            --card-gradient: linear-gradient(145deg, #ffffff 0%, #faf5ff 100%);
            --primary-gradient: linear-gradient(135deg, #8b5cf6 0%, #a855f7 100%);
            --secondary-gradient: linear-gradient(135deg, #06b6d4 0%, #0891b2 100%);
            --success-gradient: linear-gradient(135deg, #10b981 0%, #059669 100%);
            --warning-gradient: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
            
            /* Shadows */
            --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
            --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
            --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
            --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', sans-serif;
            background: var(--bg-gradient);
            color: var(--gray-700);
            line-height: 1.6;
        }

        /* Layout */
        .dashboard-layout {
            display: flex;
            min-height: 100vh;
        }

        /* Sidebar */
        .sidebar {
            width: 280px;
            background: white;
            border-right: 1px solid var(--gray-200);
            box-shadow: var(--shadow-lg);
            position: fixed;
            height: 100vh;
            overflow-y: auto;
            z-index: 1000;
        }

        .sidebar-header {
            padding: 2rem;
            border-bottom: 1px solid var(--gray-100);
        }

        .logo {
            display: flex;
            align-items: center;
            gap: 0.75rem;
            text-decoration: none;
            color: var(--gray-800);
        }

        .logo-icon {
            width: 48px;
            height: 48px;
            background: var(--primary-gradient);
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.25rem;
            box-shadow: var(--shadow-md);
        }

        .logo-text {
            font-family: 'Poppins', sans-serif;
            font-weight: 700;
            font-size: 1.125rem;
            color: var(--gray-800);
        }

        .logo-subtitle {
            font-size: 0.75rem;
            color: var(--gray-500);
            font-weight: 500;
        }

        /* Navigation */
        .nav-menu {
            padding: 1rem;
            list-style: none;
        }

        .nav-item {
            margin-bottom: 0.25rem;
        }

        .nav-link {
            display: flex;
            align-items: center;
            gap: 0.75rem;
            padding: 0.75rem 1rem;
            color: var(--gray-600);
            text-decoration: none;
            border-radius: 8px;
            transition: all 0.2s ease;
            font-weight: 500;
            font-size: 0.875rem;
        }

        .nav-link:hover {
            background: var(--gray-50);
            color: var(--primary-color);
            transform: translateX(2px);
        }

        .nav-link.active {
            background: var(--primary-color);
            color: white;
            box-shadow: var(--shadow-md);
        }

        .nav-icon {
            font-size: 1rem;
            width: 20px;
            text-align: center;
        }

        /* Main Content */
        .main-content {
            flex: 1;
            margin-left: 280px;
            padding: 2rem;
            background: var(--bg-gradient);
        }

        /* Header */
        .page-header {
            background: white;
            border-radius: 16px;
            padding: 2rem;
            margin-bottom: 2rem;
            box-shadow: var(--shadow-sm);
            border: 1px solid var(--gray-100);
        }

        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
            gap: 1rem;
        }

        .page-title {
            font-family: 'Poppins', sans-serif;
            font-size: 2rem;
            font-weight: 700;
            color: var(--gray-800);
            margin: 0;
            display: flex;
            align-items: center;
            gap: 0.75rem;
        }

        .title-icon {
            width: 48px;
            height: 48px;
            background: var(--primary-gradient);
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.25rem;
        }

        .page-subtitle {
            color: var(--gray-500);
            font-size: 1rem;
            margin-top: 0.5rem;
            font-weight: 400;
        }

        .user-profile {
            display: flex;
            align-items: center;
            gap: 0.75rem;
            background: var(--gray-50);
            padding: 0.75rem 1rem;
            border-radius: 12px;
            border: 1px solid var(--gray-200);
        }

        .profile-avatar {
            width: 40px;
            height: 40px;
            background: var(--primary-gradient);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1rem;
        }

        .profile-info {
            color: var(--gray-700);
        }

        .profile-name {
            font-weight: 600;
            font-size: 0.875rem;
        }

        .profile-role {
            font-size: 0.75rem;
            color: var(--gray-500);
        }

        /* Stats Grid */
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
        }

        .stat-card {
            background: white;
            border-radius: 16px;
            padding: 1.5rem;
            border: 1px solid var(--gray-100);
            box-shadow: var(--shadow-sm);
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .stat-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: var(--card-accent, var(--primary-color));
        }

        .stat-card:hover {
            transform: translateY(-4px);
            box-shadow: var(--shadow-lg);
        }

        .stat-card.primary { --card-accent: var(--primary-color); }
        .stat-card.secondary { --card-accent: var(--secondary-color); }
        .stat-card.success { --card-accent: var(--accent-color); }
        .stat-card.warning { --card-accent: var(--warning-color); }

        .stat-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1rem;
        }

        .stat-icon {
            width: 48px;
            height: 48px;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.25rem;
            color: white;
            background: var(--card-accent, var(--primary-color));
        }

        .stat-value {
            font-size: 2rem;
            font-weight: 700;
            color: var(--gray-800);
            margin-bottom: 0.25rem;
        }

        .stat-label {
            color: var(--gray-600);
            font-size: 0.875rem;
            font-weight: 500;
        }

        .stat-change {
            font-size: 0.75rem;
            margin-top: 0.5rem;
            font-weight: 500;
        }

        .stat-change.positive {
            color: var(--accent-color);
        }

        .stat-change.info {
            color: var(--secondary-color);
        }

        /* Schedule Section */
        .schedule-section {
            background: white;
            border-radius: 16px;
            padding: 1.5rem;
            margin-bottom: 2rem;
            box-shadow: var(--shadow-sm);
            border: 1px solid var(--gray-100);
        }

        .schedule-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1.5rem;
            padding-bottom: 1rem;
            border-bottom: 1px solid var(--gray-100);
        }

        .schedule-title {
            font-size: 1.25rem;
            font-weight: 600;
            color: var(--gray-800);
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .schedule-list {
            display: grid;
            gap: 1rem;
        }

        .schedule-item {
            background: var(--gray-50);
            border-radius: 12px;
            padding: 1rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
            transition: all 0.2s ease;
            border: 1px solid var(--gray-100);
        }

        .schedule-item:hover {
            background: var(--gray-100);
            transform: translateX(4px);
        }

        .schedule-info {
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .schedule-time {
            background: var(--primary-color);
            color: white;
            padding: 0.5rem 0.75rem;
            border-radius: 8px;
            font-weight: 600;
            font-size: 0.875rem;
            min-width: 80px;
            text-align: center;
        }

        .schedule-details h4 {
            margin: 0 0 0.25rem 0;
            font-size: 1rem;
            color: var(--gray-800);
            font-weight: 600;
        }

        .schedule-details p {
            margin: 0;
            color: var(--gray-600);
            font-size: 0.875rem;
        }

        .schedule-status {
            padding: 0.25rem 0.75rem;
            border-radius: 20px;
            font-size: 0.75rem;
            font-weight: 600;
        }

        .status-upcoming {
            background: var(--secondary-light);
            color: var(--secondary-color);
        }

        .status-active {
            background: var(--accent-light);
            color: var(--accent-color);
        }

        .status-completed {
            background: var(--gray-200);
            color: var(--gray-600);
        }

        /* Quick Actions */
        .actions-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 1.5rem;
        }

        .action-card {
            background: white;
            border-radius: 16px;
            padding: 1.5rem;
            border: 1px solid var(--gray-100);
            box-shadow: var(--shadow-sm);
            transition: all 0.3s ease;
            text-align: center;
        }

        .action-card:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-md);
        }

        .action-icon {
            width: 56px;
            height: 56px;
            margin: 0 auto 1rem;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
            color: white;
            background: var(--action-color, var(--primary-gradient));
        }

        .action-title {
            font-size: 1.125rem;
            font-weight: 600;
            color: var(--gray-800);
            margin-bottom: 0.5rem;
        }

        .action-description {
            color: var(--gray-600);
            font-size: 0.875rem;
            line-height: 1.4;
        }

        /* Responsive */
        @media (max-width: 768px) {
            .sidebar {
                transform: translateX(-100%);
                transition: transform 0.3s ease;
            }
            
            .sidebar.show {
                transform: translateX(0);
            }
            
            .main-content {
                margin-left: 0;
                padding: 1rem;
            }
            
            .page-title {
                font-size: 1.5rem;
            }
            
            .stats-grid {
                grid-template-columns: 1fr;
            }
        }

        /* Smooth animations */
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .animate-in {
            animation: fadeInUp 0.6s ease-out;
        }
    </style>
</head>
<body>
    <div class="dashboard-layout">
        <!-- Sidebar -->
        <nav class="sidebar">
            <div class="sidebar-header">
                <a href="{% url 'home' %}" class="logo">
                    <div class="logo-icon">
                        <i class="bi bi-person-badge"></i>
                    </div>
                    <div>
                        <div class="logo-text">Coach Panel</div>
                        <div class="logo-subtitle">Training Hub</div>
                    </div>
                </a>
            </div>

            <ul class="nav-menu">
                <li class="nav-item">
                    <a href="#dashboard" class="nav-link active">
                        <i class="bi bi-grid nav-icon"></i>
                        Dashboard
                    </a>
                </li>
                <li class="nav-item">
                    <a href="#schedule" class="nav-link">
                        <i class="bi bi-calendar-event nav-icon"></i>
                        My Schedule
                    </a>
                </li>
                <li class="nav-item">
                    <a href="#students" class="nav-link">
                        <i class="bi bi-people nav-icon"></i>
                        My Students
                    </a>
                </li>
                <li class="nav-item">
                    <a href="#sessions" class="nav-link">
                        <i class="bi bi-clipboard-check nav-icon"></i>
                        Training Sessions
                    </a>
                </li>
                <li class="nav-item">
                    <a href="#progress" class="nav-link">
                        <i class="bi bi-graph-up nav-icon"></i>
                        Progress Tracking
                    </a>
                </li>
                <li class="nav-item">
                    <a href="#earnings" class="nav-link">
                        <i class="bi bi-currency-dollar nav-icon"></i>
                        Earnings
                    </a>
                </li>
                <li class="nav-item">
                    <a href="#profile" class="nav-link">
                        <i class="bi bi-person-gear nav-icon"></i>
                        Profile Settings
                    </a>
                </li>
                <li class="nav-item" style="margin-top: 2rem;">
                    <a href="{% url 'logout' %}" class="nav-link">
                        <i class="bi bi-box-arrow-right nav-icon"></i>
                        Logout
                    </a>
                </li>
            </ul>
        </nav>

        <!-- Main Content -->
        <main class="main-content">
            <!-- Page Header -->
            <div class="page-header">
                <div class="header-content">
                    <div>
                        <h1 class="page-title">
                            <div class="title-icon">
                                <i class="bi bi-person-badge"></i>
                            </div>
                            Coach Dashboard
                        </h1>
                        <p class="page-subtitle">Welcome back, Coach! Ready to inspire champions today?</p>
                    </div>
                    <div class="user-profile">
                        <div class="profile-avatar">
                            <i class="bi bi-person"></i>
                        </div>
                        <div class="profile-info">
                            <div class="profile-name">Coach Smith</div>
                            <div class="profile-role">Tennis & Fitness Coach</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Stats Grid -->
            <div class="stats-grid">
                <div class="stat-card primary animate-in">
                    <div class="stat-header">
                        <div class="stat-icon">
                            <i class="bi bi-people"></i>
                        </div>
                    </div>
                    <div class="stat-value">32</div>
                    <div class="stat-label">Active Students</div>
                    <div class="stat-change positive">
                        <i class="bi bi-arrow-up"></i> +3 new this week
                    </div>
                </div>

                <div class="stat-card secondary animate-in" style="animation-delay: 0.1s;">
                    <div class="stat-header">
                        <div class="stat-icon">
                            <i class="bi bi-calendar-check"></i>
                        </div>
                    </div>
                    <div class="stat-value">8</div>
                    <div class="stat-label">Today's Sessions</div>
                    <div class="stat-change info">
                        <i class="bi bi-clock"></i> Next at 2:00 PM
                    </div>
                </div>

                <div class="stat-card success animate-in" style="animation-delay: 0.2s;">
                    <div class="stat-header">
                        <div class="stat-icon">
                            <i class="bi bi-trophy"></i>
                        </div>
                    </div>
                    <div class="stat-value">156</div>
                    <div class="stat-label">Sessions This Month</div>
                    <div class="stat-change positive">
                        <i class="bi bi-arrow-up"></i> +12% vs last month
                    </div>
                </div>

                <div class="stat-card warning animate-in" style="animation-delay: 0.3s;">
                    <div class="stat-header">
                        <div class="stat-icon">
                            <i class="bi bi-currency-dollar"></i>
                        </div>
                    </div>
                    <div class="stat-value">$3,240</div>
                    <div class="stat-label">Monthly Earnings</div>
                    <div class="stat-change positive">
                        <i class="bi bi-arrow-up"></i> +18% increase
                    </div>
                </div>
            </div>

            <!-- Today's Schedule -->
            <div class="schedule-section animate-in" style="animation-delay: 0.4s;">
                <div class="schedule-header">
                    <h2 class="schedule-title">
                        <i class="bi bi-calendar-event"></i>
                        Today's Schedule
                    </h2>
                    <div id="live-clock" style="font-size: 0.875rem; color: var(--gray-500);"></div>
                </div>
                
                <div class="schedule-list">
                    <div class="schedule-item">
                        <div class="schedule-info">
                            <div class="schedule-time">9:00 AM</div>
                            <div class="schedule-details">
                                <h4>Tennis Training - Beginners</h4>
                                <p>Court 1 • 6 students • 90 minutes</p>
                            </div>
                        </div>
                        <div class="schedule-status status-completed">Completed</div>
                    </div>
                    
                    <div class="schedule-item">
                        <div class="schedule-info">
                            <div class="schedule-time">2:00 PM</div>
                            <div class="schedule-details">
                                <h4>Private Session - Advanced</h4>
                                <p>Court 2 • 1 student • 60 minutes</p>
                            </div>
                        </div>
                        <div class="schedule-status status-upcoming">Upcoming</div>
                    </div>
                    
                    <div class="schedule-item">
                        <div class="schedule-info">
                            <div class="schedule-time">4:30 PM</div>
                            <div class="schedule-details">
                                <h4>Fitness Training Group</h4>
                                <p>Gym • 8 students • 75 minutes</p>
                            </div>
                        </div>
                        <div class="schedule-status status-upcoming">Upcoming</div>
                    </div>
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="actions-grid">
                <div class="action-card animate-in" style="animation-delay: 0.5s; --action-color: var(--primary-gradient);">
                    <div class="action-icon">
                        <i class="bi bi-calendar-plus"></i>
                    </div>
                    <div class="action-title">Add Session</div>
                    <div class="action-description">
                        Schedule new training sessions and manage your availability calendar.
                    </div>
                </div>

                <div class="action-card animate-in" style="animation-delay: 0.6s; --action-color: var(--secondary-gradient);">
                    <div class="action-icon">
                        <i class="bi bi-clipboard-data"></i>
                    </div>
                    <div class="action-title">Student Progress</div>
                    <div class="action-description">
                        Track and update student progress, add notes, and set training goals.
                    </div>
                </div>

                <div class="action-card animate-in" style="animation-delay: 0.7s; --action-color: var(--success-gradient);">
                    <div class="action-icon">
                        <i class="bi bi-chat-dots"></i>
                    </div>
                    <div class="action-title">Messages</div>
                    <div class="action-description">
                        Communicate with students and parents about training schedules and progress.
                    </div>
                </div>

                <div class="action-card animate-in" style="animation-delay: 0.8s; --action-color: var(--warning-gradient);">
                    <div class="action-icon">
                        <i class="bi bi-graph-up-arrow"></i>
                    </div>
                    <div class="action-title">Performance Analytics</div>
                    <div class="action-description">
                        View detailed analytics about your coaching performance and student outcomes.
                    </div>
                </div>
            </div>
        </main>
    </div>

    <!-- JavaScript -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Navigation interactions
        document.querySelectorAll('.nav-link').forEach(link => {
            link.addEventListener('click', function(e) {
                e.preventDefault();
                document.querySelectorAll('.nav-link').forEach(l => l.classList.remove('active'));
                this.classList.add('active');
            });
        });

        // Real-time clock
        function updateClock() {
            const now = new Date();
            const timeString = now.toLocaleTimeString();
            const dateString = now.toLocaleDateString();
            
            const clockElement = document.getElementById('live-clock');
            if (clockElement) {
                clockElement.innerHTML = `${timeString}<br><small>${dateString}</small>`;
            }
        }
        
        setInterval(updateClock, 1000);
        updateClock();

        // Mobile sidebar toggle
        function toggleSidebar() {
            document.querySelector('.sidebar').classList.toggle('show');
        }

        // Add click effects to action cards
        document.querySelectorAll('.action-card').forEach(card => {
            card.addEventListener('click', function() {
                this.style.transform = 'scale(0.98)';
                setTimeout(() => {
                    this.style.transform = '';
                }, 150);
            });
        });
    </script>
</body>
</html>
