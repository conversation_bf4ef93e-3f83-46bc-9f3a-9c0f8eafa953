#!/usr/bin/env python
"""
Script to create test data for the tennis management system
"""
import os
import sys
import django

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'tennis_manager.settings')
django.setup()

from django.contrib.auth import get_user_model
from reservations.models import Coach, Terrain

User = get_user_model()

def create_test_data():
    print("Creating test data for Tennis Management System...")
    
    # Create test users
    print("\n1. Creating test users...")
    
    # Create admin user (if not exists)
    admin_user, created = User.objects.get_or_create(
        username='admin',
        defaults={
            'email': '<EMAIL>',
            'first_name': 'Admin',
            'last_name': 'User',
            'role': 'admin',
            'is_staff': True,
            'is_superuser': True
        }
    )
    if created:
        admin_user.set_password('admin123')
        admin_user.save()
        print(f"✅ Created admin user: {admin_user.username}")
    else:
        print(f"ℹ️  Admin user already exists: {admin_user.username}")
    
    # Create coach users
    coaches_data = [
        {
            'username': 'coach_john',
            'email': '<EMAIL>',
            'first_name': '<PERSON>',
            'last_name': '<PERSON>',
            'role': 'coach'
        },
        {
            'username': 'coach_maria',
            'email': '<EMAIL>',
            'first_name': 'Maria',
            'last_name': 'Garcia',
            'role': 'coach'
        },
        {
            'username': 'coach_david',
            'email': '<EMAIL>',
            'first_name': 'David',
            'last_name': 'Johnson',
            'role': 'coach'
        }
    ]
    
    for coach_data in coaches_data:
        user, created = User.objects.get_or_create(
            username=coach_data['username'],
            defaults=coach_data
        )
        if created:
            user.set_password('coach123')
            user.save()
            print(f"✅ Created coach user: {user.username}")
        else:
            print(f"ℹ️  Coach user already exists: {user.username}")
    
    # Create player users
    players_data = [
        {
            'username': 'player_alice',
            'email': '<EMAIL>',
            'first_name': 'Alice',
            'last_name': 'Brown',
            'role': 'player'
        },
        {
            'username': 'player_bob',
            'email': '<EMAIL>',
            'first_name': 'Bob',
            'last_name': 'Wilson',
            'role': 'player'
        }
    ]
    
    for player_data in players_data:
        user, created = User.objects.get_or_create(
            username=player_data['username'],
            defaults=player_data
        )
        if created:
            user.set_password('player123')
            user.save()
            print(f"✅ Created player user: {user.username}")
        else:
            print(f"ℹ️  Player user already exists: {user.username}")
    
    print("\n2. Creating coaches...")
    
    # Create coach profiles
    coaches_profiles = [
        {
            'name': 'John Smith',
            'email': '<EMAIL>',
            'phone': '******-0101',
            'price_per_hour': 75.00,
            'experience': 8
        },
        {
            'name': 'Maria Garcia',
            'email': '<EMAIL>',
            'phone': '******-0102',
            'price_per_hour': 85.00,
            'experience': 12
        },
        {
            'name': 'David Johnson',
            'email': '<EMAIL>',
            'phone': '******-0103',
            'price_per_hour': 90.00,
            'experience': 15
        }
    ]
    
    for coach_profile in coaches_profiles:
        coach, created = Coach.objects.get_or_create(
            email=coach_profile['email'],
            defaults=coach_profile
        )
        if created:
            print(f"✅ Created coach: {coach.name}")
        else:
            print(f"ℹ️  Coach already exists: {coach.name}")
    
    print("\n3. Creating tennis courts...")
    
    # Create tennis courts
    courts_data = [
        {
            'name': 'Court 1 - Clay',
            'location': 'North Side',
            'price_per_hour': 25.00,
            'available': True
        },
        {
            'name': 'Court 2 - Hard',
            'location': 'South Side',
            'price_per_hour': 30.00,
            'available': True
        },
        {
            'name': 'Court 3 - Grass',
            'location': 'East Side',
            'price_per_hour': 35.00,
            'available': True
        },
        {
            'name': 'Court 4 - Indoor Hard',
            'location': 'Indoor Complex',
            'price_per_hour': 40.00,
            'available': True
        }
    ]
    
    for court_data in courts_data:
        terrain, created = Terrain.objects.get_or_create(
            name=court_data['name'],
            defaults=court_data
        )
        if created:
            print(f"✅ Created court: {terrain.name}")
        else:
            print(f"ℹ️  Court already exists: {terrain.name}")
    
    print("\n🎾 Test data creation completed!")
    print("\n📋 Summary:")
    print(f"   👥 Users: {User.objects.count()}")
    print(f"   🎾 Coaches: {Coach.objects.count()}")
    print(f"   🏟️  Courts: {Terrain.objects.count()}")
    
    print("\n🔑 Login Credentials:")
    print("   Admin: admin / admin123")
    print("   Coach: coach_john / coach123")
    print("   Player: player_alice / player123")
    
    print("\n🌐 Access URLs:")
    print("   Home: http://127.0.0.1:8000/")
    print("   Login: http://127.0.0.1:8000/auth/login/")
    print("   Admin Dashboard: http://127.0.0.1:8000/auth/admin-dashboard/")
    print("   Coach Dashboard: http://127.0.0.1:8000/auth/coach-dashboard/")
    print("   Player Dashboard: http://127.0.0.1:8000/auth/joueur-dashboard/")

if __name__ == '__main__':
    create_test_data()
