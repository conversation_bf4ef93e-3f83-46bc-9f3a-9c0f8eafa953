#!/usr/bin/env python
"""
Test complet du système de gestion des coachs par l'admin
"""
import requests
import json

def test_admin_coach_management():
    print("🎾 Test Système de Gestion des Coachs - Admin")
    print("="*50)
    
    # 1. Test login admin
    print("\n1️⃣ Test Login Admin:")
    try:
        login_response = requests.post('http://127.0.0.1:8000/auth/api/login/', json={
            'email': '<EMAIL>',
            'password': 'admin123'
        })
        
        if login_response.status_code == 200:
            print("✅ Login admin réussi")
            access_token = login_response.json().get('access')
            headers = {'Authorization': f'Bearer {access_token}'}
        else:
            print(f"❌ Login admin échoué: {login_response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Erreur login admin: {e}")
        return False
    
    # 2. Test get coach details API
    print("\n2️⃣ Test API Coach Details:")
    try:
        # Get first coach for testing
        coaches_response = requests.get('http://127.0.0.1:8000/res/api/coaches/')
        coaches = coaches_response.json().get('coaches', [])
        
        if len(coaches) > 0:
            coach_id = coaches[0]['id']
            coach_name = coaches[0]['name']
            
            details_response = requests.get(f'http://127.0.0.1:8000/res/api/coaches/{coach_id}/details/', headers=headers)
            
            if details_response.status_code == 200:
                coach_details = details_response.json().get('coach', {})
                print(f"✅ Coach details récupérés pour {coach_name}")
                print(f"   Email: {coach_details.get('email')}")
                print(f"   Prix: ${coach_details.get('price_per_hour')}/heure")
                print(f"   Actif: {coach_details.get('is_active')}")
            else:
                print(f"❌ Erreur coach details: {details_response.status_code}")
                return False
        else:
            print("❌ Aucun coach trouvé pour le test")
            return False
    except Exception as e:
        print(f"❌ Erreur test coach details: {e}")
        return False
    
    # 3. Test update coach API
    print("\n3️⃣ Test API Update Coach:")
    try:
        update_data = {
            'name': coach_details.get('name'),
            'email': coach_details.get('email'),
            'phone': coach_details.get('phone', ''),
            'price_per_hour': coach_details.get('price_per_hour'),
            'experience': coach_details.get('experience', 0),
            'specialization': 'Updated by test - Singles & Doubles',
            'is_active': True
        }
        
        update_response = requests.put(f'http://127.0.0.1:8000/res/api/coaches/{coach_id}/update/', 
                                     json=update_data, headers=headers)
        
        if update_response.status_code == 200:
            updated_coach = update_response.json().get('coach', {})
            print(f"✅ Coach mis à jour avec succès")
            print(f"   Nouvelle spécialisation: {updated_coach.get('specialization')}")
        else:
            print(f"❌ Erreur update coach: {update_response.status_code}")
            print(f"   Message: {update_response.text}")
            return False
    except Exception as e:
        print(f"❌ Erreur test update coach: {e}")
        return False
    
    # 4. Test create schedule slot API
    print("\n4️⃣ Test API Create Schedule Slot:")
    try:
        from datetime import datetime, timedelta
        tomorrow = (datetime.now() + timedelta(days=1)).strftime('%Y-%m-%d')
        
        schedule_data = {
            'coach_id': coach_id,
            'date': tomorrow,
            'start_time': '18:00',  # Use evening time to avoid conflicts
            'end_time': '19:00'
        }
        
        create_response = requests.post('http://127.0.0.1:8000/res/api/coach-schedule/create/', 
                                      json=schedule_data, headers=headers)
        
        if create_response.status_code == 201:
            slot_data = create_response.json().get('slot', {})
            slot_id = slot_data.get('id')
            print(f"✅ Créneau créé avec succès")
            print(f"   ID: {slot_id}")
            print(f"   Date: {slot_data.get('date')}")
            print(f"   Heure: {slot_data.get('start_time')}-{slot_data.get('end_time')}")
        else:
            print(f"❌ Erreur create schedule: {create_response.status_code}")
            print(f"   Message: {create_response.text}")
            return False
    except Exception as e:
        print(f"❌ Erreur test create schedule: {e}")
        return False
    
    # 5. Test get coach schedule API
    print("\n5️⃣ Test API Get Coach Schedule:")
    try:
        today = datetime.now().strftime('%Y-%m-%d')
        next_week = (datetime.now() + timedelta(days=7)).strftime('%Y-%m-%d')
        
        schedule_response = requests.get(
            f'http://127.0.0.1:8000/res/api/coaches/{coach_id}/schedule/?start_date={today}&end_date={next_week}', 
            headers=headers
        )
        
        if schedule_response.status_code == 200:
            schedule_data = schedule_response.json()
            slots = schedule_data.get('schedule_slots', [])
            print(f"✅ Horaires récupérés pour {schedule_data.get('coach_name')}")
            print(f"   {len(slots)} créneaux trouvés")
            
            # Show some slots
            for slot in slots[:3]:
                status = "Réservé" if slot.get('is_booked') else "Disponible"
                print(f"   📅 {slot.get('date')} {slot.get('start_time')}-{slot.get('end_time')} ({status})")
        else:
            print(f"❌ Erreur get schedule: {schedule_response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Erreur test get schedule: {e}")
        return False
    
    # 6. Test delete schedule slot API
    print("\n6️⃣ Test API Delete Schedule Slot:")
    try:
        delete_response = requests.delete(f'http://127.0.0.1:8000/res/api/schedule-slots/{slot_id}/delete/', 
                                        headers=headers)
        
        if delete_response.status_code == 200:
            print(f"✅ Créneau supprimé avec succès (ID: {slot_id})")
        else:
            print(f"❌ Erreur delete schedule: {delete_response.status_code}")
            print(f"   Message: {delete_response.text}")
            return False
    except Exception as e:
        print(f"❌ Erreur test delete schedule: {e}")
        return False
    
    # 7. Test admin dashboard interface
    print("\n7️⃣ Test Interface Admin Dashboard:")
    try:
        dashboard_response = requests.get('http://127.0.0.1:8000/auth/admin-dashboard/')
        
        if dashboard_response.status_code == 200:
            html_content = dashboard_response.text
            
            # Check for edit coach functionality
            checks = [
                ('editCoach function', 'editCoach' in html_content),
                ('saveCoachChanges function', 'saveCoachChanges' in html_content),
                ('Edit Coach Modal', 'editCoachModal' in html_content),
                ('Coach form fields', 'editCoachName' in html_content and 'editCoachEmail' in html_content),
                ('Schedule management', 'manageCoachSchedule' in html_content),
                ('addNewScheduleSlot function', 'addNewScheduleSlot' in html_content),
                ('deleteScheduleSlot function', 'deleteScheduleSlot' in html_content),
                ('Schedule modal', 'coachScheduleModal' in html_content),
                ('Schedule form', 'newSlotDate' in html_content and 'newSlotStartTime' in html_content)
            ]
            
            all_passed = True
            for check_name, check_result in checks:
                if check_result:
                    print(f"   ✅ {check_name}")
                else:
                    print(f"   ❌ {check_name} manquant")
                    all_passed = False
            
            if all_passed:
                print("✅ Interface admin complète!")
            else:
                print("⚠️  Certaines fonctionnalités manquent dans l'interface")
        else:
            print(f"❌ Dashboard admin inaccessible: {dashboard_response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Erreur test dashboard admin: {e}")
        return False
    
    return True

def show_manual_test_instructions():
    print("\n🎯 INSTRUCTIONS POUR TEST MANUEL:")
    print("="*40)
    print("1. 🌐 Aller sur: http://127.0.0.1:8000/auth/login/")
    print("2. 🔑 Se connecter: <EMAIL> / admin123")
    print("3. 👨‍🏫 Aller dans la section 'Coaches'")
    print("4. 🔄 Tester Edit Coach:")
    print("   - Cliquer sur 'Edit' d'un coach")
    print("   - Modifier les informations (nom, email, prix, etc.)")
    print("   - Sauvegarder les modifications")
    print("   - Vérifier que les changements sont appliqués")
    print("5. 📅 Tester Schedule Management:")
    print("   - Cliquer sur 'Schedule' d'un coach")
    print("   - Voir les créneaux existants")
    print("   - Ajouter un nouveau créneau (date, heure début, heure fin)")
    print("   - Vérifier que le créneau apparaît dans la liste")
    print("   - Supprimer un créneau disponible")
    print("   - Vérifier que le créneau est supprimé")
    
    print("\n🎨 FONCTIONNALITÉS À TESTER:")
    print("="*30)
    print("✓ Edit Coach Modal:")
    print("  - Formulaire pré-rempli avec les données actuelles")
    print("  - Validation des champs (email, prix > 0)")
    print("  - Sauvegarde avec feedback de succès")
    print("  - Mise à jour de la liste des coachs")
    print("✓ Schedule Management Modal:")
    print("  - Interface à 2 colonnes (ajout + liste)")
    print("  - Formulaire d'ajout de créneau")
    print("  - Liste des créneaux existants par date")
    print("  - Distinction créneaux disponibles/réservés")
    print("  - Suppression des créneaux disponibles")
    print("  - Bouton refresh pour recharger")

if __name__ == '__main__':
    print("🎾 TENNIS MANAGEMENT SYSTEM")
    print("🔧 Test Complet - Gestion des Coachs par l'Admin")
    print("="*55)
    
    success = test_admin_coach_management()
    
    if success:
        print("\n🚀 SYSTÈME DE GESTION DES COACHS FONCTIONNEL!")
        print("="*45)
        print("✅ Login admin et authentification")
        print("✅ API Edit Coach complète")
        print("✅ API Schedule Management complète")
        print("✅ Interface admin avec modals")
        print("✅ Validation et gestion d'erreurs")
        print("✅ CRUD complet pour les horaires")
        
        show_manual_test_instructions()
        
        print("\n🎉 FÉLICITATIONS!")
        print("="*20)
        print("Le système de gestion des coachs par l'admin")
        print("est ENTIÈREMENT FONCTIONNEL!")
        print("- Edit Coach avec tous les champs")
        print("- Schedule Management avec CRUD complet")
        print("- Interface moderne et intuitive")
        print("- APIs robustes avec validation")
        
    else:
        print("\n⚠️  Des problèmes ont été détectés.")
        print("Vérifiez les erreurs ci-dessus et corrigez-les.")
