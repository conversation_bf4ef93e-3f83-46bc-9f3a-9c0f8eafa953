"""
Tennis Club Elite Chatbot Service
Intégration avec OpenAI GPT pour assistance intelligente
"""
import openai
import json
from django.conf import settings
from django.utils import timezone
from .models import ChatConversation, ChatMessage, User
import logging

logger = logging.getLogger(__name__)

class TennisClubChatbot:
    def __init__(self):
        # Configuration OpenAI (vous devrez ajouter votre clé API)
        self.api_key = getattr(settings, 'OPENAI_API_KEY', None)
        if self.api_key:
            openai.api_key = self.api_key
        
        # Prompt système pour le contexte tennis
        self.system_prompt = """
You are an AI assistant for Tennis Club Elite, a premium tennis facility. You help members with:

1. TENNIS INFORMATION:
   - Rules, techniques, and strategies
   - Equipment recommendations
   - Training tips and drills
   - Tournament information

2. CLUB SERVICES:
   - Court reservations and availability
   - Coaching services and schedules
   - Equipment rental and pro shop
   - Membership plans and benefits
   - Tournament registration
   - Match requests and finding partners

3. GENERAL ASSISTANCE:
   - Facility information and hours
   - Contact details and directions
   - Event schedules and news
   - Fitness and nutrition advice for tennis

CLUB DETAILS:
- Name: Tennis Club Elite
- Location: 123 Tennis Court Drive, Sports Complex, SC 12345
- Hours: Mon-Thu 6AM-10PM, Fri 6AM-11PM, Sat 7AM-11PM, Sun 7AM-9PM
- Phone: (*************
- Email: <EMAIL>

SERVICES OFFERED:
- 12 professional courts (clay and hard courts)
- Professional coaching (individual and group)
- Equipment rental and pro shop
- Tournaments and leagues
- Fitness training
- Membership programs (Basic $29/month, Premium $79/month, Elite $149/month)

Always be helpful, friendly, and knowledgeable about tennis and the club. If you don't know specific current information (like real-time court availability), suggest the user contact the club directly or check the website/app.
"""

    def get_or_create_conversation(self, user, conversation_id=None):
        """Get existing conversation or create new one"""
        if conversation_id:
            try:
                conversation = ChatConversation.objects.get(
                    id=conversation_id, 
                    user=user, 
                    is_active=True
                )
                return conversation
            except ChatConversation.DoesNotExist:
                pass
        
        # Create new conversation
        conversation = ChatConversation.objects.create(
            user=user,
            title="Tennis Club Chat"
        )
        return conversation

    def get_conversation_history(self, conversation, limit=10):
        """Get recent messages from conversation"""
        messages = conversation.messages.order_by('-timestamp')[:limit]
        
        # Format for OpenAI API
        formatted_messages = [{"role": "system", "content": self.system_prompt}]
        
        for message in reversed(messages):
            formatted_messages.append({
                "role": message.role,
                "content": message.content
            })
        
        return formatted_messages

    def generate_response_openai(self, messages):
        """Generate response using OpenAI GPT"""
        if not self.api_key:
            return self.generate_fallback_response(messages[-1]["content"])
        
        try:
            response = openai.ChatCompletion.create(
                model="gpt-3.5-turbo",
                messages=messages,
                max_tokens=500,
                temperature=0.7,
                top_p=1,
                frequency_penalty=0,
                presence_penalty=0
            )
            
            return {
                'content': response.choices[0].message.content,
                'tokens_used': response.usage.total_tokens
            }
            
        except Exception as e:
            logger.error(f"OpenAI API error: {e}")
            return self.generate_fallback_response(messages[-1]["content"])

    def generate_fallback_response(self, user_message):
        """Generate fallback response when OpenAI is not available"""
        user_message_lower = user_message.lower()
        
        # Tennis-related responses
        if any(word in user_message_lower for word in ['court', 'reservation', 'book']):
            return {
                'content': "🎾 For court reservations, please visit our booking system in the dashboard or call us at (*************. Our courts are available from 6 AM to 10 PM on weekdays.",
                'tokens_used': 0
            }
        
        elif any(word in user_message_lower for word in ['coach', 'lesson', 'training']):
            return {
                'content': "🏆 Our professional coaches offer individual and group lessons. You can book coaching sessions through the dashboard or contact us for more information about our coaching programs.",
                'tokens_used': 0
            }
        
        elif any(word in user_message_lower for word in ['tournament', 'competition']):
            return {
                'content': "🏆 We regularly host tournaments for all skill levels! Check the 'Find Matches' section in your dashboard to see available tournaments and register.",
                'tokens_used': 0
            }
        
        elif any(word in user_message_lower for word in ['membership', 'price', 'cost']):
            return {
                'content': "💳 We offer three membership plans: Basic ($29/month), Premium ($79/month), and Elite ($149/month). Each plan includes different benefits and court time. Visit our Services page for details!",
                'tokens_used': 0
            }
        
        elif any(word in user_message_lower for word in ['hours', 'open', 'time']):
            return {
                'content': "🕐 Our hours: Mon-Thu 6AM-10PM, Fri 6AM-11PM, Sat 7AM-11PM, Sun 7AM-9PM. We're located at 123 Tennis Court Drive, Sports Complex, SC 12345.",
                'tokens_used': 0
            }
        
        elif any(word in user_message_lower for word in ['equipment', 'racquet', 'gear']):
            return {
                'content': "🎾 Our pro shop offers racquet sales and rentals, tennis apparel, and equipment maintenance. We also provide racquet stringing services and expert fitting advice.",
                'tokens_used': 0
            }
        
        else:
            return {
                'content': "👋 Hello! I'm here to help you with Tennis Club Elite. I can assist with court reservations, coaching, tournaments, memberships, and general tennis questions. What would you like to know?",
                'tokens_used': 0
            }

    def chat(self, user, message, conversation_id=None):
        """Main chat function"""
        try:
            # Get or create conversation
            conversation = self.get_or_create_conversation(user, conversation_id)
            
            # Save user message
            user_message = ChatMessage.objects.create(
                conversation=conversation,
                role='user',
                content=message
            )
            
            # Get conversation history
            messages = self.get_conversation_history(conversation)
            
            # Add current user message
            messages.append({"role": "user", "content": message})
            
            # Generate response
            if self.api_key:
                response_data = self.generate_response_openai(messages)
            else:
                response_data = self.generate_fallback_response(message)
            
            # Save assistant response
            assistant_message = ChatMessage.objects.create(
                conversation=conversation,
                role='assistant',
                content=response_data['content'],
                tokens_used=response_data['tokens_used']
            )
            
            # Update conversation title if it's the first exchange
            if conversation.messages.count() == 2:  # user + assistant
                title = message[:50] + "..." if len(message) > 50 else message
                conversation.title = title
                conversation.save()
            
            # Update conversation timestamp
            conversation.updated_at = timezone.now()
            conversation.save()
            
            return {
                'success': True,
                'response': response_data['content'],
                'conversation_id': conversation.id,
                'message_id': assistant_message.id,
                'tokens_used': response_data['tokens_used']
            }
            
        except Exception as e:
            logger.error(f"Chat error: {e}")
            return {
                'success': False,
                'error': str(e),
                'response': "Sorry, I'm having trouble right now. Please try again later or contact our staff directly."
            }

    def get_user_conversations(self, user, limit=10):
        """Get user's recent conversations"""
        conversations = ChatConversation.objects.filter(
            user=user,
            is_active=True
        ).order_by('-updated_at')[:limit]
        
        result = []
        for conv in conversations:
            last_message = conv.messages.last()
            result.append({
                'id': conv.id,
                'title': conv.title,
                'updated_at': conv.updated_at.isoformat(),
                'message_count': conv.messages.count(),
                'last_message': last_message.content[:100] + "..." if last_message and len(last_message.content) > 100 else last_message.content if last_message else ""
            })
        
        return result

# Global chatbot instance
tennis_chatbot = TennisClubChatbot()
