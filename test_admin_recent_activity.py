#!/usr/bin/env python
"""
Test du système Recent Activity amélioré pour l'admin dashboard
"""
import requests
import json

def test_admin_recent_activity():
    print("🎾 Test Recent Activity - Admin Dashboard")
    print("="*45)
    
    # 1. Test login admin
    print("\n1️⃣ Test Login Admin:")
    try:
        login_response = requests.post('http://127.0.0.1:8000/auth/api/login/', json={
            'email': '<EMAIL>',
            'password': 'admin123'
        })
        
        if login_response.status_code == 200:
            print("✅ Login admin réussi")
            access_token = login_response.json().get('access')
            headers = {'Authorization': f'Bearer {access_token}'}
        else:
            print(f"❌ Login admin échoué: {login_response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Erreur login admin: {e}")
        return False
    
    # 2. Test API Recent Activities
    print("\n2️⃣ Test API Recent Activities:")
    try:
        activities_response = requests.get('http://127.0.0.1:8000/res/api/admin/recent-activities/', headers=headers)
        
        if activities_response.status_code == 200:
            activities_data = activities_response.json()
            activities = activities_data.get('activities', [])
            total_count = activities_data.get('total_count', 0)
            
            print(f"✅ API Recent Activities fonctionnelle")
            print(f"   📊 {len(activities)} activités récupérées")
            print(f"   📈 {total_count} activités au total")
            
            # Analyser les types d'activités
            activity_types = {}
            for activity in activities:
                activity_type = activity.get('type', 'unknown')
                activity_types[activity_type] = activity_types.get(activity_type, 0) + 1
            
            print(f"   📋 Types d'activités:")
            for activity_type, count in activity_types.items():
                type_labels = {
                    'court_reservation': '🏟️ Réservations Court',
                    'coach_reservation': '👨‍🏫 Réservations Coach',
                    'user_registration': '👤 Inscriptions Utilisateur',
                    'equipment_order': '🛍️ Commandes Équipement',
                    'payment': '💳 Paiements'
                }
                label = type_labels.get(activity_type, f'❓ {activity_type}')
                print(f"      {label}: {count}")
            
            # Afficher quelques exemples d'activités
            print(f"   🔍 Exemples d'activités récentes:")
            for i, activity in enumerate(activities[:3]):
                print(f"      {i+1}. {activity.get('title')} - {activity.get('description')}")
                print(f"         📅 {activity.get('relative_time')} | 🏷️ {activity.get('type')}")
                
        else:
            print(f"❌ Erreur API Recent Activities: {activities_response.status_code}")
            print(f"   Message: {activities_response.text}")
            return False
    except Exception as e:
        print(f"❌ Erreur test API Recent Activities: {e}")
        return False
    
    # 3. Test Interface Admin Dashboard
    print("\n3️⃣ Test Interface Admin Dashboard:")
    try:
        dashboard_response = requests.get('http://127.0.0.1:8000/auth/admin-dashboard/')
        
        if dashboard_response.status_code == 200:
            html_content = dashboard_response.text
            
            # Vérifier les éléments de l'interface Recent Activity
            checks = [
                ('Recent Activity Section', 'Recent Activity' in html_content),
                ('Activity Container', 'recentActivity' in html_content),
                ('Refresh Button', 'loadRecentActivity()' in html_content),
                ('Activity Styles', 'activity-item' in html_content),
                ('Activity Icons', 'bi-clock-history' in html_content),
                ('API Call', '/res/api/admin/recent-activities/' in html_content),
                ('Activity Type Labels', 'getActivityTypeLabel' in html_content),
                ('Scrollable Container', 'max-height: 500px' in html_content),
                ('Modern Design', 'bg-opacity-10' in html_content),
                ('Error Handling', 'Error loading recent activity' in html_content)
            ]
            
            all_passed = True
            for check_name, check_result in checks:
                if check_result:
                    print(f"   ✅ {check_name}")
                else:
                    print(f"   ❌ {check_name} manquant")
                    all_passed = False
            
            if all_passed:
                print("✅ Interface Recent Activity complète!")
            else:
                print("⚠️  Certains éléments manquent dans l'interface")
        else:
            print(f"❌ Dashboard admin inaccessible: {dashboard_response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Erreur test interface admin: {e}")
        return False
    
    # 4. Test des données d'activité
    print("\n4️⃣ Test Qualité des Données:")
    try:
        if len(activities) > 0:
            sample_activity = activities[0]
            required_fields = ['type', 'icon', 'color', 'title', 'description', 'details', 'timestamp', 'user', 'relative_time']
            
            missing_fields = []
            for field in required_fields:
                if field not in sample_activity:
                    missing_fields.append(field)
            
            if len(missing_fields) == 0:
                print("✅ Structure des données d'activité complète")
                print(f"   📋 Tous les champs requis présents: {', '.join(required_fields)}")
            else:
                print(f"❌ Champs manquants dans les données: {', '.join(missing_fields)}")
                return False
            
            # Vérifier les couleurs et icônes
            valid_colors = ['success', 'info', 'primary', 'warning', 'danger']
            valid_icons = ['bi-calendar-check', 'bi-person-check', 'bi-person-plus', 'bi-bag-check', 'bi-credit-card']
            
            color_valid = sample_activity.get('color') in valid_colors
            icon_valid = sample_activity.get('icon') in valid_icons
            
            if color_valid and icon_valid:
                print("✅ Couleurs et icônes valides")
            else:
                print(f"⚠️  Couleur: {sample_activity.get('color')} | Icône: {sample_activity.get('icon')}")
        else:
            print("⚠️  Aucune activité pour tester la structure des données")
    except Exception as e:
        print(f"❌ Erreur test qualité des données: {e}")
        return False
    
    return True

def show_manual_test_instructions():
    print("\n🎯 INSTRUCTIONS POUR TEST MANUEL:")
    print("="*40)
    print("1. 🌐 Aller sur: http://127.0.0.1:8000/auth/login/")
    print("2. 🔑 Se connecter: <EMAIL> / admin123")
    print("3. 📊 Observer le Dashboard Admin")
    print("4. 🕒 Vérifier la section 'Recent Activity':")
    print("   - Liste des activités récentes avec icônes colorées")
    print("   - Types d'activités: Court, Coach, User, Equipment, Payment")
    print("   - Timestamps relatifs (ex: '2 hours ago')")
    print("   - Badges colorés par type d'activité")
    print("   - Bouton Refresh fonctionnel")
    print("   - Scroll si beaucoup d'activités")
    print("   - Hover effects sur les éléments")
    
    print("\n🎨 FONCTIONNALITÉS À OBSERVER:")
    print("="*35)
    print("✓ Design Moderne:")
    print("  - Icônes colorées avec arrière-plan semi-transparent")
    print("  - Badges avec couleurs par type d'activité")
    print("  - Hover effects sur les éléments")
    print("  - Scrollbar personnalisée")
    print("✓ Informations Complètes:")
    print("  - Titre et description de l'activité")
    print("  - Détails spécifiques (prix, quantité, etc.)")
    print("  - Timestamp relatif (ex: '2 hours ago')")
    print("  - Nom de l'utilisateur impliqué")
    print("✓ Types d'Activités:")
    print("  - 📅 Réservations de courts")
    print("  - 👨‍🏫 Réservations de coachs")
    print("  - 👤 Inscriptions d'utilisateurs")
    print("  - 🛍️ Commandes d'équipement")
    print("  - 💳 Paiements reçus")

if __name__ == '__main__':
    print("🎾 TENNIS MANAGEMENT SYSTEM")
    print("🔧 Test Recent Activity - Admin Dashboard")
    print("="*50)
    
    success = test_admin_recent_activity()
    
    if success:
        print("\n🚀 RECENT ACTIVITY SYSTEM FONCTIONNEL!")
        print("="*40)
        print("✅ API Recent Activities complète")
        print("✅ Interface admin moderne")
        print("✅ Données structurées et complètes")
        print("✅ Design responsive et interactif")
        print("✅ Types d'activités variés")
        print("✅ Timestamps relatifs")
        print("✅ Gestion d'erreurs robuste")
        
        show_manual_test_instructions()
        
        print("\n🎉 FÉLICITATIONS!")
        print("="*20)
        print("Le système Recent Activity est")
        print("ENTIÈREMENT FONCTIONNEL!")
        print("- API backend robuste")
        print("- Interface moderne et intuitive")
        print("- Données en temps réel")
        print("- Design professionnel")
        
    else:
        print("\n⚠️  Des problèmes ont été détectés.")
        print("Vérifiez les erreurs ci-dessus et corrigez-les.")
