#!/usr/bin/env python
"""
Correction définitive du problème de création d'horaires des coachs
"""
import os
import sys
import django
from datetime import datetime, timedelta

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'tennis_manager.settings')
django.setup()

from django.contrib.auth import get_user_model
from reservations.models import Coach, Schedule, ScheduleSlot
from django.test import Client
from django.urls import reverse
import json

User = get_user_model()

def create_admin_user():
    """Créer un utilisateur admin pour les tests"""
    print("🔧 Création d'un utilisateur admin pour les tests...")
    
    try:
        # Supprimer l'ancien admin de test s'il existe
        User.objects.filter(username='schedule_admin').delete()
        
        # Créer un nouvel admin
        admin_user = User.objects.create_user(
            username='schedule_admin',
            email='<EMAIL>',
            password='admin123',
            role='admin',
            is_staff=True,
            is_superuser=True
        )
        
        print(f"   ✅ Admin créé: {admin_user.username} / admin123")
        return admin_user
        
    except Exception as e:
        print(f"   ❌ Erreur création admin: {e}")
        return None

def test_schedule_creation_direct():
    """Test direct de création d'horaires via Django"""
    print("\n🔍 Test direct de création d'horaires...")
    
    # 1. Récupérer un coach
    coach = Coach.objects.first()
    if not coach:
        print("   ❌ Aucun coach trouvé")
        return False
    
    print(f"   👨‍🏫 Coach sélectionné: {coach.name} (ID: {coach.id})")
    
    # 2. Créer un créneau directement
    tomorrow = datetime.now().date() + timedelta(days=1)
    
    try:
        # Vérifier si le créneau existe déjà
        existing = ScheduleSlot.objects.filter(
            coach=coach,
            date=tomorrow,
            start_time='09:00',
            end_time='10:00'
        ).exists()
        
        if existing:
            print("   ⚠️  Créneau déjà existant, utilisons une autre heure")
            start_time = '16:00'
            end_time = '17:00'
        else:
            start_time = '09:00'
            end_time = '10:00'
        
        # Créer le créneau
        slot = ScheduleSlot.objects.create(
            coach=coach,
            date=tomorrow,
            start_time=start_time,
            end_time=end_time,
            is_booked=False
        )
        
        print(f"   ✅ Créneau créé: {slot.date} {slot.start_time}-{slot.end_time}")
        print(f"   📊 ID du créneau: {slot.id}")
        
        # Vérifier que le créneau est bien en base
        verification = ScheduleSlot.objects.filter(id=slot.id).first()
        if verification:
            print(f"   ✅ Créneau vérifié en base de données")
            return True
        else:
            print(f"   ❌ Créneau non trouvé en base après création")
            return False
            
    except Exception as e:
        print(f"   ❌ Erreur création directe: {e}")
        return False

def test_api_with_django_client():
    """Test de l'API avec le client Django"""
    print("\n🔍 Test API avec client Django...")
    
    # 1. Créer un admin
    admin_user = create_admin_user()
    if not admin_user:
        return False
    
    # 2. Créer un client Django
    client = Client()
    
    # 3. Se connecter
    login_success = client.login(username='schedule_admin', password='admin123')
    if not login_success:
        print("   ❌ Échec de connexion avec le client Django")
        return False
    
    print("   ✅ Connexion réussie avec le client Django")
    
    # 4. Récupérer un coach
    coach = Coach.objects.first()
    tomorrow = datetime.now().date() + timedelta(days=1)
    
    # 5. Préparer les données
    schedule_data = {
        'coach_id': coach.id,
        'date': tomorrow.strftime('%Y-%m-%d'),
        'start_time': '11:00',
        'end_time': '12:00'
    }
    
    print(f"   📋 Données: {schedule_data}")
    
    # 6. Appeler l'API
    try:
        response = client.post(
            '/res/api/coach-schedule/create/',
            data=json.dumps(schedule_data),
            content_type='application/json'
        )
        
        print(f"   📥 Status: {response.status_code}")
        print(f"   📥 Response: {response.content.decode()}")
        
        if response.status_code == 201:
            print("   ✅ API fonctionne correctement!")
            
            # Vérifier en base
            new_slot = ScheduleSlot.objects.filter(
                coach=coach,
                date=tomorrow,
                start_time='11:00',
                end_time='12:00'
            ).first()
            
            if new_slot:
                print(f"   ✅ Créneau trouvé en base: {new_slot}")
                return True
            else:
                print("   ❌ Créneau non trouvé en base")
                return False
        
        elif response.status_code == 400:
            response_data = json.loads(response.content.decode())
            error = response_data.get('error', 'Unknown error')
            print(f"   ❌ Erreur de validation: {error}")
            
            if 'already exists' in error:
                print("   🔄 Tentative avec une heure différente...")
                schedule_data['start_time'] = '17:00'
                schedule_data['end_time'] = '18:00'
                
                response2 = client.post(
                    '/res/api/coach-schedule/create/',
                    data=json.dumps(schedule_data),
                    content_type='application/json'
                )
                
                if response2.status_code == 201:
                    print("   ✅ Création réussie avec heure différente!")
                    return True
            
            return False
        
        else:
            print(f"   ❌ Erreur inattendue: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"   ❌ Erreur lors de l'appel API: {e}")
        return False

def check_admin_dashboard_integration():
    """Vérifier l'intégration avec le dashboard admin"""
    print("\n🔍 Vérification de l'intégration dashboard admin...")
    
    # Vérifier que l'endpoint est bien dans les URLs
    try:
        from django.urls import reverse
        url = reverse('create_coach_schedule')
        print(f"   ✅ URL trouvée: {url}")
    except Exception as e:
        print(f"   ❌ URL non trouvée: {e}")
        return False
    
    # Vérifier le code JavaScript du dashboard
    dashboard_file = 'reservations/templates/html/admin_dashboard.html'
    try:
        with open(dashboard_file, 'r', encoding='utf-8') as f:
            content = f.read()
            
        if 'addNewScheduleSlot' in content:
            print("   ✅ Fonction JavaScript trouvée")
        else:
            print("   ❌ Fonction JavaScript manquante")
            return False
            
        if '/res/api/coach-schedule/create/' in content:
            print("   ✅ URL API trouvée dans le dashboard")
        else:
            print("   ❌ URL API manquante dans le dashboard")
            return False
            
        return True
        
    except Exception as e:
        print(f"   ❌ Erreur lecture dashboard: {e}")
        return False

def show_solution_summary():
    """Afficher le résumé de la solution"""
    print("\n🎯 RÉSUMÉ DE LA SOLUTION:")
    print("="*30)
    
    print("✅ PROBLÈME IDENTIFIÉ:")
    print("   - L'API de création d'horaires fonctionne")
    print("   - Le problème était l'authentification dans les tests")
    print("   - Le système stocke bien les horaires en base de données")
    
    print("\n✅ SOLUTION MISE EN PLACE:")
    print("   - API: /res/api/coach-schedule/create/")
    print("   - Authentification: JWT Bearer token")
    print("   - Stockage: Table ScheduleSlot")
    print("   - Interface: Dashboard admin")
    
    print("\n🔧 POUR TESTER MANUELLEMENT:")
    print("   1. Connectez-vous comme admin")
    print("   2. Allez au dashboard admin")
    print("   3. Cliquez sur 'Coach Management'")
    print("   4. Sélectionnez un coach")
    print("   5. Cliquez sur 'View Schedule'")
    print("   6. Utilisez 'Add New Schedule Slot'")
    print("   7. Remplissez: Date, Start Time, End Time")
    print("   8. Cliquez 'Add Schedule Slot'")
    
    print("\n📊 DONNÉES DE TEST DISPONIBLES:")
    coaches = Coach.objects.all()
    for coach in coaches:
        slots_count = ScheduleSlot.objects.filter(coach=coach).count()
        print(f"   - {coach.name}: {slots_count} créneaux")

if __name__ == '__main__':
    print("🎾 TENNIS MANAGEMENT SYSTEM")
    print("🔧 Correction Définitive - Création d'Horaires")
    print("="*55)
    
    # Tests
    test1 = test_schedule_creation_direct()
    test2 = test_api_with_django_client()
    test3 = check_admin_dashboard_integration()
    
    if test1 and test2 and test3:
        print("\n🎉 TOUS LES TESTS RÉUSSIS!")
        print("="*30)
        print("✅ Création directe d'horaires: OK")
        print("✅ API de création d'horaires: OK")
        print("✅ Intégration dashboard admin: OK")
        
        show_solution_summary()
        
        print("\n🚀 LE SYSTÈME FONCTIONNE PARFAITEMENT!")
        print("="*40)
        print("Le problème de stockage des horaires est RÉSOLU.")
        print("Les admins peuvent maintenant créer des horaires")
        print("et ils sont correctement stockés en base de données.")
        
    else:
        print("\n❌ CERTAINS TESTS ONT ÉCHOUÉ")
        print("="*30)
        print(f"Création directe: {'✅' if test1 else '❌'}")
        print(f"API: {'✅' if test2 else '❌'}")
        print(f"Dashboard: {'✅' if test3 else '❌'}")
        
        print("\nVérifiez les erreurs ci-dessus pour plus de détails.")
