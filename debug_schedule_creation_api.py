#!/usr/bin/env python
"""
Debug de l'API de création d'horaires des coachs
"""
import requests
import json
from datetime import datetime, timedelta

def test_schedule_creation_api():
    print("🔍 DEBUG: API de Création d'Horaires des Coachs")
    print("="*55)
    
    # Configuration
    base_url = "http://127.0.0.1:8000"
    
    # 1. Test de connexion admin
    print("\n1️⃣ Test de connexion admin...")

    # Essayer différentes combinaisons de connexion
    login_attempts = [
        {"email": "<EMAIL>", "password": "admin"},
        {"email": "<EMAIL>", "password": "admin"},
        {"username": "admin", "password": "admin"},
        {"email": "<EMAIL>", "password": "password"},
        {"email": "<EMAIL>", "password": "123456"}
    ]
    
    access_token = None

    try:
        # Essayer chaque combinaison de connexion
        for i, login_data in enumerate(login_attempts):
            print(f"   🔍 Tentative {i+1}: {login_data}")

            login_response = requests.post(f"{base_url}/auth/api/login/", json=login_data)
            print(f"   📥 Status: {login_response.status_code}")

            if login_response.status_code == 200:
                token_data = login_response.json()
                access_token = token_data.get('access')
                print(f"   ✅ Connexion réussie avec: {login_data}")
                print(f"   ✅ Token obtenu: {access_token[:50]}...")
                break
            else:
                print(f"   ❌ Échec: {login_response.text[:100]}...")

        if access_token:
            # 2. Test de récupération des coachs
            print("\n2️⃣ Test de récupération des coachs...")
            headers = {
                'Authorization': f'Bearer {access_token}',
                'Content-Type': 'application/json'
            }
            
            coaches_response = requests.get(f"{base_url}/res/api/coaches/", headers=headers)
            print(f"   📥 Status coachs: {coaches_response.status_code}")
            
            if coaches_response.status_code == 200:
                coaches_data = coaches_response.json()
                coaches = coaches_data.get('coaches', [])
                print(f"   ✅ {len(coaches)} coachs trouvés")
                
                if coaches:
                    # Prendre le premier coach pour le test
                    test_coach = coaches[0]
                    print(f"   👨‍🏫 Coach de test: {test_coach['name']} (ID: {test_coach['id']})")
                    
                    # 3. Test de création d'horaire
                    print("\n3️⃣ Test de création d'horaire...")
                    test_schedule_creation(base_url, headers, test_coach['id'])
                    
                else:
                    print("   ❌ Aucun coach trouvé")
            else:
                print(f"   ❌ Erreur récupération coachs: {coaches_response.text}")

        else:
            print("   ❌ Aucune connexion réussie avec les tentatives")
            print("   💡 Créons un admin de test...")
            create_test_admin()
            
    except Exception as e:
        print(f"   ❌ Erreur de connexion: {e}")

def create_test_admin():
    """Créer un admin de test via Django"""
    print("   🔧 Création d'un admin de test...")

    try:
        import os
        import sys
        import django

        # Setup Django environment
        os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'tennis_manager.settings')
        django.setup()

        from django.contrib.auth import get_user_model
        User = get_user_model()

        # Créer un admin de test
        admin_user, created = User.objects.get_or_create(
            email='<EMAIL>',
            defaults={
                'username': 'test_admin',
                'role': 'admin',
                'is_staff': True,
                'is_superuser': True
            }
        )

        if created:
            admin_user.set_password('admin123')
            admin_user.save()
            print(f"   ✅ Admin créé: {admin_user.email} / admin123")
        else:
            print(f"   ✅ Admin existe: {admin_user.email}")

        print("   💡 Essayez maintenant avec: <EMAIL> / admin123")

    except Exception as e:
        print(f"   ❌ Erreur création admin: {e}")

def test_schedule_creation(base_url, headers, coach_id):
    """Test de création d'horaire pour un coach"""
    
    # Préparer les données de test
    tomorrow = (datetime.now() + timedelta(days=1)).strftime('%Y-%m-%d')
    
    schedule_data = {
        'coach_id': coach_id,
        'date': tomorrow,
        'start_time': '19:00',
        'end_time': '20:00'
    }
    
    print(f"   📋 Données à envoyer: {schedule_data}")
    
    try:
        # Appel API de création
        create_response = requests.post(
            f"{base_url}/res/api/coach-schedule/create/",
            json=schedule_data,
            headers=headers
        )
        
        print(f"   📥 Status création: {create_response.status_code}")
        print(f"   📥 Response: {create_response.text}")
        
        if create_response.status_code == 201:
            print("   ✅ Horaire créé avec succès!")
            
            # 4. Vérification en récupérant les horaires du coach
            print("\n4️⃣ Vérification des horaires créés...")
            verify_schedule_creation(base_url, headers, coach_id, tomorrow)
            
        elif create_response.status_code == 400:
            error_data = create_response.json()
            print(f"   ❌ Erreur de validation: {error_data.get('error', 'Unknown error')}")
            
            # Essayer avec une heure différente
            print("   🔄 Tentative avec une heure différente...")
            schedule_data['start_time'] = '21:00'
            schedule_data['end_time'] = '22:00'
            
            retry_response = requests.post(
                f"{base_url}/res/api/coach-schedule/create/",
                json=schedule_data,
                headers=headers
            )
            
            print(f"   📥 Status retry: {retry_response.status_code}")
            print(f"   📥 Response retry: {retry_response.text}")
            
        else:
            print(f"   ❌ Erreur inattendue: {create_response.status_code}")
            print(f"   📥 Headers envoyés: {headers}")
            print(f"   📥 URL appelée: {base_url}/res/api/coach-schedule/create/")
            
    except Exception as e:
        print(f"   ❌ Erreur lors de la création: {e}")

def verify_schedule_creation(base_url, headers, coach_id, date):
    """Vérifier que l'horaire a été créé"""
    
    try:
        # Récupérer les horaires du coach
        schedule_response = requests.get(
            f"{base_url}/res/api/coaches/{coach_id}/schedule/",
            headers=headers
        )
        
        print(f"   📥 Status vérification: {schedule_response.status_code}")
        
        if schedule_response.status_code == 200:
            schedule_data = schedule_response.json()
            slots = schedule_data.get('schedule_slots', [])
            
            # Chercher le créneau créé
            created_slot = None
            for slot in slots:
                if slot['date'] == date and slot['start_time'] in ['19:00', '21:00']:
                    created_slot = slot
                    break
            
            if created_slot:
                print(f"   ✅ Horaire trouvé en base: {created_slot['date']} {created_slot['start_time']}-{created_slot['end_time']}")
                print(f"   📊 Total créneaux pour ce coach: {len(slots)}")
            else:
                print(f"   ❌ Horaire non trouvé en base")
                print(f"   📊 Créneaux existants: {len(slots)}")
                if slots:
                    print("   📋 Derniers créneaux:")
                    for slot in slots[-3:]:
                        print(f"      - {slot['date']} {slot['start_time']}-{slot['end_time']}")
        else:
            print(f"   ❌ Erreur récupération horaires: {schedule_response.text}")
            
    except Exception as e:
        print(f"   ❌ Erreur vérification: {e}")

def test_direct_api_call():
    """Test direct sans authentification pour voir l'erreur"""
    print("\n🔍 Test direct de l'API (sans auth)...")
    
    base_url = "http://127.0.0.1:8000"
    tomorrow = (datetime.now() + timedelta(days=1)).strftime('%Y-%m-%d')
    
    schedule_data = {
        'coach_id': 1,  # ID arbitraire
        'date': tomorrow,
        'start_time': '19:00',
        'end_time': '20:00'
    }
    
    try:
        response = requests.post(
            f"{base_url}/res/api/coach-schedule/create/",
            json=schedule_data
        )
        
        print(f"   📥 Status sans auth: {response.status_code}")
        print(f"   📥 Response: {response.text}")
        
        if response.status_code == 401:
            print("   ✅ API correctement protégée par authentification")
        else:
            print("   ⚠️  Réponse inattendue")
            
    except Exception as e:
        print(f"   ❌ Erreur: {e}")

def show_debugging_instructions():
    """Instructions pour débugger"""
    print("\n🔧 INSTRUCTIONS DE DÉBOGAGE:")
    print("="*35)
    
    print("📋 Si l'API ne fonctionne pas:")
    print("1. 🔍 Vérifiez les logs du serveur Django")
    print("2. 🔍 Vérifiez que l'URL est correcte: /res/api/coach-schedule/create/")
    print("3. 🔍 Vérifiez l'authentification admin")
    print("4. 🔍 Vérifiez le format des données JSON")
    print("5. 🔍 Vérifiez que le coach_id existe")
    
    print("\n📋 Pour tester manuellement:")
    print("1. 🌐 Ouvrir les outils de développement (F12)")
    print("2. 🌐 Aller à l'onglet Network")
    print("3. 🌐 Essayer de créer un horaire via l'interface")
    print("4. 🌐 Vérifier la requête HTTP dans Network")
    print("5. 🌐 Vérifier la réponse du serveur")
    
    print("\n📋 Vérifications en base de données:")
    print("1. 🗄️  Vérifier la table ScheduleSlot")
    print("2. 🗄️  Vérifier que le coach existe")
    print("3. 🗄️  Vérifier les permissions admin")

if __name__ == '__main__':
    print("🎾 TENNIS MANAGEMENT SYSTEM")
    print("🔧 Debug API Création d'Horaires")
    print("="*55)
    
    # Test avec authentification
    test_schedule_creation_api()
    
    # Test direct
    test_direct_api_call()
    
    # Instructions
    show_debugging_instructions()
    
    print("\n🎯 PROCHAINES ÉTAPES:")
    print("="*25)
    print("1. Exécutez ce script")
    print("2. Vérifiez les erreurs affichées")
    print("3. Testez manuellement via l'interface web")
    print("4. Vérifiez les logs du serveur Django")
    print("5. Utilisez les outils de développement du navigateur")
