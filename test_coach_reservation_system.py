#!/usr/bin/env python
"""
Test complet du système de réservation de coach
"""
import os
import sys
import django
import requests
from datetime import datetime, timedelta

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'tennis_manager.settings')
django.setup()

from django.contrib.auth import get_user_model
from reservations.models import Coach, ReservationCoach

User = get_user_model()

def test_coach_booking_system():
    print("🎾 Test du Système de Réservation de Coach")
    print("="*50)
    
    # 1. Test login joueur
    print("\n1️⃣ Test Login Joueur:")
    try:
        login_response = requests.post('http://127.0.0.1:8000/auth/api/login/', json={
            'email': '<EMAIL>',
            'password': 'player123'
        })
        
        if login_response.status_code == 200:
            print("✅ Login joueur réussi")
            access_token = login_response.json().get('access')
            headers = {'Authorization': f'Bearer {access_token}'}
        else:
            print(f"❌ Login joueur échoué: {login_response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Erreur login: {e}")
        return False
    
    # 2. Test consultation des coachs disponibles
    print("\n2️⃣ Test Consultation des Coachs:")
    try:
        coaches_response = requests.get('http://127.0.0.1:8000/res/api/coaches/', headers=headers)
        
        if coaches_response.status_code == 200:
            coaches_data = coaches_response.json()
            coaches = coaches_data.get('coaches', [])
            print(f"✅ {len(coaches)} coachs trouvés")
            
            # Sélectionner un coach pour le test
            available_coach = None
            for coach in coaches:
                if coach['user_linked'] and coach['is_active']:
                    available_coach = coach
                    break
            
            if available_coach:
                print(f"   Coach sélectionné: {available_coach['name']} - ${available_coach['price_per_hour']}/heure")
                coach_id = available_coach['id']
            else:
                print("❌ Aucun coach disponible trouvé")
                return False
        else:
            print(f"❌ Erreur consultation coachs: {coaches_response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Erreur consultation coachs: {e}")
        return False
    
    # 3. Test consultation emploi du temps du coach
    print("\n3️⃣ Test Consultation Emploi du Temps:")
    try:
        availability_response = requests.get(f'http://127.0.0.1:8000/res/api/coach/{coach_id}/availability/')
        
        if availability_response.status_code == 200:
            availability_data = availability_response.json()
            available_slots = availability_data.get('available_slots', [])
            print(f"✅ {len(available_slots)} créneaux disponibles")
            
            if available_slots:
                # Prendre le premier créneau disponible
                selected_slot = available_slots[0]
                print(f"   Créneau sélectionné: {selected_slot['date']} {selected_slot['start_time']}-{selected_slot['end_time']}")
            else:
                print("⚠️  Aucun créneau disponible, création d'un créneau de test")
                # Créer un créneau pour demain
                tomorrow = (datetime.now() + timedelta(days=1)).strftime('%Y-%m-%d')
                selected_slot = {
                    'date': tomorrow,
                    'start_time': '10:00',
                    'end_time': '11:00'
                }
        else:
            print(f"❌ Erreur consultation emploi du temps: {availability_response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Erreur consultation emploi du temps: {e}")
        return False
    
    # 4. Test réservation du coach
    print("\n4️⃣ Test Réservation du Coach:")
    try:
        booking_data = {
            'coach_id': coach_id,
            'date': selected_slot['date'],
            'start_time': selected_slot['start_time'],
            'end_time': selected_slot['end_time']
        }
        
        booking_response = requests.post('http://127.0.0.1:8000/res/api/coach/book-slot/', 
                                       json=booking_data, headers=headers)
        
        if booking_response.status_code in [200, 201]:
            booking_result = booking_response.json()
            reservation_data = booking_result.get('reservation', {})
            print("✅ Réservation coach réussie!")
            print(f"   ID Réservation: {reservation_data.get('id')}")
            print(f"   Coach: {reservation_data.get('coach_name')}")
            print(f"   Date: {reservation_data.get('date')}")
            print(f"   Heure: {reservation_data.get('start_time')}-{reservation_data.get('end_time')}")
            print(f"   Prix: ${reservation_data.get('total_price')}")
            
            reservation_id = reservation_data.get('id')
        else:
            print(f"❌ Erreur réservation: {booking_response.status_code}")
            print(f"   Message: {booking_response.text}")
            return False
    except Exception as e:
        print(f"❌ Erreur réservation: {e}")
        return False
    
    # 5. Test dashboard stats (vérifier que les sessions de coach sont comptées)
    print("\n5️⃣ Test Dashboard Stats:")
    try:
        stats_response = requests.get('http://127.0.0.1:8000/res/api/dashboard/stats/', headers=headers)
        
        if stats_response.status_code == 200:
            stats = stats_response.json()
            coach_sessions = stats.get('coach_sessions', 0)
            court_reservations = stats.get('court_reservations', 0)
            equipment_orders = stats.get('equipment_orders', 0)
            
            print("✅ Dashboard stats récupérées:")
            print(f"   Sessions coach: {coach_sessions}")
            print(f"   Réservations court: {court_reservations}")
            print(f"   Commandes équipement: {equipment_orders}")
            
            if coach_sessions > 0:
                print("✅ Les sessions de coach sont bien comptées!")
            else:
                print("⚠️  Les sessions de coach ne sont pas comptées")
        else:
            print(f"❌ Erreur dashboard stats: {stats_response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Erreur dashboard stats: {e}")
        return False
    
    # 6. Test upcoming reservations (vérifier que les réservations de coach apparaissent)
    print("\n6️⃣ Test Upcoming Reservations:")
    try:
        upcoming_response = requests.get('http://127.0.0.1:8000/res/api/player/upcoming-reservations/', headers=headers)
        
        if upcoming_response.status_code == 200:
            upcoming_data = upcoming_response.json()
            upcoming_reservations = upcoming_data.get('upcoming_reservations', [])
            
            print(f"✅ {len(upcoming_reservations)} réservations à venir trouvées")
            
            # Vérifier si notre réservation de coach est présente
            coach_reservation_found = False
            for reservation in upcoming_reservations:
                if reservation.get('type') == 'coach' and reservation.get('id') == reservation_id:
                    coach_reservation_found = True
                    print(f"✅ Réservation coach trouvée: {reservation.get('title')}")
                    break
            
            if not coach_reservation_found:
                print("⚠️  Réservation coach non trouvée dans les upcoming reservations")
                # Afficher toutes les réservations pour debug
                for reservation in upcoming_reservations:
                    print(f"   - {reservation.get('type')}: {reservation.get('title')}")
        else:
            print(f"❌ Erreur upcoming reservations: {upcoming_response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Erreur upcoming reservations: {e}")
        return False
    
    # 7. Test dashboard access (vérifier que la page se charge)
    print("\n7️⃣ Test Dashboard Access:")
    try:
        dashboard_response = requests.get('http://127.0.0.1:8000/auth/joueur-dashboard/')
        
        if dashboard_response.status_code == 200:
            print("✅ Dashboard joueur accessible")
            
            # Vérifier que les fonctions de réservation coach sont présentes
            html_content = dashboard_response.text
            checks = [
                ('confirmBooking function', 'confirmBooking' in html_content),
                ('Coach reservations section', 'coach-reservations' in html_content),
                ('Coach sessions counter', 'totalCoachSessions' in html_content),
                ('Update/Delete buttons', 'updateReservation' in html_content and 'deleteReservation' in html_content)
            ]
            
            for check_name, check_result in checks:
                if check_result:
                    print(f"   ✅ {check_name}")
                else:
                    print(f"   ❌ {check_name} manquant")
        else:
            print(f"❌ Dashboard inaccessible: {dashboard_response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Erreur dashboard access: {e}")
        return False
    
    print("\n🎉 RÉSUMÉ DU TEST:")
    print("✅ Consultation des coachs disponibles")
    print("✅ Consultation de l'emploi du temps")
    print("✅ Réservation d'une session de coach")
    print("✅ Comptage des sessions dans le dashboard")
    print("✅ Affichage dans les upcoming reservations")
    print("✅ Boutons update/delete disponibles")
    
    return True

if __name__ == '__main__':
    success = test_coach_booking_system()
    
    if success:
        print("\n🚀 SYSTÈME DE RÉSERVATION DE COACH FONCTIONNEL!")
        print("\n🎯 Instructions pour tester manuellement:")
        print("1. Aller sur: http://127.0.0.1:8000/auth/login/")
        print("2. Se connecter: <EMAIL> / player123")
        print("3. Aller dans Coach Reservations")
        print("4. Sélectionner un coach et consulter son emploi du temps")
        print("5. Réserver un créneau")
        print("6. Vérifier que la réservation apparaît dans Dashboard et Upcoming Reservations")
        print("7. Tester les boutons Update/Delete")
    else:
        print("\n⚠️  Des problèmes ont été détectés dans le système.")
