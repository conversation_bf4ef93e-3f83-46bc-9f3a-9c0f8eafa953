#!/usr/bin/env python
"""
Script to test coach user linking and retrieval
"""
import os
import sys
import django
import requests

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'tennis_manager.settings')
django.setup()

from django.contrib.auth import get_user_model
from reservations.models import Coach

User = get_user_model()

def test_coach_user_linking():
    print("🧪 Testing Coach User Linking...")
    
    try:
        # Check all users with coach role
        coach_users = User.objects.filter(role='coach')
        print(f"Found {coach_users.count()} users with coach role:")
        
        for user in coach_users:
            print(f"   👤 {user.username} ({user.email})")
            
            # Check if they have a coach profile
            try:
                if hasattr(user, 'coach_profile'):
                    coach = user.coach_profile
                    print(f"      ✅ Linked to Coach: {coach.name} (${coach.price_per_hour}/hour)")
                else:
                    print(f"      ❌ No coach profile linked")
            except Exception as e:
                print(f"      ❌ Error checking coach profile: {e}")
        
        # Check all coaches
        all_coaches = Coach.objects.all()
        print(f"\n📋 All coaches in database ({all_coaches.count()}):")
        
        for coach in all_coaches:
            user_info = ""
            if coach.user:
                user_info = f" (User: {coach.user.username}, Role: {coach.user.role})"
            else:
                user_info = " (No user linked)"
            
            print(f"   🎾 {coach.name} - ${coach.price_per_hour}/hour{user_info}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing coach user linking: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_coach_registration():
    print("\n🧪 Testing New Coach Registration...")
    
    try:
        # Test registering a new coach user
        test_email = "<EMAIL>"
        
        # Clean up any existing test user
        User.objects.filter(email=test_email).delete()
        Coach.objects.filter(email=test_email).delete()
        
        # Register new coach user
        registration_data = {
            'username': 'test_coach_new',
            'email': test_email,
            'password': 'testpass123',
            'role': 'coach'
        }
        
        response = requests.post('http://127.0.0.1:8000/auth/api/register/', json=registration_data)
        print(f"Registration Status: {response.status_code}")
        
        if response.status_code == 201:
            print("✅ Coach user registered successfully")
            
            # Check if coach profile was automatically created
            user = User.objects.get(email=test_email)
            print(f"   User created: {user.username} (Role: {user.role})")
            
            # Check for coach profile
            try:
                if hasattr(user, 'coach_profile'):
                    coach = user.coach_profile
                    print(f"   ✅ Coach profile auto-created: {coach.name}")
                    print(f"   Price: ${coach.price_per_hour}/hour")
                    print(f"   Experience: {coach.experience} years")
                    return True
                else:
                    print("   ❌ Coach profile not auto-created")
                    return False
            except Exception as e:
                print(f"   ❌ Error checking coach profile: {e}")
                return False
        else:
            print(f"❌ Registration failed: {response.text}")
            return False
        
    except Exception as e:
        print(f"❌ Error testing coach registration: {e}")
        return False

def test_coach_api_access():
    print("\n🧪 Testing Coach API Access...")
    
    try:
        # Test login with existing coach
        login_response = requests.post('http://127.0.0.1:8000/auth/api/login/', json={
            'email': '<EMAIL>',
            'password': 'coach123'
        })
        
        if login_response.status_code != 200:
            print(f"❌ Coach login failed: {login_response.status_code}")
            return False
        
        access_token = login_response.json().get('access')
        headers = {'Authorization': f'Bearer {access_token}'}
        
        # Test coach schedule API
        schedule_response = requests.get('http://127.0.0.1:8000/res/api/coach/schedule/', headers=headers)
        print(f"Coach Schedule API Status: {schedule_response.status_code}")
        
        if schedule_response.status_code == 200:
            print("✅ Coach can access schedule API")
            schedule_data = schedule_response.json()
            print(f"   Weekly schedules: {len(schedule_data.get('weekly_schedule', []))}")
            print(f"   Upcoming slots: {len(schedule_data.get('upcoming_slots', []))}")
            return True
        else:
            print(f"❌ Coach schedule API failed: {schedule_response.text}")
            return False
        
    except Exception as e:
        print(f"❌ Error testing coach API access: {e}")
        return False

def test_coaches_list_api():
    print("\n🧪 Testing Coaches List API...")

    try:
        # Test login first
        login_response = requests.post('http://127.0.0.1:8000/auth/api/login/', json={
            'email': '<EMAIL>',
            'password': 'coach123'
        })

        if login_response.status_code != 200:
            print(f"❌ Login failed: {login_response.status_code}")
            return False

        access_token = login_response.json().get('access')
        headers = {'Authorization': f'Bearer {access_token}'}

        # Test coaches list API
        coaches_response = requests.get('http://127.0.0.1:8000/res/api/coaches/', headers=headers)
        print(f"Coaches List API Status: {coaches_response.status_code}")

        if coaches_response.status_code == 200:
            print("✅ Coaches list API working")
            coaches_data = coaches_response.json()

            total_coaches = coaches_data.get('total_coaches', 0)
            linked_coaches = coaches_data.get('linked_coaches', 0)
            unlinked_coaches = coaches_data.get('unlinked_coaches', 0)

            print(f"   Total coaches: {total_coaches}")
            print(f"   Linked to users: {linked_coaches}")
            print(f"   Not linked: {unlinked_coaches}")

            # Show some coach details
            coaches = coaches_data.get('coaches', [])
            print(f"\n   📋 Coach Details:")
            for coach in coaches[:3]:  # Show first 3
                user_status = "✅ Linked" if coach['user_linked'] else "❌ Not linked"
                print(f"      🎾 {coach['name']} - ${coach['price_per_hour']}/hour ({user_status})")

            return total_coaches > 0 and linked_coaches > 0
        else:
            print(f"❌ Coaches list API failed: {coaches_response.text}")
            return False

    except Exception as e:
        print(f"❌ Error testing coaches list API: {e}")
        return False

def test_coach_availability_api():
    print("\n🧪 Testing Coach Availability API...")

    try:
        # Get a coach ID
        coach = Coach.objects.filter(user__isnull=False).first()
        if not coach:
            print("❌ No coaches with linked users found")
            return False

        # Test coach availability API (no auth required for this endpoint)
        availability_response = requests.get(f'http://127.0.0.1:8000/res/api/coach/{coach.id}/availability/')
        print(f"Coach Availability API Status: {availability_response.status_code}")

        if availability_response.status_code == 200:
            print("✅ Coach availability API working")
            availability_data = availability_response.json()
            coach_info = availability_data.get('coach', {})
            print(f"   Coach: {coach_info.get('name')} (${coach_info.get('price_per_hour')}/hour)")
            print(f"   Available slots: {len(availability_data.get('available_slots', []))}")
            return True
        else:
            print(f"❌ Coach availability API failed: {availability_response.text}")
            return False

    except Exception as e:
        print(f"❌ Error testing coach availability API: {e}")
        return False

if __name__ == '__main__':
    print("🎾 Tennis Management System - Coach User Linking Test")
    print("="*60)
    
    # Run all tests
    test1 = test_coach_user_linking()
    test2 = test_coach_registration()
    test3 = test_coach_api_access()
    test4 = test_coaches_list_api()
    test5 = test_coach_availability_api()

    if all([test1, test2, test3, test4, test5]):
        print("\n🚀 All coach user linking tests passed!")
        print("✅ Coach users are properly linked to Coach profiles.")
        print("✅ New coach registrations automatically create Coach profiles.")
        print("✅ Coach APIs work with linked users.")
        print("✅ Coach availability system is functional.")
    else:
        print("\n⚠️  Some issues found. Check the specific errors above.")
        
    print("\n🎯 Coach users can now:")
    print("   • Register and automatically get Coach profiles")
    print("   • Access coach dashboard and APIs")
    print("   • Be available for player reservations")
    print("   • Manage their schedules and availability")
