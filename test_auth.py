#!/usr/bin/env python
"""
Script to test authentication and create valid tokens
"""
import os
import sys
import django

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'tennis_manager.settings')
django.setup()

from django.contrib.auth import get_user_model
from rest_framework_simplejwt.tokens import RefreshToken
import requests

User = get_user_model()

def create_test_tokens():
    print("🔑 Creating test tokens for authentication...")
    
    # Get test users
    users_to_test = [
        ('admin', 'admin'),
        ('player_alice', 'joueur'),
        ('coach_john', 'coach')
    ]
    
    tokens = {}
    
    for username, expected_role in users_to_test:
        try:
            user = User.objects.get(username=username)
            print(f"\n👤 User: {username}")
            print(f"   Role: {user.role}")
            print(f"   Expected: {expected_role}")
            
            if user.role == expected_role:
                # Create JWT tokens
                refresh = RefreshToken.for_user(user)
                access_token = str(refresh.access_token)
                refresh_token = str(refresh)
                
                tokens[username] = {
                    'access': access_token,
                    'refresh': refresh_token,
                    'role': user.role
                }
                
                print(f"   ✅ Token created successfully")
                print(f"   Access Token: {access_token[:50]}...")
            else:
                print(f"   ❌ Role mismatch!")
                
        except User.DoesNotExist:
            print(f"   ❌ User {username} not found!")
    
    return tokens

def test_api_endpoints(tokens):
    print("\n🌐 Testing API endpoints with tokens...")
    
    base_url = "http://127.0.0.1:8000"
    
    endpoints_to_test = [
        ("/res/coaches/", "GET"),
        ("/res/api/equipment/", "GET"),
        ("/res/api/dashboard/stats/", "GET"),
        ("/res/api/terrains/", "GET"),
    ]
    
    for username, token_data in tokens.items():
        print(f"\n👤 Testing with {username} ({token_data['role']}):")
        
        headers = {
            'Authorization': f'Bearer {token_data["access"]}',
            'Content-Type': 'application/json'
        }
        
        for endpoint, method in endpoints_to_test:
            try:
                if method == "GET":
                    response = requests.get(f"{base_url}{endpoint}", headers=headers, timeout=5)
                else:
                    response = requests.post(f"{base_url}{endpoint}", headers=headers, timeout=5)
                
                if response.status_code == 200:
                    print(f"   ✅ {endpoint} - Status: {response.status_code}")
                elif response.status_code == 401:
                    print(f"   🔒 {endpoint} - Status: {response.status_code} (Unauthorized)")
                elif response.status_code == 403:
                    print(f"   🚫 {endpoint} - Status: {response.status_code} (Forbidden)")
                else:
                    print(f"   ⚠️  {endpoint} - Status: {response.status_code}")
                    
            except requests.exceptions.ConnectionError:
                print(f"   ❌ {endpoint} - Server not running")
            except Exception as e:
                print(f"   ❌ {endpoint} - Error: {e}")

def generate_frontend_tokens():
    print("\n📋 Frontend Token Configuration:")
    print("="*60)
    
    try:
        # Get player token for frontend testing
        player = User.objects.get(username='player_alice')
        refresh = RefreshToken.for_user(player)
        access_token = str(refresh.access_token)
        
        print("🎾 Player Dashboard Token:")
        print(f"Username: player_alice")
        print(f"Password: player123")
        print(f"Role: {player.role}")
        print(f"Token: {access_token}")
        
        print("\n📝 To fix the frontend authentication:")
        print("1. Login with: player_alice / player123")
        print("2. Or manually set token in browser console:")
        print(f"   localStorage.setItem('token', '{access_token}');")
        print(f"   localStorage.setItem('role', '{player.role}');")
        print(f"   localStorage.setItem('username', 'player_alice');")
        print("3. Refresh the page")
        
        return access_token
        
    except User.DoesNotExist:
        print("❌ Player user not found!")
        return None

if __name__ == '__main__':
    print("🎾 Tennis Management System - Authentication Test")
    print("="*60)
    
    # Create tokens
    tokens = create_test_tokens()
    
    # Test API endpoints
    if tokens:
        test_api_endpoints(tokens)
    
    # Generate frontend configuration
    generate_frontend_tokens()
    
    print("\n🚀 Authentication test completed!")
    print("💡 If you're getting 401 errors, use the token configuration above.")
