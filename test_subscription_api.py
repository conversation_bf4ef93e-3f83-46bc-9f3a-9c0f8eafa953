#!/usr/bin/env python
"""
Script to test subscription API endpoints
"""
import os
import sys
import django
import requests
import json

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'tennis_manager.settings')
django.setup()

from django.contrib.auth import get_user_model
from django.test import Client
from django.urls import reverse

User = get_user_model()

def test_subscription_api():
    print("🧪 Testing Subscription API Endpoints...")
    
    # Get test user
    try:
        user = User.objects.filter(username='player_alice').first()
        if not user:
            print("❌ Test user 'player_alice' not found")
            return False
        
        print(f"✅ Found test user: {user.username}")
        
        # Create a test client
        client = Client()
        
        # Login to get JWT token
        login_response = client.post('/auth/api/login/', {
            'username': 'player_alice',
            'password': 'player123'
        })
        
        if login_response.status_code != 200:
            print(f"❌ Login failed: {login_response.status_code}")
            return False
        
        token_data = json.loads(login_response.content)
        access_token = token_data['access']
        print("✅ Login successful, got JWT token")
        
        # Test 1: Get subscription plans
        print("\n1️⃣ Testing GET /res/api/subscriptions/plans/")
        headers = {'HTTP_AUTHORIZATION': f'Bearer {access_token}'}
        
        plans_response = client.get('/res/api/subscriptions/plans/', **headers)
        print(f"   Status: {plans_response.status_code}")
        
        if plans_response.status_code == 200:
            plans_data = json.loads(plans_response.content)
            print(f"   ✅ Found {len(plans_data['plans'])} subscription plans")
        else:
            print(f"   ❌ Failed to get plans: {plans_response.content}")
        
        # Test 2: Get current subscription
        print("\n2️⃣ Testing GET /res/api/subscriptions/")
        sub_response = client.get('/res/api/subscriptions/', **headers)
        print(f"   Status: {sub_response.status_code}")
        
        if sub_response.status_code == 200:
            sub_data = json.loads(sub_response.content)
            print(f"   ✅ Current subscription: {sub_data}")
        else:
            print(f"   ❌ Failed to get subscription: {sub_response.content}")
        
        # Test 3: Create subscription
        print("\n3️⃣ Testing POST /res/api/subscriptions/")
        
        subscription_data = {
            'plan_type': 'basic',
            'duration_months': 1
        }
        
        create_response = client.post(
            '/res/api/subscriptions/',
            data=json.dumps(subscription_data),
            content_type='application/json',
            **headers
        )
        
        print(f"   Status: {create_response.status_code}")
        print(f"   Response: {create_response.content}")
        
        if create_response.status_code == 200:
            print("   ✅ Subscription created successfully")
        else:
            print(f"   ❌ Failed to create subscription")
            # Try to get more details about the error
            if hasattr(create_response, 'content'):
                try:
                    error_data = json.loads(create_response.content)
                    print(f"   Error details: {error_data}")
                except:
                    print(f"   Raw error: {create_response.content}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error during API testing: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == '__main__':
    print("🎾 Tennis Management System - Subscription API Test")
    print("="*60)
    
    success = test_subscription_api()
    
    if success:
        print("\n🚀 API testing completed!")
    else:
        print("\n⚠️  API testing failed. Check the errors above.")
