{% load static %}
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Membership Plans - Elite Sports Club</title>
    
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.0/font/bootstrap-icons.css" rel="stylesheet">
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <style>
        :root {
            --primary-color: #6366f1;
            --secondary-color: #10b981;
            --accent-color: #f59e0b;
            --danger-color: #ef4444;
            --gray-50: #f9fafb;
            --gray-100: #f3f4f6;
            --gray-200: #e5e7eb;
            --gray-600: #4b5563;
            --gray-700: #374151;
            --gray-800: #1f2937;
        }

        body {
            font-family: 'Inter', sans-serif;
            background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
            color: var(--gray-700);
            padding-top: 80px;
        }

        .membership-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 2rem;
        }

        .page-header {
            text-align: center;
            margin-bottom: 3rem;
        }

        .page-title {
            font-family: 'Poppins', sans-serif;
            font-size: 3rem;
            font-weight: 700;
            color: var(--gray-800);
            margin-bottom: 1rem;
        }

        .page-subtitle {
            font-size: 1.25rem;
            color: var(--gray-600);
            max-width: 600px;
            margin: 0 auto;
        }

        .billing-toggle {
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 1rem;
            margin: 2rem 0;
            background: white;
            padding: 1rem;
            border-radius: 16px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            border: 1px solid var(--gray-100);
        }

        .toggle-switch {
            position: relative;
            width: 60px;
            height: 30px;
            background: var(--gray-200);
            border-radius: 15px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .toggle-switch.active {
            background: var(--primary-color);
        }

        .toggle-slider {
            position: absolute;
            top: 3px;
            left: 3px;
            width: 24px;
            height: 24px;
            background: white;
            border-radius: 50%;
            transition: all 0.3s ease;
        }

        .toggle-switch.active .toggle-slider {
            transform: translateX(30px);
        }

        .toggle-label {
            font-weight: 500;
            color: var(--gray-700);
        }

        .toggle-label.active {
            color: var(--primary-color);
        }

        .savings-badge {
            background: var(--secondary-color);
            color: white;
            padding: 0.25rem 0.75rem;
            border-radius: 20px;
            font-size: 0.75rem;
            font-weight: 600;
            margin-left: 0.5rem;
        }

        .plans-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
            gap: 2rem;
            margin-top: 2rem;
        }

        .plan-card {
            background: white;
            border-radius: 20px;
            padding: 2rem;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
            border: 2px solid var(--gray-100);
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .plan-card:hover {
            transform: translateY(-8px);
            box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
        }

        .plan-card.featured {
            border-color: var(--primary-color);
            transform: scale(1.05);
        }

        .plan-card.featured::before {
            content: 'Most Popular';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            background: var(--primary-color);
            color: white;
            text-align: center;
            padding: 0.5rem;
            font-size: 0.875rem;
            font-weight: 600;
        }

        .plan-header {
            text-align: center;
            margin-bottom: 2rem;
            padding-top: 1rem;
        }

        .plan-icon {
            width: 64px;
            height: 64px;
            margin: 0 auto 1rem;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2rem;
            color: white;
        }

        .plan-icon.basic {
            background: linear-gradient(135deg, var(--gray-600), var(--gray-700));
        }

        .plan-icon.premium {
            background: linear-gradient(135deg, var(--primary-color), #8b5cf6);
        }

        .plan-icon.elite {
            background: linear-gradient(135deg, var(--accent-color), #d97706);
        }

        .plan-name {
            font-size: 1.5rem;
            font-weight: 700;
            color: var(--gray-800);
            margin-bottom: 0.5rem;
        }

        .plan-description {
            color: var(--gray-600);
            font-size: 0.875rem;
        }

        .plan-pricing {
            text-align: center;
            margin-bottom: 2rem;
        }

        .plan-price {
            font-size: 3rem;
            font-weight: 700;
            color: var(--gray-800);
            line-height: 1;
        }

        .plan-currency {
            font-size: 1.5rem;
            vertical-align: top;
        }

        .plan-period {
            color: var(--gray-600);
            font-size: 1rem;
            margin-left: 0.5rem;
        }

        .plan-original-price {
            color: var(--gray-500);
            text-decoration: line-through;
            font-size: 1.25rem;
            margin-top: 0.5rem;
        }

        .plan-features {
            list-style: none;
            padding: 0;
            margin-bottom: 2rem;
        }

        .plan-features li {
            display: flex;
            align-items: center;
            gap: 0.75rem;
            padding: 0.75rem 0;
            border-bottom: 1px solid var(--gray-100);
        }

        .plan-features li:last-child {
            border-bottom: none;
        }

        .feature-icon {
            width: 20px;
            height: 20px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 0.75rem;
            color: white;
            flex-shrink: 0;
        }

        .feature-icon.included {
            background: var(--secondary-color);
        }

        .feature-icon.not-included {
            background: var(--gray-400);
        }

        .feature-text {
            font-size: 0.875rem;
            color: var(--gray-700);
        }

        .feature-text.not-included {
            color: var(--gray-500);
        }

        .plan-button {
            width: 100%;
            padding: 1rem;
            border: none;
            border-radius: 12px;
            font-weight: 600;
            font-size: 1rem;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .plan-button.basic {
            background: var(--gray-700);
            color: white;
        }

        .plan-button.basic:hover {
            background: var(--gray-800);
        }

        .plan-button.premium {
            background: var(--primary-color);
            color: white;
        }

        .plan-button.premium:hover {
            background: #5856eb;
        }

        .plan-button.elite {
            background: var(--accent-color);
            color: white;
        }

        .plan-button.elite:hover {
            background: #d97706;
        }

        .features-comparison {
            background: white;
            border-radius: 16px;
            padding: 2rem;
            margin-top: 3rem;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
            border: 1px solid var(--gray-100);
        }

        .comparison-title {
            text-align: center;
            font-size: 2rem;
            font-weight: 700;
            color: var(--gray-800);
            margin-bottom: 2rem;
        }

        .comparison-table {
            width: 100%;
            border-collapse: collapse;
        }

        .comparison-table th,
        .comparison-table td {
            padding: 1rem;
            text-align: center;
            border-bottom: 1px solid var(--gray-100);
        }

        .comparison-table th {
            background: var(--gray-50);
            font-weight: 600;
            color: var(--gray-800);
        }

        .comparison-table td:first-child {
            text-align: left;
            font-weight: 500;
        }

        /* Responsive */
        @media (max-width: 768px) {
            .membership-container {
                padding: 1rem;
            }
            
            .page-title {
                font-size: 2rem;
            }
            
            .plans-grid {
                grid-template-columns: 1fr;
            }
            
            .plan-card.featured {
                transform: none;
            }
            
            .comparison-table {
                font-size: 0.875rem;
            }
        }

        /* Animation */
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .animate-in {
            animation: fadeInUp 0.6s ease-out forwards;
        }
    </style>
</head>
<body>
    <!-- Include Navbar -->
    {% include 'html/navbar.html' %}

    <div class="membership-container">
        <!-- Page Header -->
        <div class="page-header animate-in">
            <h1 class="page-title">Choose Your Membership</h1>
            <p class="page-subtitle">Join Elite Sports Club and unlock your potential with our comprehensive training programs and world-class facilities</p>
        </div>

        <!-- Billing Toggle -->
        <div class="billing-toggle animate-in" style="animation-delay: 0.1s;">
            <span class="toggle-label" id="monthlyLabel">Monthly</span>
            <div class="toggle-switch" id="billingToggle">
                <div class="toggle-slider"></div>
            </div>
            <span class="toggle-label" id="yearlyLabel">Yearly</span>
            <span class="savings-badge">Save 20%</span>
        </div>

        <!-- Plans Grid -->
        <div class="plans-grid">
            <!-- Basic Plan -->
            <div class="plan-card animate-in" style="animation-delay: 0.2s;">
                <div class="plan-header">
                    <div class="plan-icon basic">
                        <i class="bi bi-person"></i>
                    </div>
                    <h3 class="plan-name">Basic</h3>
                    <p class="plan-description">Perfect for beginners getting started</p>
                </div>
                
                <div class="plan-pricing">
                    <div class="plan-price">
                        <span class="plan-currency">$</span><span id="basicPrice">49</span>
                        <span class="plan-period">/month</span>
                    </div>
                    <div class="plan-original-price" id="basicOriginal" style="display: none;">$59/month</div>
                </div>
                
                <ul class="plan-features">
                    <li>
                        <div class="feature-icon included"><i class="bi bi-check"></i></div>
                        <span class="feature-text">Access to basic facilities</span>
                    </li>
                    <li>
                        <div class="feature-icon included"><i class="bi bi-check"></i></div>
                        <span class="feature-text">2 group training sessions/week</span>
                    </li>
                    <li>
                        <div class="feature-icon included"><i class="bi bi-check"></i></div>
                        <span class="feature-text">Equipment rental included</span>
                    </li>
                    <li>
                        <div class="feature-icon not-included"><i class="bi bi-x"></i></div>
                        <span class="feature-text not-included">Personal training</span>
                    </li>
                    <li>
                        <div class="feature-icon not-included"><i class="bi bi-x"></i></div>
                        <span class="feature-text not-included">Premium courts access</span>
                    </li>
                </ul>
                
                <button class="plan-button basic">Get Started</button>
            </div>

            <!-- Premium Plan (Featured) -->
            <div class="plan-card featured animate-in" style="animation-delay: 0.3s;">
                <div class="plan-header">
                    <div class="plan-icon premium">
                        <i class="bi bi-star"></i>
                    </div>
                    <h3 class="plan-name">Premium</h3>
                    <p class="plan-description">Most popular choice for serious athletes</p>
                </div>
                
                <div class="plan-pricing">
                    <div class="plan-price">
                        <span class="plan-currency">$</span><span id="premiumPrice">99</span>
                        <span class="plan-period">/month</span>
                    </div>
                    <div class="plan-original-price" id="premiumOriginal" style="display: none;">$119/month</div>
                </div>
                
                <ul class="plan-features">
                    <li>
                        <div class="feature-icon included"><i class="bi bi-check"></i></div>
                        <span class="feature-text">All basic features</span>
                    </li>
                    <li>
                        <div class="feature-icon included"><i class="bi bi-check"></i></div>
                        <span class="feature-text">Unlimited group sessions</span>
                    </li>
                    <li>
                        <div class="feature-icon included"><i class="bi bi-check"></i></div>
                        <span class="feature-text">2 personal training sessions/month</span>
                    </li>
                    <li>
                        <div class="feature-icon included"><i class="bi bi-check"></i></div>
                        <span class="feature-text">Premium courts access</span>
                    </li>
                    <li>
                        <div class="feature-icon included"><i class="bi bi-check"></i></div>
                        <span class="feature-text">Nutrition consultation</span>
                    </li>
                </ul>
                
                <button class="plan-button premium">Choose Premium</button>
            </div>

            <!-- Elite Plan -->
            <div class="plan-card animate-in" style="animation-delay: 0.4s;">
                <div class="plan-header">
                    <div class="plan-icon elite">
                        <i class="bi bi-trophy"></i>
                    </div>
                    <h3 class="plan-name">Elite</h3>
                    <p class="plan-description">Ultimate experience for champions</p>
                </div>
                
                <div class="plan-pricing">
                    <div class="plan-price">
                        <span class="plan-currency">$</span><span id="elitePrice">199</span>
                        <span class="plan-period">/month</span>
                    </div>
                    <div class="plan-original-price" id="eliteOriginal" style="display: none;">$239/month</div>
                </div>
                
                <ul class="plan-features">
                    <li>
                        <div class="feature-icon included"><i class="bi bi-check"></i></div>
                        <span class="feature-text">All premium features</span>
                    </li>
                    <li>
                        <div class="feature-icon included"><i class="bi bi-check"></i></div>
                        <span class="feature-text">Unlimited personal training</span>
                    </li>
                    <li>
                        <div class="feature-icon included"><i class="bi bi-check"></i></div>
                        <span class="feature-text">VIP courts & facilities</span>
                    </li>
                    <li>
                        <div class="feature-icon included"><i class="bi bi-check"></i></div>
                        <span class="feature-text">Competition entry fees included</span>
                    </li>
                    <li>
                        <div class="feature-icon included"><i class="bi bi-check"></i></div>
                        <span class="feature-text">24/7 facility access</span>
                    </li>
                </ul>
                
                <button class="plan-button elite">Go Elite</button>
            </div>
        </div>

        <!-- Features Comparison -->
        <div class="features-comparison animate-in" style="animation-delay: 0.5s;">
            <h2 class="comparison-title">Compare All Features</h2>
            <table class="comparison-table">
                <thead>
                    <tr>
                        <th>Features</th>
                        <th>Basic</th>
                        <th>Premium</th>
                        <th>Elite</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>Facility Access</td>
                        <td>Basic</td>
                        <td>Premium</td>
                        <td>VIP</td>
                    </tr>
                    <tr>
                        <td>Group Training</td>
                        <td>2/week</td>
                        <td>Unlimited</td>
                        <td>Unlimited</td>
                    </tr>
                    <tr>
                        <td>Personal Training</td>
                        <td>❌</td>
                        <td>2/month</td>
                        <td>Unlimited</td>
                    </tr>
                    <tr>
                        <td>Equipment Rental</td>
                        <td>✅</td>
                        <td>✅</td>
                        <td>✅</td>
                    </tr>
                    <tr>
                        <td>Nutrition Consultation</td>
                        <td>❌</td>
                        <td>✅</td>
                        <td>✅</td>
                    </tr>
                    <tr>
                        <td>Competition Entries</td>
                        <td>❌</td>
                        <td>Discounted</td>
                        <td>Included</td>
                    </tr>
                    <tr>
                        <td>24/7 Access</td>
                        <td>❌</td>
                        <td>❌</td>
                        <td>✅</td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>

    <!-- JavaScript -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        const billingToggle = document.getElementById('billingToggle');
        const monthlyLabel = document.getElementById('monthlyLabel');
        const yearlyLabel = document.getElementById('yearlyLabel');
        
        const prices = {
            monthly: { basic: 49, premium: 99, elite: 199 },
            yearly: { basic: 39, premium: 79, elite: 159 }
        };
        
        let isYearly = false;

        billingToggle.addEventListener('click', function() {
            isYearly = !isYearly;
            
            // Toggle UI
            this.classList.toggle('active');
            monthlyLabel.classList.toggle('active', !isYearly);
            yearlyLabel.classList.toggle('active', isYearly);
            
            // Update prices
            updatePrices();
        });

        function updatePrices() {
            const currentPrices = isYearly ? prices.yearly : prices.monthly;
            const period = isYearly ? '/year' : '/month';
            
            // Update price displays
            document.getElementById('basicPrice').textContent = currentPrices.basic;
            document.getElementById('premiumPrice').textContent = currentPrices.premium;
            document.getElementById('elitePrice').textContent = currentPrices.elite;
            
            // Update periods
            document.querySelectorAll('.plan-period').forEach(el => {
                el.textContent = period;
            });
            
            // Show/hide original prices for yearly
            const originalPrices = document.querySelectorAll('.plan-original-price');
            originalPrices.forEach(el => {
                el.style.display = isYearly ? 'block' : 'none';
            });
        }

        // Plan button click handlers
        document.querySelectorAll('.plan-button').forEach(button => {
            button.addEventListener('click', function() {
                const planType = this.classList.contains('basic') ? 'Basic' : 
                               this.classList.contains('premium') ? 'Premium' : 'Elite';
                const billing = isYearly ? 'Yearly' : 'Monthly';
                
                // Add visual feedback
                this.style.transform = 'scale(0.95)';
                setTimeout(() => {
                    this.style.transform = '';
                }, 150);
                
                // Here you would typically redirect to payment or show a modal
                alert(`Selected: ${planType} Plan (${billing} billing)`);
            });
        });

        // Add hover effects to plan cards
        document.querySelectorAll('.plan-card').forEach(card => {
            card.addEventListener('mouseenter', function() {
                if (!this.classList.contains('featured')) {
                    this.style.borderColor = 'var(--primary-color)';
                }
            });
            
            card.addEventListener('mouseleave', function() {
                if (!this.classList.contains('featured')) {
                    this.style.borderColor = 'var(--gray-100)';
                }
            });
        });
    </script>
</body>
</html>
