{% load static %}
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Register - Elite Sports Club</title>
    
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.0/font/bootstrap-icons.css" rel="stylesheet">
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Orbitron:wght@400;700;900&family=Rajdhani:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <style>
        :root {
            --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            --secondary-gradient: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            --accent-gradient: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            --success-gradient: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);
            --sports-blue: #1e40af;
            --sports-orange: #f97316;
            --sports-green: #10b981;
            --sports-purple: #8b5cf6;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Rajdhani', sans-serif;
            background: linear-gradient(135deg, rgba(30, 64, 175, 0.9) 0%, rgba(139, 92, 246, 0.9) 50%, rgba(16, 185, 129, 0.9) 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
            overflow: hidden;
            padding: 2rem 0;
        }

        /* Animated Background Elements */
        .bg-animation {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            overflow: hidden;
            z-index: 1;
        }

        .floating-shape {
            position: absolute;
            opacity: 0.1;
            animation: float 8s ease-in-out infinite;
            color: white;
        }

        .floating-shape:nth-child(1) {
            top: 5%;
            left: 5%;
            font-size: 2.5rem;
            animation-delay: 0s;
        }

        .floating-shape:nth-child(2) {
            top: 15%;
            right: 10%;
            font-size: 3rem;
            animation-delay: 2s;
        }

        .floating-shape:nth-child(3) {
            bottom: 15%;
            left: 15%;
            font-size: 3.5rem;
            animation-delay: 4s;
        }

        .floating-shape:nth-child(4) {
            bottom: 25%;
            right: 5%;
            font-size: 2.8rem;
            animation-delay: 1s;
        }

        .floating-shape:nth-child(5) {
            top: 50%;
            left: 5%;
            font-size: 2.2rem;
            animation-delay: 3s;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            50% { transform: translateY(-25px) rotate(180deg); }
        }

        .register-container {
            position: relative;
            z-index: 2;
            width: 100%;
            max-width: 500px;
            padding: 1rem;
        }

        .register-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: 25px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            padding: 2.5rem;
            transition: all 0.3s ease;
        }

        .register-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 30px 60px rgba(0, 0, 0, 0.15);
        }

        .brand-logo {
            width: 90px;
            height: 90px;
            margin: 0 auto 1.5rem;
            background: var(--success-gradient);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 2.2rem;
            box-shadow: 0 10px 30px rgba(17, 153, 142, 0.4);
            animation: pulse 3s infinite;
            position: relative;
            overflow: hidden;
        }

        .brand-logo::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.3), transparent);
            transform: rotate(45deg);
            animation: shine 2.5s infinite;
        }

        @keyframes pulse {
            0% { box-shadow: 0 10px 30px rgba(17, 153, 142, 0.4); }
            50% { box-shadow: 0 10px 40px rgba(17, 153, 142, 0.7); }
            100% { box-shadow: 0 10px 30px rgba(17, 153, 142, 0.4); }
        }

        @keyframes shine {
            0% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
            100% { transform: translateX(100%) translateY(100%) rotate(45deg); }
        }

        .register-title {
            font-family: 'Orbitron', monospace;
            font-size: 1.8rem;
            font-weight: 700;
            text-align: center;
            margin-bottom: 0.5rem;
            background: var(--primary-gradient);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .register-subtitle {
            text-align: center;
            color: #6b7280;
            margin-bottom: 1.5rem;
            font-size: 1rem;
        }

        .form-group {
            margin-bottom: 1.2rem;
        }

        .form-label {
            font-weight: 600;
            color: #374151;
            margin-bottom: 0.4rem;
            font-size: 0.95rem;
        }

        .input-group {
            position: relative;
        }

        .input-icon {
            position: absolute;
            left: 12px;
            top: 50%;
            transform: translateY(-50%);
            color: #9ca3af;
            z-index: 3;
            font-size: 1rem;
        }

        .form-control, .form-select {
            padding: 0.8rem 0.8rem 0.8rem 2.8rem;
            border: 2px solid #e5e7eb;
            border-radius: 12px;
            font-size: 0.95rem;
            transition: all 0.3s ease;
            background: rgba(255, 255, 255, 0.8);
        }

        .form-control:focus, .form-select:focus {
            border-color: #11998e;
            box-shadow: 0 0 0 3px rgba(17, 153, 142, 0.1);
            background: white;
        }

        .register-btn {
            width: 100%;
            padding: 0.9rem;
            background: var(--success-gradient);
            border: none;
            border-radius: 12px;
            color: white;
            font-size: 1rem;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 1px;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
            margin-top: 1rem;
        }

        .register-btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: left 0.5s;
        }

        .register-btn:hover::before {
            left: 100%;
        }

        .register-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(17, 153, 142, 0.4);
        }

        .login-link {
            text-align: center;
            margin-top: 1.5rem;
            color: #6b7280;
        }

        .login-link a {
            color: #4facfe;
            text-decoration: none;
            font-weight: 600;
            transition: all 0.3s ease;
        }

        .login-link a:hover {
            color: #0ea5e9;
            text-decoration: underline;
        }

        .alert {
            border-radius: 12px;
            border: none;
            margin-bottom: 1.2rem;
            font-size: 0.9rem;
        }

        .alert-danger {
            background: linear-gradient(135deg, #fee2e2 0%, #fecaca 100%);
            color: #dc2626;
        }

        .alert-success {
            background: linear-gradient(135deg, #d1fae5 0%, #a7f3d0 100%);
            color: #059669;
        }

        /* Responsive */
        @media (max-width: 768px) {
            .register-container {
                padding: 0.5rem;
            }
            
            .register-card {
                padding: 2rem;
            }
            
            .brand-logo {
                width: 70px;
                height: 70px;
                font-size: 1.8rem;
            }
            
            .register-title {
                font-size: 1.5rem;
            }
        }
    </style>
</head>
<body>
    <!-- Animated Background -->
    <div class="bg-animation">
        <div class="floating-shape">
            <i class="bi bi-trophy-fill"></i>
        </div>
        <div class="floating-shape">
            <i class="bi bi-award-fill"></i>
        </div>
        <div class="floating-shape">
            <i class="bi bi-star-fill"></i>
        </div>
        <div class="floating-shape">
            <i class="bi bi-gem"></i>
        </div>
        <div class="floating-shape">
            <i class="bi bi-shield-fill"></i>
        </div>
    </div>

    <!-- Register Container -->
    <div class="register-container">
        <div class="register-card">
            <!-- Brand Logo -->
            <div class="brand-logo">
                <i class="bi bi-person-plus-fill"></i>
            </div>

            <!-- Title -->
            <h1 class="register-title">JOIN ELITE SPORTS</h1>
            <p class="register-subtitle">Start your champion journey today!</p>

            <!-- Messages -->
            <div id="error" class="alert alert-danger d-none" role="alert"></div>
            <div id="success" class="alert alert-success d-none" role="alert"></div>
            
            <!-- Loading -->
            <div id="loading" class="d-none text-center mb-3">
                <div class="spinner-border text-success" role="status">
                    <span class="visually-hidden">Loading...</span>
                </div>
            </div>

            <!-- Register Form -->
            <form id="registerForm">
                {% csrf_token %}
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="username" class="form-label">Username</label>
                            <div class="input-group">
                                <i class="bi bi-person input-icon"></i>
                                <input type="text" class="form-control" id="username" name="username" required 
                                       placeholder="Choose username">
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="email" class="form-label">Email</label>
                            <div class="input-group">
                                <i class="bi bi-envelope input-icon"></i>
                                <input type="email" class="form-control" id="email" name="email" required 
                                       placeholder="Your email">
                            </div>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="password" class="form-label">Password</label>
                            <div class="input-group">
                                <i class="bi bi-lock input-icon"></i>
                                <input type="password" class="form-control" id="password" name="password" required 
                                       placeholder="Create password">
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="confirm_password" class="form-label">Confirm Password</label>
                            <div class="input-group">
                                <i class="bi bi-lock-fill input-icon"></i>
                                <input type="password" class="form-control" id="confirm_password" name="confirm_password" required 
                                       placeholder="Confirm password">
                            </div>
                        </div>
                    </div>
                </div>

                <div class="form-group">
                    <label for="role" class="form-label">Join as</label>
                    <div class="input-group">
                        <i class="bi bi-people input-icon"></i>
                        <select class="form-select" id="role" name="role" required>
                            <option value="">Select your role</option>
                            <option value="joueur">Player</option>
                            <option value="coach">Coach</option>
                        </select>
                    </div>
                </div>
                
                <button type="submit" id="registerBtn" class="register-btn">
                    Join the Club
                </button>
            </form>

            <!-- Login Link -->
            <div class="login-link">
                <p class="mb-0">
                    Already have an account?
                    <a href="{% url 'login' %}">Sign In</a>
                </p>
            </div>
        </div>
    </div>

    <!-- JavaScript -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/axios@1.4.0/dist/axios.min.js"></script>
    <script>
        document.getElementById('registerForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const formData = new FormData(this);
            const data = Object.fromEntries(formData);
            
            const errorDiv = document.getElementById('error');
            const successDiv = document.getElementById('success');
            const loadingDiv = document.getElementById('loading');
            const registerBtn = document.getElementById('registerBtn');
            
            // Validate passwords match
            if (data.password !== data.confirm_password) {
                errorDiv.textContent = 'Passwords do not match!';
                errorDiv.classList.remove('d-none');
                successDiv.classList.add('d-none');
                return;
            }
            
            // Show loading
            loadingDiv.classList.remove('d-none');
            errorDiv.classList.add('d-none');
            successDiv.classList.add('d-none');
            registerBtn.disabled = true;
            
            try {
                const response = await axios.post('/auth/api/register/', data);
                
                if (response.data.message) {
                    successDiv.textContent = 'Registration successful! Redirecting to login...';
                    successDiv.classList.remove('d-none');
                    
                    // Redirect to login after 2 seconds
                    setTimeout(() => {
                        window.location.href = '{% url "login" %}';
                    }, 2000);
                }
            } catch (error) {
                console.error('Registration error:', error);
                const errorMessage = error.response?.data?.error || 'Registration failed. Please try again.';
                errorDiv.textContent = errorMessage;
                errorDiv.classList.remove('d-none');
            } finally {
                loadingDiv.classList.add('d-none');
                registerBtn.disabled = false;
            }
        });

        // Add floating animation to shapes
        const shapes = document.querySelectorAll('.floating-shape');
        shapes.forEach((shape, index) => {
            shape.addEventListener('click', function() {
                this.style.transform = 'scale(1.5) rotate(720deg)';
                setTimeout(() => {
                    this.style.transform = '';
                }, 600);
            });
        });
    </script>
</body>
</html>
