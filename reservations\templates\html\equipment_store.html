{% load static %}
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Equipment Store - Elite Sports Club</title>
    
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.0/font/bootstrap-icons.css" rel="stylesheet">
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <style>
        :root {
            --primary-color: #6366f1;
            --secondary-color: #10b981;
            --accent-color: #f59e0b;
            --danger-color: #ef4444;
            --gray-50: #f9fafb;
            --gray-100: #f3f4f6;
            --gray-200: #e5e7eb;
            --gray-600: #4b5563;
            --gray-700: #374151;
            --gray-800: #1f2937;
        }

        body {
            font-family: 'Inter', sans-serif;
            background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
            color: var(--gray-700);
            padding-top: 80px;
        }

        .store-container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 2rem;
        }

        .page-header {
            background: white;
            border-radius: 16px;
            padding: 2rem;
            margin-bottom: 2rem;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
            border: 1px solid var(--gray-100);
        }

        .page-title {
            font-family: 'Poppins', sans-serif;
            font-size: 2.5rem;
            font-weight: 700;
            color: var(--gray-800);
            margin: 0;
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .title-icon {
            width: 56px;
            height: 56px;
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            border-radius: 16px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.5rem;
        }

        .store-filters {
            background: white;
            border-radius: 16px;
            padding: 1.5rem;
            margin-bottom: 2rem;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            border: 1px solid var(--gray-100);
        }

        .filter-row {
            display: flex;
            gap: 1rem;
            align-items: center;
            flex-wrap: wrap;
        }

        .filter-select {
            border-radius: 8px;
            border: 2px solid var(--gray-200);
            padding: 0.5rem 1rem;
            font-size: 0.875rem;
            min-width: 150px;
        }

        .search-input {
            border-radius: 8px;
            border: 2px solid var(--gray-200);
            padding: 0.5rem 1rem;
            font-size: 0.875rem;
            flex: 1;
            min-width: 200px;
        }

        .search-input:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
            outline: none;
        }

        .products-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 2rem;
        }

        .product-card {
            background: white;
            border-radius: 16px;
            overflow: hidden;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
            border: 1px solid var(--gray-100);
            transition: all 0.3s ease;
            position: relative;
        }

        .product-card:hover {
            transform: translateY(-8px);
            box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
        }

        .product-image {
            width: 100%;
            height: 200px;
            background: var(--gray-100);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 3rem;
            color: var(--gray-400);
            position: relative;
            overflow: hidden;
        }

        .product-badge {
            position: absolute;
            top: 1rem;
            right: 1rem;
            padding: 0.25rem 0.75rem;
            border-radius: 20px;
            font-size: 0.75rem;
            font-weight: 600;
            text-transform: uppercase;
        }

        .badge-new {
            background: var(--secondary-color);
            color: white;
        }

        .badge-sale {
            background: var(--danger-color);
            color: white;
        }

        .badge-popular {
            background: var(--accent-color);
            color: white;
        }

        .product-info {
            padding: 1.5rem;
        }

        .product-category {
            color: var(--gray-600);
            font-size: 0.75rem;
            text-transform: uppercase;
            font-weight: 500;
            margin-bottom: 0.5rem;
        }

        .product-title {
            font-weight: 600;
            color: var(--gray-800);
            margin-bottom: 0.5rem;
            font-size: 1.1rem;
        }

        .product-description {
            color: var(--gray-600);
            font-size: 0.875rem;
            line-height: 1.4;
            margin-bottom: 1rem;
        }

        .product-rating {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            margin-bottom: 1rem;
        }

        .stars {
            color: var(--accent-color);
        }

        .rating-text {
            font-size: 0.875rem;
            color: var(--gray-600);
        }

        .product-price {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            margin-bottom: 1rem;
        }

        .current-price {
            font-size: 1.25rem;
            font-weight: 700;
            color: var(--primary-color);
        }

        .original-price {
            font-size: 1rem;
            color: var(--gray-500);
            text-decoration: line-through;
        }

        .discount {
            background: var(--danger-color);
            color: white;
            padding: 0.25rem 0.5rem;
            border-radius: 4px;
            font-size: 0.75rem;
            font-weight: 600;
        }

        .product-actions {
            display: flex;
            gap: 0.5rem;
        }

        .btn-add-cart {
            flex: 1;
            background: var(--primary-color);
            color: white;
            border: none;
            padding: 0.75rem;
            border-radius: 8px;
            font-weight: 500;
            transition: all 0.2s ease;
            cursor: pointer;
        }

        .btn-add-cart:hover {
            background: #5856eb;
        }

        .btn-wishlist {
            width: 44px;
            height: 44px;
            background: var(--gray-100);
            color: var(--gray-600);
            border: none;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .btn-wishlist:hover {
            background: var(--danger-color);
            color: white;
        }

        .btn-wishlist.active {
            background: var(--danger-color);
            color: white;
        }

        .cart-sidebar {
            position: fixed;
            top: 0;
            right: -400px;
            width: 400px;
            height: 100vh;
            background: white;
            box-shadow: -4px 0 20px rgba(0, 0, 0, 0.1);
            transition: right 0.3s ease;
            z-index: 1000;
            display: flex;
            flex-direction: column;
        }

        .cart-sidebar.open {
            right: 0;
        }

        .cart-header {
            padding: 2rem;
            border-bottom: 1px solid var(--gray-200);
            display: flex;
            justify-content: between;
            align-items: center;
        }

        .cart-title {
            font-weight: 600;
            color: var(--gray-800);
            margin: 0;
        }

        .cart-close {
            background: none;
            border: none;
            font-size: 1.5rem;
            color: var(--gray-600);
            cursor: pointer;
        }

        .cart-items {
            flex: 1;
            overflow-y: auto;
            padding: 1rem;
        }

        .cart-item {
            display: flex;
            gap: 1rem;
            padding: 1rem;
            border-bottom: 1px solid var(--gray-100);
        }

        .cart-item-image {
            width: 60px;
            height: 60px;
            background: var(--gray-100);
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: var(--gray-400);
        }

        .cart-item-info {
            flex: 1;
        }

        .cart-item-title {
            font-weight: 500;
            color: var(--gray-800);
            margin-bottom: 0.25rem;
            font-size: 0.875rem;
        }

        .cart-item-price {
            color: var(--primary-color);
            font-weight: 600;
            font-size: 0.875rem;
        }

        .cart-footer {
            padding: 2rem;
            border-top: 1px solid var(--gray-200);
        }

        .cart-total {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1rem;
            font-size: 1.125rem;
            font-weight: 600;
        }

        .btn-checkout {
            width: 100%;
            background: var(--secondary-color);
            color: white;
            border: none;
            padding: 1rem;
            border-radius: 8px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .btn-checkout:hover {
            background: #059669;
        }

        .cart-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            opacity: 0;
            visibility: hidden;
            transition: all 0.3s ease;
            z-index: 999;
        }

        .cart-overlay.show {
            opacity: 1;
            visibility: visible;
        }

        .floating-cart {
            position: fixed;
            bottom: 2rem;
            right: 2rem;
            width: 60px;
            height: 60px;
            background: var(--primary-color);
            color: white;
            border: none;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
            cursor: pointer;
            box-shadow: 0 4px 20px rgba(99, 102, 241, 0.3);
            transition: all 0.3s ease;
            z-index: 998;
        }

        .floating-cart:hover {
            transform: scale(1.1);
        }

        .cart-count {
            position: absolute;
            top: -8px;
            right: -8px;
            background: var(--danger-color);
            color: white;
            border-radius: 50%;
            width: 24px;
            height: 24px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 0.75rem;
            font-weight: 600;
        }

        /* Responsive */
        @media (max-width: 768px) {
            .store-container {
                padding: 1rem;
            }
            
            .page-title {
                font-size: 2rem;
            }
            
            .products-grid {
                grid-template-columns: 1fr;
            }
            
            .filter-row {
                flex-direction: column;
                align-items: stretch;
            }
            
            .cart-sidebar {
                width: 100%;
                right: -100%;
            }
        }

        /* Animation */
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .animate-in {
            animation: fadeInUp 0.6s ease-out forwards;
        }
    </style>
</head>
<body>
    <!-- Include Navbar -->
    {% include 'html/navbar.html' %}

    <div class="store-container">
        <!-- Page Header -->
        <div class="page-header animate-in">
            <h1 class="page-title">
                <div class="title-icon">
                    <i class="bi bi-bag"></i>
                </div>
                Equipment Store
            </h1>
            <p class="text-muted mt-2">Professional sports equipment, apparel, and accessories for all your training needs</p>
        </div>

        <!-- Store Filters -->
        <div class="store-filters animate-in" style="animation-delay: 0.1s;">
            <div class="filter-row">
                <input type="text" class="search-input" placeholder="Search products..." id="searchInput">
                <select class="filter-select" id="categoryFilter">
                    <option value="">All Categories</option>
                    <option value="rackets">Rackets</option>
                    <option value="apparel">Apparel</option>
                    <option value="shoes">Shoes</option>
                    <option value="accessories">Accessories</option>
                    <option value="balls">Balls & Equipment</option>
                </select>
                <select class="filter-select" id="priceFilter">
                    <option value="">All Prices</option>
                    <option value="0-50">$0 - $50</option>
                    <option value="50-100">$50 - $100</option>
                    <option value="100-200">$100 - $200</option>
                    <option value="200+">$200+</option>
                </select>
                <select class="filter-select" id="sortFilter">
                    <option value="featured">Featured</option>
                    <option value="price-low">Price: Low to High</option>
                    <option value="price-high">Price: High to Low</option>
                    <option value="rating">Highest Rated</option>
                    <option value="newest">Newest</option>
                </select>
            </div>
        </div>

        <!-- Products Grid -->
        <div class="products-grid" id="productsGrid">
            <!-- Products will be populated here -->
        </div>
    </div>

    <!-- Floating Cart Button -->
    <button class="floating-cart" id="floatingCart">
        <i class="bi bi-cart"></i>
        <span class="cart-count" id="cartCount">0</span>
    </button>

    <!-- Cart Overlay -->
    <div class="cart-overlay" id="cartOverlay"></div>

    <!-- Cart Sidebar -->
    <div class="cart-sidebar" id="cartSidebar">
        <div class="cart-header">
            <h3 class="cart-title">Shopping Cart</h3>
            <button class="cart-close" id="cartClose">
                <i class="bi bi-x"></i>
            </button>
        </div>
        <div class="cart-items" id="cartItems">
            <!-- Cart items will be populated here -->
        </div>
        <div class="cart-footer">
            <div class="cart-total">
                <span>Total:</span>
                <span id="cartTotal">$0.00</span>
            </div>
            <button class="btn-checkout">Proceed to Checkout</button>
        </div>
    </div>

    <!-- JavaScript -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Sample products data
        const products = [
            {
                id: 1,
                title: "Professional Tennis Racket",
                category: "rackets",
                price: 199.99,
                originalPrice: 249.99,
                rating: 4.8,
                reviews: 124,
                description: "High-quality carbon fiber racket for professional players",
                badge: "popular",
                image: "🎾"
            },
            {
                id: 2,
                title: "Elite Sports Shoes",
                category: "shoes",
                price: 129.99,
                originalPrice: null,
                rating: 4.6,
                reviews: 89,
                description: "Comfortable and durable sports shoes for all court surfaces",
                badge: "new",
                image: "👟"
            },
            {
                id: 3,
                title: "Premium Training Apparel",
                category: "apparel",
                price: 79.99,
                originalPrice: 99.99,
                rating: 4.7,
                reviews: 156,
                description: "Moisture-wicking fabric for optimal performance",
                badge: "sale",
                image: "👕"
            },
            {
                id: 4,
                title: "Professional Tennis Balls",
                category: "balls",
                price: 24.99,
                originalPrice: null,
                rating: 4.9,
                reviews: 203,
                description: "ITF approved tennis balls for tournaments",
                badge: null,
                image: "🎾"
            },
            {
                id: 5,
                title: "Sports Water Bottle",
                category: "accessories",
                price: 19.99,
                originalPrice: null,
                rating: 4.5,
                reviews: 67,
                description: "Insulated water bottle to keep drinks cold",
                badge: null,
                image: "🍶"
            },
            {
                id: 6,
                title: "Tennis Grip Tape",
                category: "accessories",
                price: 12.99,
                originalPrice: 15.99,
                rating: 4.4,
                reviews: 45,
                description: "Non-slip grip tape for better racket control",
                badge: "sale",
                image: "🎯"
            }
        ];

        let cart = [];
        let wishlist = [];

        function renderProducts(productsToRender = products) {
            const grid = document.getElementById('productsGrid');
            grid.innerHTML = '';

            productsToRender.forEach((product, index) => {
                const productCard = createProductCard(product, index);
                grid.appendChild(productCard);
            });
        }

        function createProductCard(product, index) {
            const card = document.createElement('div');
            card.className = 'product-card animate-in';
            card.style.animationDelay = `${(index * 0.1) + 0.2}s`;
            
            const discount = product.originalPrice ? 
                Math.round(((product.originalPrice - product.price) / product.originalPrice) * 100) : 0;
            
            card.innerHTML = `
                <div class="product-image">
                    ${product.image}
                    ${product.badge ? `<div class="product-badge badge-${product.badge}">${product.badge}</div>` : ''}
                </div>
                <div class="product-info">
                    <div class="product-category">${product.category}</div>
                    <h3 class="product-title">${product.title}</h3>
                    <p class="product-description">${product.description}</p>
                    
                    <div class="product-rating">
                        <div class="stars">
                            ${'★'.repeat(Math.floor(product.rating))}${'☆'.repeat(5 - Math.floor(product.rating))}
                        </div>
                        <span class="rating-text">${product.rating} (${product.reviews} reviews)</span>
                    </div>
                    
                    <div class="product-price">
                        <span class="current-price">$${product.price}</span>
                        ${product.originalPrice ? `<span class="original-price">$${product.originalPrice}</span>` : ''}
                        ${discount > 0 ? `<span class="discount">-${discount}%</span>` : ''}
                    </div>
                    
                    <div class="product-actions">
                        <button class="btn-add-cart" onclick="addToCart(${product.id})">
                            <i class="bi bi-cart-plus"></i> Add to Cart
                        </button>
                        <button class="btn-wishlist" onclick="toggleWishlist(${product.id})">
                            <i class="bi bi-heart"></i>
                        </button>
                    </div>
                </div>
            `;
            
            return card;
        }

        function addToCart(productId) {
            const product = products.find(p => p.id === productId);
            const existingItem = cart.find(item => item.id === productId);
            
            if (existingItem) {
                existingItem.quantity += 1;
            } else {
                cart.push({ ...product, quantity: 1 });
            }
            
            updateCartUI();
            showCartNotification();
        }

        function toggleWishlist(productId) {
            const index = wishlist.indexOf(productId);
            if (index > -1) {
                wishlist.splice(index, 1);
            } else {
                wishlist.push(productId);
            }
            
            // Update wishlist button appearance
            const btn = event.target.closest('.btn-wishlist');
            btn.classList.toggle('active');
        }

        function updateCartUI() {
            const cartCount = document.getElementById('cartCount');
            const cartItems = document.getElementById('cartItems');
            const cartTotal = document.getElementById('cartTotal');
            
            // Update cart count
            const totalItems = cart.reduce((sum, item) => sum + item.quantity, 0);
            cartCount.textContent = totalItems;
            cartCount.style.display = totalItems > 0 ? 'flex' : 'none';
            
            // Update cart items
            cartItems.innerHTML = '';
            cart.forEach(item => {
                const cartItem = document.createElement('div');
                cartItem.className = 'cart-item';
                cartItem.innerHTML = `
                    <div class="cart-item-image">${item.image}</div>
                    <div class="cart-item-info">
                        <div class="cart-item-title">${item.title}</div>
                        <div class="cart-item-price">$${item.price} x ${item.quantity}</div>
                    </div>
                `;
                cartItems.appendChild(cartItem);
            });
            
            // Update total
            const total = cart.reduce((sum, item) => sum + (item.price * item.quantity), 0);
            cartTotal.textContent = `$${total.toFixed(2)}`;
        }

        function showCartNotification() {
            // Simple notification - you could enhance this
            const btn = event.target;
            const originalText = btn.innerHTML;
            btn.innerHTML = '<i class="bi bi-check"></i> Added!';
            btn.style.background = '#10b981';
            
            setTimeout(() => {
                btn.innerHTML = originalText;
                btn.style.background = '';
            }, 1000);
        }

        // Cart sidebar functionality
        document.getElementById('floatingCart').addEventListener('click', function() {
            document.getElementById('cartSidebar').classList.add('open');
            document.getElementById('cartOverlay').classList.add('show');
        });

        document.getElementById('cartClose').addEventListener('click', closeCart);
        document.getElementById('cartOverlay').addEventListener('click', closeCart);

        function closeCart() {
            document.getElementById('cartSidebar').classList.remove('open');
            document.getElementById('cartOverlay').classList.remove('show');
        }

        // Filter functionality
        function filterProducts() {
            const searchTerm = document.getElementById('searchInput').value.toLowerCase();
            const category = document.getElementById('categoryFilter').value;
            const priceRange = document.getElementById('priceFilter').value;
            const sortBy = document.getElementById('sortFilter').value;

            let filtered = products.filter(product => {
                const matchesSearch = product.title.toLowerCase().includes(searchTerm) ||
                                    product.description.toLowerCase().includes(searchTerm);
                const matchesCategory = !category || product.category === category;
                
                let matchesPrice = true;
                if (priceRange) {
                    const [min, max] = priceRange.split('-').map(p => p.replace('+', ''));
                    if (max) {
                        matchesPrice = product.price >= parseInt(min) && product.price <= parseInt(max);
                    } else {
                        matchesPrice = product.price >= parseInt(min);
                    }
                }

                return matchesSearch && matchesCategory && matchesPrice;
            });

            // Sort products
            switch (sortBy) {
                case 'price-low':
                    filtered.sort((a, b) => a.price - b.price);
                    break;
                case 'price-high':
                    filtered.sort((a, b) => b.price - a.price);
                    break;
                case 'rating':
                    filtered.sort((a, b) => b.rating - a.rating);
                    break;
                case 'newest':
                    filtered.sort((a, b) => b.id - a.id);
                    break;
            }

            renderProducts(filtered);
        }

        // Event listeners
        document.getElementById('searchInput').addEventListener('input', filterProducts);
        document.getElementById('categoryFilter').addEventListener('change', filterProducts);
        document.getElementById('priceFilter').addEventListener('change', filterProducts);
        document.getElementById('sortFilter').addEventListener('change', filterProducts);

        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            renderProducts();
            updateCartUI();
        });
    </script>
</body>
</html>
