{% load static %}
<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Blog - Elite Sports Club</title>

    <!-- Bootstrap Icons -->
    <link
      href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.0/font/bootstrap-icons.css"
      rel="stylesheet"
    />
    <!-- Bootstrap CSS -->
    <link
      href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css"
      rel="stylesheet"
    />
    <!-- Google Fonts -->
    <link
      href="https://fonts.googleapis.com/css2?family=Orbitron:wght@400;700;900&family=Rajdhani:wght@300;400;500;600;700&display=swap"
      rel="stylesheet"
    />

    <style>
      :root {
        --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        --secondary-gradient: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
        --accent-gradient: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
        --success-gradient: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);
        --warning-gradient: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
        --dark-gradient: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
      }

      * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
      }

      body {
        font-family: "Rajdhani", sans-serif;
        background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
        padding-top: 80px;
      }

      /* Header Section */
      .blog-header {
        background: linear-gradient(
          135deg,
          rgba(30, 64, 175, 0.9) 0%,
          rgba(139, 92, 246, 0.9) 50%,
          rgba(16, 185, 129, 0.9) 100%
        );
        padding: 4rem 0;
        text-align: center;
        color: white;
        position: relative;
        overflow: hidden;
      }

      .blog-header::before {
        content: "";
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><path d="M20,20 Q30,10 40,20 T60,20" stroke="rgba(255,255,255,0.1)" fill="none" stroke-width="2"/><circle cx="80" cy="80" r="3" fill="rgba(255,255,255,0.08)"/></svg>')
          repeat;
        animation: drift 15s linear infinite;
      }

      @keyframes drift {
        0% {
          transform: translateX(0) translateY(0);
        }
        100% {
          transform: translateX(-100px) translateY(-50px);
        }
      }

      .blog-title {
        font-family: "Orbitron", monospace;
        font-size: 3rem;
        font-weight: 900;
        margin-bottom: 1rem;
        text-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
        position: relative;
        z-index: 2;
      }

      .blog-subtitle {
        font-size: 1.2rem;
        opacity: 0.9;
        position: relative;
        z-index: 2;
      }

      /* Blog Section */
      .blog-section {
        padding: 4rem 0;
      }

      .blog-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
        gap: 2rem;
        max-width: 1200px;
        margin: 0 auto;
        padding: 0 2rem;
      }

      .blog-card {
        background: white;
        border-radius: 20px;
        overflow: hidden;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        transition: all 0.3s ease;
        position: relative;
      }

      .blog-card:hover {
        transform: translateY(-10px);
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
      }

      .blog-image {
        height: 200px;
        background: var(--accent-gradient);
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 3rem;
        position: relative;
        overflow: hidden;
      }

      .blog-image::before {
        content: "";
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(
          45deg,
          transparent,
          rgba(255, 255, 255, 0.1),
          transparent
        );
        transform: translateX(-100%);
        transition: transform 0.6s;
      }

      .blog-card:hover .blog-image::before {
        transform: translateX(100%);
      }

      .blog-content {
        padding: 2rem;
      }

      .blog-category {
        display: inline-block;
        background: var(--success-gradient);
        color: white;
        padding: 0.3rem 1rem;
        border-radius: 20px;
        font-size: 0.8rem;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 1px;
        margin-bottom: 1rem;
      }

      .blog-title-card {
        font-size: 1.3rem;
        font-weight: 700;
        margin-bottom: 1rem;
        color: #2d3748;
        line-height: 1.4;
      }

      .blog-excerpt {
        color: #718096;
        line-height: 1.6;
        margin-bottom: 1.5rem;
      }

      .blog-meta {
        display: flex;
        justify-content: space-between;
        align-items: center;
        color: #a0aec0;
        font-size: 0.9rem;
        margin-bottom: 1.5rem;
      }

      .blog-date {
        display: flex;
        align-items: center;
      }

      .blog-date i {
        margin-right: 0.5rem;
      }

      .blog-read-time {
        display: flex;
        align-items: center;
      }

      .blog-read-time i {
        margin-right: 0.5rem;
      }

      .blog-btn {
        background: var(--primary-gradient);
        color: white;
        border: none;
        padding: 0.8rem 2rem;
        border-radius: 25px;
        font-weight: 600;
        text-decoration: none;
        display: inline-block;
        transition: all 0.3s ease;
        text-transform: uppercase;
        letter-spacing: 1px;
        font-size: 0.9rem;
      }

      .blog-btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 20px rgba(102, 126, 234, 0.4);
        color: white;
      }

      /* Featured Post */
      .featured-post {
        grid-column: 1 / -1;
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 0;
        margin-bottom: 2rem;
      }

      .featured-image {
        background: var(--secondary-gradient);
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 4rem;
        position: relative;
        overflow: hidden;
      }

      .featured-content {
        padding: 3rem;
        display: flex;
        flex-direction: column;
        justify-content: center;
      }

      .featured-category {
        background: var(--warning-gradient);
        color: #2d3748;
        padding: 0.5rem 1.5rem;
        border-radius: 25px;
        font-size: 0.9rem;
        font-weight: 700;
        text-transform: uppercase;
        letter-spacing: 1px;
        margin-bottom: 1.5rem;
        display: inline-block;
        width: fit-content;
      }

      .featured-title {
        font-family: "Orbitron", monospace;
        font-size: 2rem;
        font-weight: 700;
        margin-bottom: 1rem;
        color: #2d3748;
        line-height: 1.3;
      }

      .featured-excerpt {
        color: #718096;
        line-height: 1.6;
        margin-bottom: 2rem;
        font-size: 1.1rem;
      }

      /* Newsletter Section */
      .newsletter-section {
        background: var(--dark-gradient);
        padding: 4rem 0;
        text-align: center;
        color: white;
      }

      .newsletter-title {
        font-family: "Orbitron", monospace;
        font-size: 2.5rem;
        font-weight: 700;
        margin-bottom: 1rem;
      }

      .newsletter-subtitle {
        font-size: 1.1rem;
        margin-bottom: 2rem;
        opacity: 0.9;
      }

      .newsletter-form {
        max-width: 500px;
        margin: 0 auto;
        display: flex;
        gap: 1rem;
      }

      .newsletter-input {
        flex: 1;
        padding: 1rem;
        border: none;
        border-radius: 25px;
        font-size: 1rem;
        background: rgba(255, 255, 255, 0.1);
        color: white;
        backdrop-filter: blur(10px);
      }

      .newsletter-input::placeholder {
        color: rgba(255, 255, 255, 0.7);
      }

      .newsletter-btn {
        background: var(--success-gradient);
        color: white;
        border: none;
        padding: 1rem 2rem;
        border-radius: 25px;
        font-weight: 600;
        transition: all 0.3s ease;
        text-transform: uppercase;
        letter-spacing: 1px;
      }

      .newsletter-btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 20px rgba(17, 153, 142, 0.4);
      }

      /* Responsive */
      @media (max-width: 768px) {
        .blog-title {
          font-size: 2.5rem;
        }

        .blog-grid {
          grid-template-columns: 1fr;
          padding: 0 1rem;
        }

        .featured-post {
          grid-template-columns: 1fr;
        }

        .featured-content {
          padding: 2rem;
        }

        .featured-title {
          font-size: 1.5rem;
        }

        .newsletter-form {
          flex-direction: column;
        }
      }
    </style>
  </head>
  <body>
    <!-- Include Navbar -->
    {% include 'html/navbar.html' %}

    <!-- Blog Header -->
    <section class="blog-header">
      <div class="container">
        <h1 class="blog-title">SPORTS BLOG</h1>
        <p class="blog-subtitle">
          Latest news, tips, and insights from the world of sports
        </p>
      </div>
    </section>

    <!-- Blog Section -->
    <section class="blog-section">
      <div class="container">
        <div class="blog-grid">
          <!-- Featured Post -->
          <article class="blog-card featured-post">
            <div class="featured-image">
              <i class="bi bi-trophy-fill"></i>
            </div>
            <div class="featured-content">
              <span class="featured-category">Featured</span>
              <h2 class="featured-title">Championship Training Secrets</h2>
              <p class="featured-excerpt">
                Discover the training methods used by professional athletes to
                reach peak performance. Learn the secrets that separate
                champions from the rest.
              </p>
              <a href="#" class="blog-btn">Read More</a>
            </div>
          </article>

          <!-- Regular Posts -->
          <article class="blog-card">
            <div class="blog-image" style="background: var(--success-gradient)">
              <i class="bi bi-heart-pulse-fill"></i>
            </div>
            <div class="blog-content">
              <span class="blog-category">Fitness</span>
              <h3 class="blog-title-card">10 Essential Warm-up Exercises</h3>
              <p class="blog-excerpt">
                Proper warm-up is crucial for preventing injuries and improving
                performance. Here are the top 10 exercises every athlete should
                know.
              </p>
              <div class="blog-meta">
                <span class="blog-date">
                  <i class="bi bi-calendar3"></i>
                  June 15, 2025
                </span>
                <span class="blog-read-time">
                  <i class="bi bi-clock"></i>
                  5 min read
                </span>
              </div>
              <a href="#" class="blog-btn">Read More</a>
            </div>
          </article>

          <article class="blog-card">
            <div
              class="blog-image"
              style="background: var(--secondary-gradient)"
            >
              <i class="bi bi-award-fill"></i>
            </div>
            <div class="blog-content">
              <span class="blog-category">Nutrition</span>
              <h3 class="blog-title-card">Athlete's Guide to Nutrition</h3>
              <p class="blog-excerpt">
                Fuel your body like a champion. Learn about the best foods and
                supplements for optimal athletic performance.
              </p>
              <div class="blog-meta">
                <span class="blog-date">
                  <i class="bi bi-calendar3"></i>
                  June 12, 2025
                </span>
                <span class="blog-read-time">
                  <i class="bi bi-clock"></i>
                  8 min read
                </span>
              </div>
              <a href="#" class="blog-btn">Read More</a>
            </div>
          </article>

          <article class="blog-card">
            <div class="blog-image" style="background: var(--sports-purple)">
              <i class="bi bi-people-fill"></i>
            </div>
            <div class="blog-content">
              <span class="blog-category">Team Sports</span>
              <h3 class="blog-title-card">Building Team Chemistry</h3>
              <p class="blog-excerpt">
                Great teams are built on trust and communication. Discover
                strategies for creating unbreakable team bonds.
              </p>
              <div class="blog-meta">
                <span class="blog-date">
                  <i class="bi bi-calendar3"></i>
                  June 10, 2025
                </span>
                <span class="blog-read-time">
                  <i class="bi bi-clock"></i>
                  6 min read
                </span>
              </div>
              <a href="#" class="blog-btn">Read More</a>
            </div>
          </article>

          <article class="blog-card">
            <div class="blog-image" style="background: var(--warning-gradient)">
              <i class="bi bi-lightning-charge-fill"></i>
            </div>
            <div class="blog-content">
              <span class="blog-category">Training</span>
              <h3 class="blog-title-card">High-Intensity Interval Training</h3>
              <p class="blog-excerpt">
                HIIT workouts can dramatically improve your fitness level in
                less time. Learn how to incorporate HIIT into your routine.
              </p>
              <div class="blog-meta">
                <span class="blog-date">
                  <i class="bi bi-calendar3"></i>
                  June 8, 2025
                </span>
                <span class="blog-read-time">
                  <i class="bi bi-clock"></i>
                  7 min read
                </span>
              </div>
              <a href="#" class="blog-btn">Read More</a>
            </div>
          </article>

          <article class="blog-card">
            <div class="blog-image" style="background: var(--accent-gradient)">
              <i class="bi bi-shield-fill-check"></i>
            </div>
            <div class="blog-content">
              <span class="blog-category">Safety</span>
              <h3 class="blog-title-card">Injury Prevention Tips</h3>
              <p class="blog-excerpt">
                Stay in the game longer with these proven injury prevention
                strategies. Your body will thank you later.
              </p>
              <div class="blog-meta">
                <span class="blog-date">
                  <i class="bi bi-calendar3"></i>
                  June 5, 2025
                </span>
                <span class="blog-read-time">
                  <i class="bi bi-clock"></i>
                  4 min read
                </span>
              </div>
              <a href="#" class="blog-btn">Read More</a>
            </div>
          </article>

          <article class="blog-card">
            <div class="blog-image" style="background: var(--primary-gradient)">
              <i class="bi bi-brain"></i>
            </div>
            <div class="blog-content">
              <span class="blog-category">Mental Health</span>
              <h3 class="blog-title-card">Mental Toughness in Sports</h3>
              <p class="blog-excerpt">
                The mind is just as important as the body in sports. Learn
                techniques to develop unshakeable mental strength.
              </p>
              <div class="blog-meta">
                <span class="blog-date">
                  <i class="bi bi-calendar3"></i>
                  June 3, 2025
                </span>
                <span class="blog-read-time">
                  <i class="bi bi-clock"></i>
                  9 min read
                </span>
              </div>
              <a href="#" class="blog-btn">Read More</a>
            </div>
          </article>
        </div>
      </div>
    </section>

    <!-- Newsletter Section -->
    <section class="newsletter-section">
      <div class="container">
        <h2 class="newsletter-title">Stay Updated</h2>
        <p class="newsletter-subtitle">
          Get the latest sports tips and news delivered to your inbox
        </p>
        <form class="newsletter-form">
          <input
            type="email"
            class="newsletter-input"
            placeholder="Enter your email"
            required
          />
          <button type="submit" class="newsletter-btn">Subscribe</button>
        </form>
      </div>
    </section>

    <!-- JavaScript -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
      // Add hover effects to blog cards
      const blogCards = document.querySelectorAll(
        ".blog-card:not(.featured-post)"
      );
      blogCards.forEach((card) => {
        card.addEventListener("mouseenter", function () {
          this.style.transform = "translateY(-15px) scale(1.02)";
        });

        card.addEventListener("mouseleave", function () {
          this.style.transform = "translateY(0) scale(1)";
        });
      });

      // Newsletter form submission
      document
        .querySelector(".newsletter-form")
        .addEventListener("submit", function (e) {
          e.preventDefault();
          const email = this.querySelector(".newsletter-input").value;
          alert(`Thank you for subscribing with email: ${email}`);
          this.reset();
        });
    </script>
  </body>
</html>
