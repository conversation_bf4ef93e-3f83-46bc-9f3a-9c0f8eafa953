#!/usr/bin/env python
"""
Script to create test equipment data
"""
import os
import sys
import django

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'tennis_manager.settings')
django.setup()

from reservations.models import Equipment

def create_test_equipment():
    print("🧪 Creating Test Equipment...")
    
    try:
        # Delete existing equipment
        Equipment.objects.all().delete()
        
        # Create tennis equipment
        equipment_data = [
            {
                'name': 'Professional Tennis Racket',
                'type': 'RAQUETTE',
                'brand': 'Wilson',
                'price': 150.00,
                'stock_quantity': 10,
                'description': 'High-quality professional tennis racket for advanced players',
                'available': True
            },
            {
                'name': 'Tennis Equipment Bag',
                'type': 'SAC',
                'brand': 'Head',
                'price': 75.00,
                'stock_quantity': 15,
                'description': 'Spacious tennis bag with multiple compartments',
                'available': True
            },
            {
                'name': 'Premium Tennis Balls (Pack of 3)',
                'type': 'BALLE',
                'brand': 'Penn',
                'price': 12.00,
                'stock_quantity': 50,
                'description': 'High-quality tennis balls for professional play',
                'available': True
            },
            {
                'name': 'Tennis Court Shoes',
                'type': 'CHAUSSURE',
                'brand': 'Nike',
                'price': 120.00,
                'stock_quantity': 20,
                'description': 'Professional tennis shoes with excellent grip and comfort',
                'available': True
            },
            {
                'name': 'Tennis Strings',
                'type': 'ACCESSOIRE',
                'brand': 'Babolat',
                'price': 25.00,
                'stock_quantity': 30,
                'description': 'High-performance tennis strings for racket restringing',
                'available': True
            },
            {
                'name': 'Tennis Grip Tape',
                'type': 'ACCESSOIRE',
                'brand': 'Tourna',
                'price': 8.00,
                'stock_quantity': 40,
                'description': 'Comfortable grip tape for better racket handling',
                'available': True
            }
        ]
        
        created_equipment = []
        for item_data in equipment_data:
            equipment = Equipment.objects.create(**item_data)
            created_equipment.append(equipment)
            print(f"✅ Created: {equipment.name} - ${equipment.price}")
        
        print(f"\n📊 Summary:")
        print(f"   Total Equipment Created: {len(created_equipment)}")
        print(f"   Total Stock Value: ${sum(item.price * item.stock_quantity for item in created_equipment):.2f}")
        
        # Test equipment by type
        print(f"\n📋 Equipment by Type:")
        types = Equipment.objects.values_list('type', flat=True).distinct()
        for equipment_type in types:
            count = Equipment.objects.filter(type=equipment_type).count()
            print(f"   {equipment_type}: {count} items")
        
        print(f"\n🎯 Test equipment purchase at:")
        print(f"   http://127.0.0.1:8000/auth/login/")
        print(f"   Login: <EMAIL> / player123")
        print(f"   Go to Equipment section and try ordering!")
        
        return True
        
    except Exception as e:
        print(f"❌ Error creating test equipment: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == '__main__':
    print("🎾 Tennis Management System - Test Equipment Creator")
    print("="*60)
    
    success = create_test_equipment()
    
    if success:
        print("\n🚀 Test equipment created successfully!")
        print("✅ Players can now purchase equipment from the dashboard.")
    else:
        print("\n⚠️  Equipment creation failed.")
