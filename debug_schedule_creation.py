#!/usr/bin/env python
"""
Debug du problème de création d'horaires des coachs
"""
import os
import sys
import django
import requests
import json
from datetime import datetime, timedelta

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'tennis_manager.settings')
django.setup()

from django.contrib.auth import get_user_model
from reservations.models import Coach, Schedule, ScheduleSlot

User = get_user_model()

def test_schedule_creation_api():
    print("🔍 DEBUG: Test de Création d'Horaires")
    print("="*45)
    
    # 1. Vérifier les coachs disponibles
    print("\n1️⃣ Vérification des coachs:")
    coaches = Coach.objects.all()
    print(f"   📊 {coaches.count()} coachs trouvés")
    
    if coaches.count() == 0:
        print("   ❌ Aucun coach trouvé - créons un coach de test")
        return create_test_coach()
    
    for coach in coaches:
        print(f"   👨‍🏫 Coach ID {coach.id}: {coach.name} ({coach.email})")
    
    # 2. Vérifier les horaires existants
    print("\n2️⃣ Vérification des horaires existants:")
    schedules = Schedule.objects.all()
    slots = ScheduleSlot.objects.all()
    print(f"   📅 {schedules.count()} horaires hebdomadaires")
    print(f"   ⏰ {slots.count()} créneaux spécifiques")
    
    # 3. Test API direct
    print("\n3️⃣ Test API de création d'horaires:")
    
    # Prendre le premier coach
    test_coach = coaches.first()
    tomorrow = (datetime.now() + timedelta(days=1)).strftime('%Y-%m-%d')
    
    # Données de test
    schedule_data = {
        'coach_id': test_coach.id,
        'date': tomorrow,
        'start_time': '10:00',
        'end_time': '11:00'
    }
    
    print(f"   📋 Données de test: {schedule_data}")
    
    try:
        # Test sans authentification d'abord
        print("   🔍 Test sans authentification:")
        response = requests.post('http://127.0.0.1:8000/res/api/coach-schedule/create/', 
                               json=schedule_data)
        print(f"   Status: {response.status_code}")
        print(f"   Response: {response.text[:200]}...")
        
        if response.status_code == 401:
            print("   ✅ API correctement protégée par authentification")
            
            # Test avec authentification admin
            print("\n   🔍 Test avec authentification admin:")
            return test_with_admin_auth(schedule_data)
        
    except Exception as e:
        print(f"   ❌ Erreur API: {e}")
        return False
    
    return True

def test_with_admin_auth(schedule_data):
    """Test avec authentification admin"""

    # 1. Utiliser un admin existant ou en créer un nouveau
    try:
        # Essayer de trouver un admin existant
        admin_user = User.objects.filter(role='admin').first()

        if admin_user:
            print(f"   ✅ Admin existant trouvé: {admin_user.username}")
            # Utiliser le mot de passe par défaut ou essayer plusieurs
            test_passwords = ['admin123', 'password', 'admin', '123456']
        else:
            # Créer un nouvel admin avec un email unique
            admin_user = User.objects.create_user(
                username='debug_admin',
                email='<EMAIL>',
                password='admin123',
                role='admin',
                is_staff=True,
                is_superuser=True
            )
            print(f"   ✅ Nouvel admin créé: {admin_user.username}")
            test_passwords = ['admin123']

    except Exception as e:
        print(f"   ❌ Erreur création/récupération admin: {e}")
        return False
    
    # 2. Obtenir un token JWT
    print("   🔑 Obtention du token JWT:")

    # Essayer de se connecter avec différents mots de passe
    for password in test_passwords:
        try:
            login_data = {
                'username': admin_user.username,
                'password': password
            }
        
            print(f"   🔍 Tentative de connexion: {admin_user.username} / {password}")

            login_response = requests.post('http://127.0.0.1:8000/auth/api/login/',
                                         json=login_data)

            if login_response.status_code == 200:
                token_data = login_response.json()
                access_token = token_data.get('access')
                print(f"   ✅ Token obtenu avec {password}: {access_token[:50]}...")

                # 3. Test API avec token
                headers = {
                    'Authorization': f'Bearer {access_token}',
                    'Content-Type': 'application/json'
                }

                return test_api_call(schedule_data, headers)

            else:
                print(f"   ❌ Échec connexion avec {password}: {login_response.status_code}")
                continue

        except Exception as e:
            print(f"   ❌ Erreur avec {password}: {e}")
            continue

    print("   ❌ Aucun mot de passe ne fonctionne")
    return False

def test_api_call(schedule_data, headers):
    """Effectuer l'appel API avec les headers d'authentification"""
    try:
            
        print("   📤 Envoi de la requête avec authentification:")
        print(f"   📋 URL: http://127.0.0.1:8000/res/api/coach-schedule/create/")
        print(f"   📋 Data: {schedule_data}")
        print(f"   📋 Headers: Authorization Bearer ...")

        response = requests.post('http://127.0.0.1:8000/res/api/coach-schedule/create/',
                               json=schedule_data, headers=headers)

        print(f"   📥 Status: {response.status_code}")
        print(f"   📥 Response: {response.text}")

        if response.status_code == 201:
            print("   ✅ Horaire créé avec succès!")

            # Vérifier en base de données
            coach_id = schedule_data['coach_id']
            date_str = schedule_data['date']

            new_slots = ScheduleSlot.objects.filter(
                coach_id=coach_id,
                date=date_str
            )

            print(f"   📊 {new_slots.count()} nouveaux créneaux trouvés en DB")

            for slot in new_slots:
                print(f"   ⏰ {slot.date} {slot.start_time}-{slot.end_time} (Coach: {slot.coach.name})")

            return True

        elif response.status_code == 400:
            error_data = response.json()
            print(f"   ❌ Erreur de validation: {error_data.get('error', 'Unknown error')}")

            # Diagnostics supplémentaires
            print("\n   🔍 Diagnostics supplémentaires:")

            # Vérifier si le coach existe
            try:
                coach = Coach.objects.get(id=schedule_data['coach_id'])
                print(f"   ✅ Coach trouvé: {coach.name}")
            except Coach.DoesNotExist:
                print(f"   ❌ Coach ID {schedule_data['coach_id']} n'existe pas")

            # Vérifier les créneaux existants
            existing_slots = ScheduleSlot.objects.filter(
                coach_id=schedule_data['coach_id'],
                date=schedule_data['date'],
                start_time=schedule_data['start_time'],
                end_time=schedule_data['end_time']
            )

            if existing_slots.exists():
                print(f"   ⚠️  Créneau déjà existant trouvé")
                # Essayer avec une heure différente
                print("   🔄 Tentative avec une heure différente...")
                new_schedule_data = schedule_data.copy()
                new_schedule_data['start_time'] = '15:00'
                new_schedule_data['end_time'] = '16:00'
                return test_api_call(new_schedule_data, headers)
            else:
                print(f"   ✅ Aucun créneau en conflit")

            return False

        else:
            print(f"   ❌ Erreur inattendue: {response.status_code}")
            return False

    except Exception as e:
        print(f"   ❌ Erreur lors de l'appel API: {e}")
        return False

def create_test_coach():
    """Créer un coach de test si aucun n'existe"""
    print("\n🏗️  Création d'un coach de test:")
    
    try:
        # Créer un utilisateur coach
        coach_user, created = User.objects.get_or_create(
            username='test_coach_debug',
            defaults={
                'email': '<EMAIL>',
                'first_name': 'Debug',
                'last_name': 'Coach',
                'role': 'coach'
            }
        )
        
        if created:
            coach_user.set_password('coach123')
            coach_user.save()
        
        # Créer le profil coach
        coach, created = Coach.objects.get_or_create(
            email=coach_user.email,
            defaults={
                'name': f"{coach_user.first_name} {coach_user.last_name}",
                'specialization': 'Debug Tennis Coaching',
                'experience_years': 3,
                'price_per_hour': 45.00,
                'available': True
            }
        )
        
        print(f"   ✅ Coach créé: {coach.name} (ID: {coach.id})")
        
        # Relancer le test
        return test_schedule_creation_api()
        
    except Exception as e:
        print(f"   ❌ Erreur création coach: {e}")
        return False

def show_current_state():
    """Afficher l'état actuel du système"""
    print("\n📊 ÉTAT ACTUEL DU SYSTÈME:")
    print("="*30)
    
    coaches = Coach.objects.all()
    schedules = Schedule.objects.all()
    slots = ScheduleSlot.objects.all()
    
    print(f"👥 Coachs: {coaches.count()}")
    for coach in coaches:
        coach_slots = slots.filter(coach=coach).count()
        print(f"   - {coach.name}: {coach_slots} créneaux")
    
    print(f"📅 Horaires hebdomadaires: {schedules.count()}")
    print(f"⏰ Créneaux spécifiques: {slots.count()}")
    
    # Créneaux récents
    recent_slots = slots.order_by('-created_at')[:5]
    if recent_slots:
        print(f"\n🕐 5 derniers créneaux créés:")
        for slot in recent_slots:
            print(f"   - {slot.coach.name}: {slot.date} {slot.start_time}-{slot.end_time}")

if __name__ == '__main__':
    print("🎾 TENNIS MANAGEMENT SYSTEM")
    print("🔧 Debug Création d'Horaires des Coachs")
    print("="*55)
    
    # Afficher l'état actuel
    show_current_state()
    
    # Tester la création d'horaires
    success = test_schedule_creation_api()
    
    if success:
        print("\n🎉 DIAGNOSTIC RÉUSSI!")
        print("="*25)
        print("✅ L'API de création d'horaires fonctionne")
        print("✅ Les horaires sont stockés en base de données")
        print("✅ Le système est opérationnel")
        
        # Afficher l'état après test
        show_current_state()
        
        print("\n🎯 INSTRUCTIONS POUR TEST MANUEL:")
        print("="*35)
        print("1. Connectez-vous comme admin: test_admin / admin123")
        print("2. Allez au dashboard admin")
        print("3. Cliquez sur 'Coach Management'")
        print("4. Sélectionnez un coach et cliquez 'View Schedule'")
        print("5. Utilisez le formulaire 'Add New Schedule Slot'")
        print("6. Vérifiez que l'horaire apparaît dans la liste")
        
    else:
        print("\n❌ PROBLÈME DÉTECTÉ!")
        print("="*25)
        print("Le système de création d'horaires ne fonctionne pas correctement.")
        print("Vérifiez les erreurs ci-dessus pour diagnostiquer le problème.")
        
        # Afficher l'état pour debug
        show_current_state()
