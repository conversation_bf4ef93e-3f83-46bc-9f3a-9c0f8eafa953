{% load static %}
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Leaderboards - Elite Sports Club</title>
    
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.0/font/bootstrap-icons.css" rel="stylesheet">
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <style>
        :root {
            --primary-color: #6366f1;
            --secondary-color: #10b981;
            --accent-color: #f59e0b;
            --danger-color: #ef4444;
            --gray-50: #f9fafb;
            --gray-100: #f3f4f6;
            --gray-200: #e5e7eb;
            --gray-600: #4b5563;
            --gray-700: #374151;
            --gray-800: #1f2937;
            --gold: #fbbf24;
            --silver: #9ca3af;
            --bronze: #d97706;
        }

        body {
            font-family: 'Inter', sans-serif;
            background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
            color: var(--gray-700);
            padding-top: 80px;
        }

        .leaderboards-container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 2rem;
        }

        .page-header {
            background: white;
            border-radius: 16px;
            padding: 2rem;
            margin-bottom: 2rem;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
            border: 1px solid var(--gray-100);
        }

        .page-title {
            font-family: 'Poppins', sans-serif;
            font-size: 2.5rem;
            font-weight: 700;
            color: var(--gray-800);
            margin: 0;
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .title-icon {
            width: 56px;
            height: 56px;
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            border-radius: 16px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.5rem;
        }

        .leaderboard-tabs {
            background: white;
            border-radius: 16px;
            padding: 1.5rem;
            margin-bottom: 2rem;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            border: 1px solid var(--gray-100);
        }

        .nav-tabs {
            border: none;
            gap: 0.5rem;
        }

        .nav-tabs .nav-link {
            border: 2px solid var(--gray-200);
            border-radius: 12px;
            color: var(--gray-600);
            font-weight: 500;
            padding: 0.75rem 1.5rem;
            transition: all 0.2s ease;
        }

        .nav-tabs .nav-link.active {
            background: var(--primary-color);
            border-color: var(--primary-color);
            color: white;
        }

        .nav-tabs .nav-link:hover {
            border-color: var(--primary-color);
            color: var(--primary-color);
        }

        .podium-section {
            background: white;
            border-radius: 16px;
            padding: 2rem;
            margin-bottom: 2rem;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
            border: 1px solid var(--gray-100);
        }

        .podium {
            display: flex;
            justify-content: center;
            align-items: end;
            gap: 2rem;
            margin: 2rem 0;
        }

        .podium-place {
            text-align: center;
            transition: all 0.3s ease;
        }

        .podium-place:hover {
            transform: translateY(-8px);
        }

        .podium-avatar {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            margin: 0 auto 1rem;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 2rem;
            font-weight: 700;
            position: relative;
        }

        .podium-avatar.first {
            background: linear-gradient(135deg, var(--gold), #f59e0b);
            width: 100px;
            height: 100px;
            font-size: 2.5rem;
        }

        .podium-avatar.second {
            background: linear-gradient(135deg, var(--silver), #6b7280);
        }

        .podium-avatar.third {
            background: linear-gradient(135deg, var(--bronze), #92400e);
        }

        .podium-crown {
            position: absolute;
            top: -15px;
            left: 50%;
            transform: translateX(-50%);
            font-size: 1.5rem;
            color: var(--gold);
        }

        .podium-name {
            font-weight: 600;
            color: var(--gray-800);
            margin-bottom: 0.5rem;
        }

        .podium-score {
            font-size: 1.25rem;
            font-weight: 700;
            color: var(--primary-color);
        }

        .leaderboard-table {
            background: white;
            border-radius: 16px;
            overflow: hidden;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
            border: 1px solid var(--gray-100);
        }

        .table {
            margin: 0;
        }

        .table thead th {
            background: var(--gray-50);
            border: none;
            font-weight: 600;
            color: var(--gray-700);
            padding: 1rem;
        }

        .table tbody td {
            border: none;
            padding: 1rem;
            vertical-align: middle;
        }

        .table tbody tr {
            border-bottom: 1px solid var(--gray-100);
            transition: all 0.2s ease;
        }

        .table tbody tr:hover {
            background: var(--gray-50);
        }

        .rank-badge {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 600;
            font-size: 0.875rem;
        }

        .rank-badge.top-3 {
            color: white;
        }

        .rank-badge.rank-1 {
            background: var(--gold);
        }

        .rank-badge.rank-2 {
            background: var(--silver);
        }

        .rank-badge.rank-3 {
            background: var(--bronze);
        }

        .rank-badge:not(.top-3) {
            background: var(--gray-200);
            color: var(--gray-700);
        }

        .player-info {
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .player-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: 600;
            font-size: 1rem;
        }

        .player-details h6 {
            margin: 0;
            font-weight: 600;
            color: var(--gray-800);
        }

        .player-details small {
            color: var(--gray-600);
        }

        .score-badge {
            background: var(--primary-color);
            color: white;
            padding: 0.5rem 1rem;
            border-radius: 20px;
            font-weight: 600;
            font-size: 0.875rem;
        }

        .trend-indicator {
            display: flex;
            align-items: center;
            gap: 0.25rem;
            font-size: 0.875rem;
            font-weight: 500;
        }

        .trend-up {
            color: var(--secondary-color);
        }

        .trend-down {
            color: var(--danger-color);
        }

        .trend-same {
            color: var(--gray-500);
        }

        .filters-section {
            display: flex;
            gap: 1rem;
            align-items: center;
            margin-bottom: 1rem;
        }

        .filter-select {
            border-radius: 8px;
            border: 2px solid var(--gray-200);
            padding: 0.5rem 1rem;
            font-size: 0.875rem;
        }

        /* Responsive */
        @media (max-width: 768px) {
            .leaderboards-container {
                padding: 1rem;
            }
            
            .page-title {
                font-size: 2rem;
            }
            
            .podium {
                flex-direction: column;
                align-items: center;
            }
            
            .podium-place {
                margin-bottom: 1rem;
            }
            
            .filters-section {
                flex-direction: column;
                align-items: stretch;
            }
        }

        /* Animation */
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .animate-in {
            animation: fadeInUp 0.6s ease-out forwards;
        }

        @keyframes bounce {
            0%, 20%, 50%, 80%, 100% {
                transform: translateY(0);
            }
            40% {
                transform: translateY(-10px);
            }
            60% {
                transform: translateY(-5px);
            }
        }

        .podium-crown {
            animation: bounce 2s infinite;
        }
    </style>
</head>
<body>
    <!-- Include Navbar -->
    {% include 'html/navbar.html' %}

    <div class="leaderboards-container">
        <!-- Page Header -->
        <div class="page-header animate-in">
            <h1 class="page-title">
                <div class="title-icon">
                    <i class="bi bi-trophy"></i>
                </div>
                Leaderboards
            </h1>
            <p class="text-muted mt-2">See how you rank against other players and celebrate top performers</p>
        </div>

        <!-- Leaderboard Tabs -->
        <div class="leaderboard-tabs animate-in" style="animation-delay: 0.1s;">
            <ul class="nav nav-tabs" id="leaderboardTabs" role="tablist">
                <li class="nav-item" role="presentation">
                    <button class="nav-link active" id="overall-tab" data-bs-toggle="tab" data-bs-target="#overall" type="button" role="tab">
                        <i class="bi bi-trophy"></i> Overall
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="tennis-tab" data-bs-toggle="tab" data-bs-target="#tennis" type="button" role="tab">
                        <i class="bi bi-circle"></i> Tennis
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="football-tab" data-bs-toggle="tab" data-bs-target="#football" type="button" role="tab">
                        <i class="bi bi-hexagon"></i> Football
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="basketball-tab" data-bs-toggle="tab" data-bs-target="#basketball" type="button" role="tab">
                        <i class="bi bi-circle-fill"></i> Basketball
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="monthly-tab" data-bs-toggle="tab" data-bs-target="#monthly" type="button" role="tab">
                        <i class="bi bi-calendar-month"></i> This Month
                    </button>
                </li>
            </ul>
        </div>

        <!-- Tab Content -->
        <div class="tab-content" id="leaderboardTabContent">
            <!-- Overall Tab -->
            <div class="tab-pane fade show active" id="overall" role="tabpanel">
                <!-- Podium Section -->
                <div class="podium-section animate-in" style="animation-delay: 0.2s;">
                    <h3 class="text-center mb-4">🏆 Top Performers</h3>
                    <div class="podium">
                        <!-- Second Place -->
                        <div class="podium-place">
                            <div class="podium-avatar second">SW</div>
                            <h5 class="podium-name">Sarah Williams</h5>
                            <div class="podium-score">2,847 pts</div>
                        </div>
                        
                        <!-- First Place -->
                        <div class="podium-place">
                            <div class="podium-avatar first">
                                <i class="bi bi-crown podium-crown"></i>
                                AJ
                            </div>
                            <h5 class="podium-name">Alex Johnson</h5>
                            <div class="podium-score">3,156 pts</div>
                        </div>
                        
                        <!-- Third Place -->
                        <div class="podium-place">
                            <div class="podium-avatar third">MC</div>
                            <h5 class="podium-name">Mike Chen</h5>
                            <div class="podium-score">2,634 pts</div>
                        </div>
                    </div>
                </div>

                <!-- Leaderboard Table -->
                <div class="leaderboard-table animate-in" style="animation-delay: 0.3s;">
                    <div class="filters-section p-3">
                        <select class="filter-select">
                            <option>All Time</option>
                            <option>This Year</option>
                            <option>This Month</option>
                            <option>This Week</option>
                        </select>
                        <select class="filter-select">
                            <option>All Sports</option>
                            <option>Tennis</option>
                            <option>Football</option>
                            <option>Basketball</option>
                        </select>
                    </div>
                    
                    <table class="table">
                        <thead>
                            <tr>
                                <th>Rank</th>
                                <th>Player</th>
                                <th>Score</th>
                                <th>Matches</th>
                                <th>Win Rate</th>
                                <th>Trend</th>
                            </tr>
                        </thead>
                        <tbody id="leaderboardBody">
                            <!-- Leaderboard data will be populated here -->
                        </tbody>
                    </table>
                </div>
            </div>

            <!-- Other tabs would have similar structure -->
            <div class="tab-pane fade" id="tennis" role="tabpanel">
                <div class="text-center py-5">
                    <h3>Tennis Leaderboard</h3>
                    <p class="text-muted">Tennis-specific rankings and statistics</p>
                </div>
            </div>

            <div class="tab-pane fade" id="football" role="tabpanel">
                <div class="text-center py-5">
                    <h3>Football Leaderboard</h3>
                    <p class="text-muted">Football-specific rankings and statistics</p>
                </div>
            </div>

            <div class="tab-pane fade" id="basketball" role="tabpanel">
                <div class="text-center py-5">
                    <h3>Basketball Leaderboard</h3>
                    <p class="text-muted">Basketball-specific rankings and statistics</p>
                </div>
            </div>

            <div class="tab-pane fade" id="monthly" role="tabpanel">
                <div class="text-center py-5">
                    <h3>Monthly Leaderboard</h3>
                    <p class="text-muted">This month's top performers</p>
                </div>
            </div>
        </div>
    </div>

    <!-- JavaScript -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Sample leaderboard data
        const leaderboardData = [
            { rank: 1, name: "Alex Johnson", avatar: "AJ", score: 3156, matches: 45, winRate: 84, trend: "up", change: "+3" },
            { rank: 2, name: "Sarah Williams", avatar: "SW", score: 2847, matches: 38, winRate: 89, trend: "up", change: "+1" },
            { rank: 3, name: "Mike Chen", avatar: "MC", score: 2634, matches: 42, winRate: 76, trend: "down", change: "-1" },
            { rank: 4, name: "Emma Davis", avatar: "ED", score: 2456, matches: 35, winRate: 80, trend: "up", change: "+2" },
            { rank: 5, name: "James Wilson", avatar: "JW", score: 2298, matches: 29, winRate: 72, trend: "same", change: "0" },
            { rank: 6, name: "Lisa Rodriguez", avatar: "LR", score: 2187, matches: 33, winRate: 78, trend: "up", change: "+1" },
            { rank: 7, name: "David Kim", avatar: "DK", score: 2045, matches: 31, winRate: 71, trend: "down", change: "-2" },
            { rank: 8, name: "Maria Garcia", avatar: "MG", score: 1967, matches: 28, winRate: 75, trend: "up", change: "+3" },
            { rank: 9, name: "Tom Anderson", avatar: "TA", score: 1834, matches: 26, winRate: 69, trend: "same", change: "0" },
            { rank: 10, name: "Nina Patel", avatar: "NP", score: 1756, matches: 24, winRate: 73, trend: "up", change: "+1" }
        ];

        function renderLeaderboard() {
            const tbody = document.getElementById('leaderboardBody');
            tbody.innerHTML = '';

            leaderboardData.forEach(player => {
                const row = document.createElement('tr');
                
                const rankClass = player.rank <= 3 ? `rank-${player.rank} top-3` : '';
                const trendIcon = player.trend === 'up' ? 'bi-arrow-up' : 
                                 player.trend === 'down' ? 'bi-arrow-down' : 'bi-dash';
                const trendClass = `trend-${player.trend}`;
                
                row.innerHTML = `
                    <td>
                        <div class="rank-badge ${rankClass}">${player.rank}</div>
                    </td>
                    <td>
                        <div class="player-info">
                            <div class="player-avatar">${player.avatar}</div>
                            <div class="player-details">
                                <h6>${player.name}</h6>
                                <small>Elite Member</small>
                            </div>
                        </div>
                    </td>
                    <td>
                        <span class="score-badge">${player.score.toLocaleString()}</span>
                    </td>
                    <td>${player.matches}</td>
                    <td>${player.winRate}%</td>
                    <td>
                        <div class="trend-indicator ${trendClass}">
                            <i class="bi ${trendIcon}"></i>
                            ${player.change}
                        </div>
                    </td>
                `;
                
                tbody.appendChild(row);
            });
        }

        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            renderLeaderboard();
            
            // Add some interactive effects
            document.querySelectorAll('.podium-place').forEach(place => {
                place.addEventListener('mouseenter', function() {
                    this.style.transform = 'translateY(-8px) scale(1.05)';
                });
                
                place.addEventListener('mouseleave', function() {
                    this.style.transform = 'translateY(0) scale(1)';
                });
            });
        });

        // Tab change handler
        document.querySelectorAll('[data-bs-toggle="tab"]').forEach(tab => {
            tab.addEventListener('shown.bs.tab', function(e) {
                const target = e.target.getAttribute('data-bs-target');
                if (target === '#overall') {
                    renderLeaderboard();
                }
                // Here you would load different data for different tabs
            });
        });
    </script>
</body>
</html>
