{% load static %}
<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Elite Sports Club</title>

    <!-- Bootstrap Icons -->
    <link
      href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.0/font/bootstrap-icons.css"
      rel="stylesheet"
    />
    <!-- Bootstrap CSS -->
    <link
      href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css"
      rel="stylesheet"
    />
    <!-- Google Fonts -->
    <link
      href="https://fonts.googleapis.com/css2?family=Orbitron:wght@400;700;900&family=Rajdhani:wght@300;400;500;600;700&display=swap"
      rel="stylesheet"
    />

    <style>
      :root {
        --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        --secondary-gradient: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
        --accent-gradient: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
        --success-gradient: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);
        --warning-gradient: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
        --dark-gradient: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
        --sports-blue: #1e40af;
        --sports-orange: #f97316;
        --sports-green: #10b981;
        --sports-purple: #8b5cf6;
      }

      * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
      }

      body {
        font-family: "Rajdhani", sans-serif;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        min-height: 100vh;
      }

      .sports-navbar {
        background: linear-gradient(
          135deg,
          rgba(30, 64, 175, 0.95) 0%,
          rgba(139, 92, 246, 0.95) 50%,
          rgba(16, 185, 129, 0.95) 100%
        );
        backdrop-filter: blur(20px);
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        transition: all 0.3s ease;
        padding: 0.75rem 0;
        position: fixed;
        top: 0;
        width: 100%;
        z-index: 1000;
      }

      .sports-navbar.scrolled {
        background: linear-gradient(
          135deg,
          rgba(30, 64, 175, 0.98) 0%,
          rgba(139, 92, 246, 0.98) 50%,
          rgba(16, 185, 129, 0.98) 100%
        );
        backdrop-filter: blur(25px);
        box-shadow: 0 12px 40px rgba(0, 0, 0, 0.2);
        padding: 0.5rem 0;
      }

      .sports-brand {
        font-family: "Orbitron", monospace;
        font-size: 1.8rem;
        font-weight: 900;
        background: linear-gradient(45deg, #fff, #f8f9fa, #e9ecef);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
        text-decoration: none;
        display: flex;
        align-items: center;
        transition: all 0.3s ease;
        text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
      }

      .sports-brand:hover {
        transform: scale(1.05);
        filter: brightness(1.2);
      }

      .brand-icon {
        width: 45px;
        height: 45px;
        margin-right: 15px;
        background: var(--accent-gradient);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 1.4rem;
        box-shadow: 0 4px 15px rgba(79, 172, 254, 0.4);
        animation: pulse 3s infinite;
        position: relative;
        overflow: hidden;
      }

      .brand-icon::before {
        content: "";
        position: absolute;
        top: -50%;
        left: -50%;
        width: 200%;
        height: 200%;
        background: linear-gradient(
          45deg,
          transparent,
          rgba(255, 255, 255, 0.3),
          transparent
        );
        transform: rotate(45deg);
        animation: shine 2s infinite;
      }

      @keyframes pulse {
        0% {
          box-shadow: 0 4px 15px rgba(79, 172, 254, 0.4);
        }
        50% {
          box-shadow: 0 4px 25px rgba(79, 172, 254, 0.7);
        }
        100% {
          box-shadow: 0 4px 15px rgba(79, 172, 254, 0.4);
        }
      }

      @keyframes shine {
        0% {
          transform: translateX(-100%) translateY(-100%) rotate(45deg);
        }
        100% {
          transform: translateX(100%) translateY(100%) rotate(45deg);
        }
      }

      .sports-nav-link {
        color: rgba(255, 255, 255, 0.9) !important;
        font-weight: 600;
        font-size: 1.1rem;
        padding: 0.75rem 1.5rem !important;
        border-radius: 30px;
        transition: all 0.3s ease;
        position: relative;
        overflow: hidden;
        margin: 0 0.25rem;
        text-decoration: none;
        display: flex;
        align-items: center;
        text-transform: uppercase;
        letter-spacing: 0.5px;
      }

      .sports-nav-link::before {
        content: "";
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(
          90deg,
          transparent,
          rgba(255, 255, 255, 0.2),
          transparent
        );
        transition: left 0.5s;
      }

      .sports-nav-link:hover::before {
        left: 100%;
      }

      .sports-nav-link:hover {
        color: white !important;
        background: rgba(255, 255, 255, 0.15);
        transform: translateY(-3px);
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
      }

      .sports-nav-link.active {
        background: var(--accent-gradient);
        color: white !important;
        box-shadow: 0 8px 25px rgba(79, 172, 254, 0.5);
      }

      .nav-icon {
        margin-right: 10px;
        font-size: 1.2rem;
        transition: transform 0.3s ease;
      }

      .sports-nav-link:hover .nav-icon {
        transform: scale(1.3) rotate(10deg);
      }

      .sports-dropdown {
        background: linear-gradient(
          135deg,
          rgba(255, 255, 255, 0.95) 0%,
          rgba(248, 249, 250, 0.95) 100%
        );
        backdrop-filter: blur(20px);
        border: 1px solid rgba(255, 255, 255, 0.2);
        border-radius: 20px;
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
        padding: 1rem 0;
        margin-top: 0.5rem;
      }

      .sports-dropdown-item {
        color: #2c3e50 !important;
        padding: 1rem 2rem;
        font-weight: 600;
        transition: all 0.3s ease;
        border-radius: 0;
        display: flex;
        align-items: center;
        font-size: 1.05rem;
      }

      .sports-dropdown-item:hover {
        background: var(--primary-gradient);
        color: white !important;
        transform: translateX(10px);
      }

      .sports-dropdown-item i {
        margin-right: 15px;
        width: 25px;
        text-align: center;
        font-size: 1.2rem;
      }

      /* Enhanced Dropdown Styles */
      .dropdown-menu {
        border: none;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
        border-radius: 15px;
        padding: 0.5rem 0;
        margin-top: 0.5rem;
        min-width: 250px;
        backdrop-filter: blur(20px);
      }

      .dropdown-divider {
        margin: 0.5rem 1rem;
        border-color: rgba(255, 255, 255, 0.2);
      }

      /* Notification Badge */
      .notification-badge {
        position: absolute;
        top: -5px;
        right: -5px;
        background: var(--danger-gradient);
        color: white;
        border-radius: 50%;
        width: 18px;
        height: 18px;
        font-size: 0.7rem;
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: 600;
        animation: pulse 2s infinite;
      }

      @keyframes pulse {
        0% {
          transform: scale(1);
        }
        50% {
          transform: scale(1.1);
        }
        100% {
          transform: scale(1);
        }
      }

      .user-avatar {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        background: var(--success-gradient);
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 12px;
        color: white;
        font-weight: bold;
        font-size: 1.1rem;
        box-shadow: 0 4px 15px rgba(17, 153, 142, 0.4);
        border: 2px solid rgba(255, 255, 255, 0.3);
      }

      .auth-buttons .sports-nav-link {
        margin: 0 0.5rem;
        font-size: 1rem;
        padding: 0.6rem 1.8rem !important;
      }

      .login-btn {
        background: transparent;
        border: 2px solid rgba(255, 255, 255, 0.4);
      }

      .login-btn:hover {
        background: rgba(255, 255, 255, 0.15);
        border-color: rgba(255, 255, 255, 0.8);
      }

      .register-btn {
        background: var(--success-gradient);
        border: 2px solid transparent;
      }

      .register-btn:hover {
        background: var(--warning-gradient);
        transform: translateY(-3px);
        box-shadow: 0 10px 30px rgba(252, 182, 159, 0.5);
      }

      .navbar-toggler {
        border: none;
        padding: 0.75rem;
        border-radius: 15px;
        background: rgba(255, 255, 255, 0.1);
        transition: all 0.3s ease;
      }

      .navbar-toggler:hover {
        background: rgba(255, 255, 255, 0.2);
        transform: scale(1.1);
      }

      .navbar-toggler-icon {
        background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 30 30'%3e%3cpath stroke='rgba%28255, 255, 255, 0.9%29' stroke-linecap='round' stroke-miterlimit='10' stroke-width='2' d='m4 7h22M4 15h22M4 23h22'/%3e%3c/svg%3e");
      }

      /* Mobile Responsive */
      @media (max-width: 991.98px) {
        .sports-navbar {
          padding: 1rem 0;
        }

        .sports-brand {
          font-size: 1.5rem;
        }

        .brand-icon {
          width: 40px;
          height: 40px;
          margin-right: 12px;
        }

        .navbar-collapse {
          background: linear-gradient(
            135deg,
            rgba(30, 64, 175, 0.98) 0%,
            rgba(139, 92, 246, 0.98) 100%
          );
          backdrop-filter: blur(25px);
          margin-top: 1.5rem;
          border-radius: 20px;
          padding: 2rem;
          box-shadow: 0 15px 35px rgba(0, 0, 0, 0.3);
        }

        .sports-nav-link {
          margin: 0.5rem 0;
          text-align: center;
          justify-content: center;
        }
      }

      /* Scroll Animation */
      .navbar-scroll-hidden {
        transform: translateY(-100%);
        transition: transform 0.3s ease-in-out;
      }

      .navbar-scroll-visible {
        transform: translateY(0);
        transition: transform 0.3s ease-in-out;
      }

      /* Sports Icons Animation */
      .sports-icons {
        display: flex;
        gap: 15px;
        margin-left: 20px;
      }

      .sport-icon {
        width: 35px;
        height: 35px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 1rem;
        transition: all 0.3s ease;
        cursor: pointer;
      }

      .sport-icon.tennis {
        background: var(--success-gradient);
      }

      .sport-icon.football {
        background: var(--sports-orange);
      }

      .sport-icon.basketball {
        background: var(--sports-purple);
      }

      .sport-icon:hover {
        transform: scale(1.2) rotate(360deg);
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
      }
    </style>
  </head>
  <body>
    <nav class="navbar navbar-expand-lg sports-navbar" id="sportsNavbar">
      <div class="container-fluid px-4">
        <!-- Brand -->
        <a class="sports-brand" href="{% url 'home' %}">
          <div class="brand-icon">
            <i class="bi bi-trophy-fill"></i>
          </div>
          <span>ELITE SPORTS</span>
        </a>

        <!-- Sports Icons -->
        <div class="sports-icons d-none d-lg-flex">
          <div class="sport-icon tennis" title="Tennis">
            <i class="bi bi-circle"></i>
          </div>
          <div class="sport-icon football" title="Football">
            <i class="bi bi-hexagon-fill"></i>
          </div>
          <div class="sport-icon basketball" title="Basketball">
            <i class="bi bi-circle-fill"></i>
          </div>
        </div>

        <!-- Mobile Toggle -->
        <button
          class="navbar-toggler"
          type="button"
          data-bs-toggle="collapse"
          data-bs-target="#navbarNav"
          aria-controls="navbarNav"
          aria-expanded="false"
          aria-label="Toggle navigation"
        >
          <span class="navbar-toggler-icon"></span>
        </button>

        <!-- Navigation Menu -->
        <div class="collapse navbar-collapse" id="navbarNav">
          <ul class="navbar-nav me-auto" id="mainNavMenu">
            <!-- Navigation items will be populated by JavaScript -->
          </ul>

          <!-- User Authentication -->
          <ul class="navbar-nav auth-buttons">
            {% if user.is_authenticated %}
            <li class="nav-item dropdown">
              <a
                class="nav-link dropdown-toggle sports-nav-link"
                href="#"
                id="navbarDropdown"
                role="button"
                data-bs-toggle="dropdown"
                aria-expanded="false"
              >
                <div class="user-avatar">{{ user.username|first|upper }}</div>
                {{ user.username }}
              </a>
              <ul class="dropdown-menu sports-dropdown">
                {% if user.role == 'admin' %}
                <li>
                  <a
                    class="dropdown-item sports-dropdown-item"
                    href="{% url 'admin_dashboard' %}"
                  >
                    <i class="bi bi-speedometer2"></i>Admin Dashboard
                  </a>
                </li>
                <li><hr class="dropdown-divider" /></li>
                <li>
                  <a
                    class="dropdown-item sports-dropdown-item"
                    href="/admin/members/"
                  >
                    <i class="bi bi-people"></i>Member Management
                  </a>
                </li>
                <li>
                  <a
                    class="dropdown-item sports-dropdown-item"
                    href="/admin/schedule-builder/"
                  >
                    <i class="bi bi-calendar-plus"></i>Schedule Builder
                  </a>
                </li>
                <li>
                  <a
                    class="dropdown-item sports-dropdown-item"
                    href="/admin/reports/"
                  >
                    <i class="bi bi-file-earmark-text"></i>Export Reports
                  </a>
                </li>
                <li>
                  <a
                    class="dropdown-item sports-dropdown-item"
                    href="/admin/documents/"
                  >
                    <i class="bi bi-file-earmark-arrow-up"></i>Document
                    Management
                  </a>
                </li>
                <li>
                  <a
                    class="dropdown-item sports-dropdown-item"
                    href="/admin/notifications/"
                  >
                    <i class="bi bi-bell"></i>Send Notifications
                  </a>
                </li>
                {% elif user.role == 'coach' %}
                <li>
                  <a
                    class="dropdown-item sports-dropdown-item"
                    href="{% url 'coach_dashboard' %}"
                  >
                    <i class="bi bi-person-badge"></i>Coach Dashboard
                  </a>
                </li>
                <li><hr class="dropdown-divider" /></li>
                <li>
                  <a
                    class="dropdown-item sports-dropdown-item"
                    href="/coach/attendance/"
                  >
                    <i class="bi bi-check2-square"></i>View Attendance
                  </a>
                </li>
                <li>
                  <a
                    class="dropdown-item sports-dropdown-item"
                    href="/coach/training-plans/"
                  >
                    <i class="bi bi-clipboard-data"></i>Training Plans
                  </a>
                </li>
                <li>
                  <a
                    class="dropdown-item sports-dropdown-item"
                    href="/coach/communicate/"
                  >
                    <i class="bi bi-chat-square-text"></i>Communicate with
                    Players
                  </a>
                </li>
                {% else %}
                <li>
                  <a
                    class="dropdown-item sports-dropdown-item"
                    href="{% url 'joueur_dashboard' %}"
                  >
                    <i class="bi bi-person-workspace"></i>Player Dashboard
                  </a>
                </li>
                <li><hr class="dropdown-divider" /></li>
                <li>
                  <a
                    class="dropdown-item sports-dropdown-item"
                    href="/player/profile/"
                  >
                    <i class="bi bi-person-badge"></i>My Profile & Stats
                  </a>
                </li>
                <li>
                  <a
                    class="dropdown-item sports-dropdown-item"
                    href="/player/achievements/"
                  >
                    <i class="bi bi-award"></i>My Achievements
                  </a>
                </li>
                <li>
                  <a
                    class="dropdown-item sports-dropdown-item"
                    href="/player/bookings/"
                  >
                    <i class="bi bi-calendar-check"></i>My Bookings
                  </a>
                </li>
                {% endif %}
                <li><hr class="dropdown-divider" /></li>
                <li>
                  <a
                    class="dropdown-item sports-dropdown-item"
                    href="{% url 'logout' %}"
                  >
                    <i class="bi bi-box-arrow-right"></i>Logout
                  </a>
                </li>
              </ul>
            </li>
            {% else %}
            <li class="nav-item">
              <a
                class="nav-link sports-nav-link login-btn"
                href="{% url 'login' %}"
              >
                <i class="bi bi-box-arrow-in-right nav-icon"></i>Login
              </a>
            </li>
            <li class="nav-item">
              <a
                class="nav-link sports-nav-link register-btn"
                href="{% url 'register' %}"
              >
                <i class="bi bi-person-plus nav-icon"></i>Register
              </a>
            </li>
            {% endif %}
          </ul>
        </div>
      </div>
    </nav>

    <!-- JavaScript for Navbar Effects -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
      // Navbar scroll effect
      let lastScrollTop = 0;
      const navbar = document.getElementById("sportsNavbar");

      window.addEventListener("scroll", function () {
        let scrollTop =
          window.pageYOffset || document.documentElement.scrollTop;

        if (scrollTop > 100) {
          navbar.classList.add("scrolled");
        } else {
          navbar.classList.remove("scrolled");
        }

        // Hide/show navbar on scroll
        if (scrollTop > lastScrollTop && scrollTop > 200) {
          navbar.classList.add("navbar-scroll-hidden");
        } else {
          navbar.classList.remove("navbar-scroll-hidden");
        }
        lastScrollTop = scrollTop;
      });

      // Active link highlighting
      const currentLocation = location.pathname;
      const menuItems = document.querySelectorAll(".sports-nav-link");

      menuItems.forEach((item) => {
        if (item.getAttribute("href") === currentLocation) {
          item.classList.add("active");
        }
      });

      // Sports icons animation
      const sportIcons = document.querySelectorAll(".sport-icon");
      sportIcons.forEach((icon) => {
        icon.addEventListener("click", function () {
          this.style.transform = "scale(1.5) rotate(720deg)";
          setTimeout(() => {
            this.style.transform = "";
          }, 600);
        });
      });

      // Smooth scrolling for anchor links
      document.querySelectorAll('a[href^="#"]').forEach((anchor) => {
        anchor.addEventListener("click", function (e) {
          e.preventDefault();
          const target = document.querySelector(this.getAttribute("href"));
          if (target) {
            target.scrollIntoView({
              behavior: "smooth",
              block: "start",
            });
          }
        });
      });

      // Dynamic navbar based on authentication status
      function updateNavbar() {
        const token = localStorage.getItem("token");
        const isAuthenticated = token && token !== "null";
        const mainNavMenu = document.getElementById("mainNavMenu");
        const authButtons = document.querySelector(".auth-buttons");

        if (isAuthenticated) {
          // Authenticated user navbar
          mainNavMenu.innerHTML = `
            <li class="nav-item">
              <a class="nav-link sports-nav-link" href="/">
                <i class="bi bi-house-door nav-icon"></i>Home
              </a>
            </li>

            <!-- Member Area Dropdown -->
            <li class="nav-item dropdown">
              <a class="nav-link dropdown-toggle sports-nav-link" href="#" id="memberAreaDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                <i class="bi bi-people nav-icon"></i>Member Area
              </a>
              <ul class="dropdown-menu sports-dropdown">
                <li><a class="dropdown-item sports-dropdown-item" href="/member/profiles/"><i class="bi bi-person-badge"></i>Player Profiles</a></li>
                <li><a class="dropdown-item sports-dropdown-item" href="/member/stats/"><i class="bi bi-graph-up"></i>Stats & Achievements</a></li>
                <li><a class="dropdown-item sports-dropdown-item" href="/member/schedule/"><i class="bi bi-calendar-event"></i>My Schedule</a></li>
                <li><a class="dropdown-item sports-dropdown-item" href="/member/messages/"><i class="bi bi-chat-dots"></i>Messages</a></li>
                <li><a class="dropdown-item sports-dropdown-item" href="/member/parent-portal/"><i class="bi bi-shield-check"></i>Parent Portal</a></li>
              </ul>
            </li>

            <!-- Events Dropdown -->
            <li class="nav-item dropdown">
              <a class="nav-link dropdown-toggle sports-nav-link" href="#" id="eventsDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                <i class="bi bi-calendar-event nav-icon"></i>Events
              </a>
              <ul class="dropdown-menu sports-dropdown">
                <li><a class="dropdown-item sports-dropdown-item" href="/events/calendar/"><i class="bi bi-calendar3"></i>Interactive Calendar</a></li>
                <li><a class="dropdown-item sports-dropdown-item" href="/events/tournaments/"><i class="bi bi-trophy"></i>Tournaments</a></li>
                <li><a class="dropdown-item sports-dropdown-item" href="/events/camps/"><i class="bi bi-tent"></i>Training Camps</a></li>
                <li><a class="dropdown-item sports-dropdown-item" href="/events/matches/"><i class="bi bi-lightning"></i>Live Matches</a></li>
                <li><a class="dropdown-item sports-dropdown-item" href="/events/results/"><i class="bi bi-award"></i>Results & Scores</a></li>
              </ul>
            </li>

            <!-- Shop Dropdown -->
            <li class="nav-item dropdown">
              <a class="nav-link dropdown-toggle sports-nav-link" href="#" id="shopDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                <i class="bi bi-shop nav-icon"></i>Shop
              </a>
              <ul class="dropdown-menu sports-dropdown">
                <li><a class="dropdown-item sports-dropdown-item" href="/shop/membership/"><i class="bi bi-credit-card"></i>Membership Plans</a></li>
                <li><a class="dropdown-item sports-dropdown-item" href="/shop/store/"><i class="bi bi-bag"></i>Equipment Store</a></li>
                <li><a class="dropdown-item sports-dropdown-item" href="/shop/tickets/"><i class="bi bi-ticket"></i>Event Tickets</a></li>
                <li><a class="dropdown-item sports-dropdown-item" href="/shop/donations/"><i class="bi bi-heart"></i>Donations</a></li>
              </ul>
            </li>

            <!-- Stats Dropdown -->
            <li class="nav-item dropdown">
              <a class="nav-link dropdown-toggle sports-nav-link" href="#" id="statsDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                <i class="bi bi-graph-up nav-icon"></i>Stats
              </a>
              <ul class="dropdown-menu sports-dropdown">
                <li><a class="dropdown-item sports-dropdown-item" href="/stats/performance/"><i class="bi bi-speedometer2"></i>Performance Dashboard</a></li>
                <li><a class="dropdown-item sports-dropdown-item" href="/stats/leaderboards/"><i class="bi bi-trophy"></i>Leaderboards</a></li>
                <li><a class="dropdown-item sports-dropdown-item" href="/stats/mvp/"><i class="bi bi-star"></i>MVP Voting</a></li>
                <li><a class="dropdown-item sports-dropdown-item" href="/stats/rankings/"><i class="bi bi-list-ol"></i>Rankings</a></li>
              </ul>
            </li>

            <li class="nav-item">
              <a class="nav-link sports-nav-link" href="/services/">
                <i class="bi bi-gear-fill nav-icon"></i>Services
              </a>
            </li>
            <li class="nav-item">
              <a class="nav-link sports-nav-link" href="/blog/">
                <i class="bi bi-journal-text nav-icon"></i>Blog
              </a>
            </li>
            <li class="nav-item">
              <a class="nav-link sports-nav-link" href="/contact/">
                <i class="bi bi-envelope-fill nav-icon"></i>Contact
              </a>
            </li>
          `;
        } else {
          // Non-authenticated user navbar (simple)
          mainNavMenu.innerHTML = `
            <li class="nav-item">
              <a class="nav-link sports-nav-link" href="/">
                <i class="bi bi-house-door nav-icon"></i>Home
              </a>
            </li>
            <li class="nav-item">
              <a class="nav-link sports-nav-link" href="/services/">
                <i class="bi bi-gear-fill nav-icon"></i>Services
              </a>
            </li>
            <li class="nav-item">
              <a class="nav-link sports-nav-link" href="/blog/">
                <i class="bi bi-journal-text nav-icon"></i>Blog
              </a>
            </li>
            <li class="nav-item">
              <a class="nav-link sports-nav-link" href="/contact/">
                <i class="bi bi-envelope-fill nav-icon"></i>Contact
              </a>
            </li>
          `;
        }
      }

      // Initialize navbar on page load
      document.addEventListener("DOMContentLoaded", function () {
        updateNavbar();
      });

      // Listen for authentication changes
      window.addEventListener("storage", function (e) {
        if (e.key === "token") {
          updateNavbar();
        }
      });
    </script>
  </body>
</html>
