#!/usr/bin/env python
"""
Script to check user data in database
"""
import os
import sys
import django

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'tennis_manager.settings')
django.setup()

from django.contrib.auth import get_user_model

User = get_user_model()

def check_users():
    print("🔍 Checking User Data...")
    
    try:
        users = User.objects.all()
        print(f"Total users: {users.count()}")
        
        for user in users:
            print(f"\n👤 User: {user.username}")
            print(f"   Email: {user.email}")
            print(f"   Role: {user.role}")
            print(f"   Active: {user.is_active}")
            print(f"   Staff: {user.is_staff}")
            print(f"   Superuser: {user.is_superuser}")
        
        # Check specific users
        print(f"\n🎯 Checking specific test users...")
        
        test_users = ['player_alice', 'player_bob', 'test_player_fresh']
        for username in test_users:
            user = User.objects.filter(username=username).first()
            if user:
                print(f"✅ {username}: email={user.email}, role={user.role}, active={user.is_active}")
            else:
                print(f"❌ {username}: Not found")
        
        return True
        
    except Exception as e:
        print(f"❌ Error checking users: {e}")
        return False

if __name__ == '__main__':
    print("🎾 Tennis Management System - User Data Check")
    print("="*60)
    
    check_users()
