#!/usr/bin/env python
"""
Script to create test reservations for players
"""
import os
import sys
import django
from datetime import datetime, timedelta, time, date

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'tennis_manager.settings')
django.setup()

from django.contrib.auth import get_user_model
from reservations.models import Reservation, ReservationCoach, Terrain, Coach
from django.utils import timezone

User = get_user_model()

def create_test_reservations():
    print("🧪 Creating Test Reservations for Players...")
    
    try:
        # Get test users
        player_alice = User.objects.filter(username='player_alice').first()
        player_bob = User.objects.filter(username='player_bob').first()
        fresh_player = User.objects.filter(username='test_player_fresh').first()
        
        if not player_alice or not player_bob:
            print("❌ Test users not found. Run create_test_data.py first.")
            return False
        
        # Get courts and coaches
        courts = Terrain.objects.all()
        coaches = Coach.objects.all()
        
        if not courts.exists() or not coaches.exists():
            print("❌ Courts or coaches not found. Run create_test_data.py first.")
            return False
        
        print(f"✅ Found {courts.count()} courts and {coaches.count()} coaches")
        
        # Create court reservations for Alice
        print("\n1️⃣ Creating court reservations for Alice...")
        
        # Past reservation
        past_date = date.today() - timedelta(days=5)
        alice_past_court = Reservation.objects.create(
            user=player_alice,
            terrain=courts.first(),
            date=past_date,
            start_time=time(10, 0),
            end_time=time(11, 0)
        )
        print(f"✅ Created past court reservation: {alice_past_court}")
        
        # Upcoming reservation
        future_date = date.today() + timedelta(days=3)
        alice_future_court = Reservation.objects.create(
            user=player_alice,
            terrain=courts.last(),
            date=future_date,
            start_time=time(14, 0),
            end_time=time(15, 30)
        )
        print(f"✅ Created upcoming court reservation: {alice_future_court}")
        
        # Create coach reservations for Alice
        print("\n2️⃣ Creating coach reservations for Alice...")
        
        # Past coach session
        alice_past_coach = ReservationCoach.objects.create(
            user=player_alice,
            coach=coaches.first(),
            date=past_date,
            start_time=time(16, 0),
            end_time=time(17, 0),
            total_price=75.00
        )
        print(f"✅ Created past coach reservation: {alice_past_coach}")
        
        # Upcoming coach session
        alice_future_coach = ReservationCoach.objects.create(
            user=player_alice,
            coach=coaches.last(),
            date=future_date + timedelta(days=1),
            start_time=time(9, 0),
            end_time=time(10, 0),
            total_price=85.00
        )
        print(f"✅ Created upcoming coach reservation: {alice_future_coach}")
        
        # Create reservations for Bob
        print("\n3️⃣ Creating reservations for Bob...")
        
        # Bob's court reservation
        bob_court = Reservation.objects.create(
            user=player_bob,
            terrain=courts[1] if courts.count() > 1 else courts.first(),
            date=date.today() + timedelta(days=2),
            start_time=time(11, 0),
            end_time=time(12, 30)
        )
        print(f"✅ Created Bob's court reservation: {bob_court}")
        
        # Bob's coach session
        bob_coach = ReservationCoach.objects.create(
            user=player_bob,
            coach=coaches[1] if coaches.count() > 1 else coaches.first(),
            date=date.today() + timedelta(days=4),
            start_time=time(15, 0),
            end_time=time(16, 0),
            total_price=50.00
        )
        print(f"✅ Created Bob's coach reservation: {bob_coach}")
        
        # Create reservations for fresh player if exists
        if fresh_player:
            print("\n4️⃣ Creating reservations for fresh player...")
            
            fresh_court = Reservation.objects.create(
                user=fresh_player,
                terrain=courts.first(),
                date=date.today() + timedelta(days=1),
                start_time=time(13, 0),
                end_time=time(14, 0)
            )
            print(f"✅ Created fresh player's court reservation: {fresh_court}")
        
        # Summary
        print("\n📊 Reservation Summary:")
        print(f"   Alice - Court reservations: {Reservation.objects.filter(user=player_alice).count()}")
        print(f"   Alice - Coach reservations: {ReservationCoach.objects.filter(user=player_alice).count()}")
        print(f"   Bob - Court reservations: {Reservation.objects.filter(user=player_bob).count()}")
        print(f"   Bob - Coach reservations: {ReservationCoach.objects.filter(user=player_bob).count()}")
        
        if fresh_player:
            print(f"   Fresh Player - Court reservations: {Reservation.objects.filter(user=fresh_player).count()}")
        
        print(f"\n📈 Total Reservations Created:")
        print(f"   Court Reservations: {Reservation.objects.count()}")
        print(f"   Coach Reservations: {ReservationCoach.objects.count()}")
        
        print("\n🎯 Test the player dashboard at:")
        print("   Alice: http://127.0.0.1:8000/auth/login/ (player_alice / player123)")
        print("   Bob: http://127.0.0.1:8000/auth/login/ (player_bob / player123)")
        if fresh_player:
            print("   Fresh: http://127.0.0.1:8000/auth/login/ (test_player_fresh / test123)")
        
        print("\n✅ Navigate to 'My Reservations' to see the reservation history!")
        
        return True
        
    except Exception as e:
        print(f"❌ Error creating test reservations: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == '__main__':
    print("🎾 Tennis Management System - Test Reservations Creator")
    print("="*60)
    
    success = create_test_reservations()
    
    if success:
        print("\n🚀 Test reservations created successfully!")
        print("✅ Players now have their own reservation history.")
    else:
        print("\n⚠️  Test reservation creation failed.")
