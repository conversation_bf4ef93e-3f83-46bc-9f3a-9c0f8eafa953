#!/usr/bin/env python
"""
Script to test court reservation API
"""
import os
import sys
import django
import requests
import json
from datetime import datetime, timedelta, date

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'tennis_manager.settings')
django.setup()

from django.contrib.auth import get_user_model
from reservations.models import Terrain, Reservation
from django.test import Client
from django.urls import reverse

User = get_user_model()

def test_court_reservation_api():
    print("🧪 Testing Court Reservation API...")
    
    try:
        # Get test user
        user = User.objects.filter(username='player_alice').first()
        if not user:
            print("❌ Test user 'player_alice' not found")
            return False
        
        # Get available courts
        courts = Terrain.objects.filter(available=True)
        if not courts.exists():
            print("❌ No available courts found")
            return False
        
        court = courts.first()
        print(f"✅ Using court: {court.name} (ID: {court.id})")
        
        # Test data
        tomorrow = date.today() + timedelta(days=1)
        test_data = {
            'terrain_id': court.id,
            'date': tomorrow.strftime('%Y-%m-%d'),
            'start_time': '10:00',
            'end_time': '11:00'
        }
        
        print(f"📋 Test reservation data:")
        print(f"   Court: {court.name} (ID: {court.id})")
        print(f"   Date: {test_data['date']}")
        print(f"   Time: {test_data['start_time']} - {test_data['end_time']}")
        
        # Use Django test client to simulate API call
        client = Client()
        
        # Login the user (simulate JWT authentication)
        client.force_login(user)
        
        # Test the API endpoint
        url = '/res/api/court-reservations/'
        response = client.post(
            url,
            data=json.dumps(test_data),
            content_type='application/json'
        )
        
        print(f"\n📡 API Response:")
        print(f"   Status Code: {response.status_code}")
        print(f"   Response: {response.content.decode()}")
        
        if response.status_code == 200:
            response_data = json.loads(response.content)
            print(f"✅ Reservation created successfully!")
            print(f"   Reservation ID: {response_data.get('reservation_id')}")
            print(f"   Price: ${response_data.get('price')}")
            print(f"   Court: {response_data.get('court_name')}")
            
            # Verify in database
            reservation = Reservation.objects.filter(
                user=user,
                terrain=court,
                date=tomorrow
            ).first()
            
            if reservation:
                print(f"✅ Reservation verified in database: {reservation}")
                return True
            else:
                print(f"❌ Reservation not found in database")
                return False
        else:
            print(f"❌ API call failed with status {response.status_code}")
            try:
                error_data = json.loads(response.content)
                print(f"   Error: {error_data.get('error', 'Unknown error')}")
            except:
                print(f"   Raw response: {response.content.decode()}")
            return False
        
    except Exception as e:
        print(f"❌ Error testing court reservation API: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_validation_scenarios():
    print("\n🧪 Testing Validation Scenarios...")
    
    try:
        user = User.objects.filter(username='player_alice').first()
        court = Terrain.objects.filter(available=True).first()
        
        if not user or not court:
            print("❌ Missing test data")
            return False
        
        client = Client()
        client.force_login(user)
        url = '/res/api/court-reservations/'
        
        # Test 1: Missing required field
        print("\n1️⃣ Testing missing required field...")
        test_data = {
            'terrain_id': court.id,
            'date': '2025-06-10',
            # Missing start_time and end_time
        }
        
        response = client.post(url, data=json.dumps(test_data), content_type='application/json')
        print(f"   Status: {response.status_code} (Expected: 400)")
        if response.status_code == 400:
            print("   ✅ Correctly rejected missing fields")
        
        # Test 2: Invalid date format
        print("\n2️⃣ Testing invalid date format...")
        test_data = {
            'terrain_id': court.id,
            'date': 'invalid-date',
            'start_time': '10:00',
            'end_time': '11:00'
        }
        
        response = client.post(url, data=json.dumps(test_data), content_type='application/json')
        print(f"   Status: {response.status_code} (Expected: 400)")
        if response.status_code == 400:
            print("   ✅ Correctly rejected invalid date")
        
        # Test 3: Start time after end time
        print("\n3️⃣ Testing invalid time logic...")
        tomorrow = date.today() + timedelta(days=1)
        test_data = {
            'terrain_id': court.id,
            'date': tomorrow.strftime('%Y-%m-%d'),
            'start_time': '15:00',
            'end_time': '14:00'  # End before start
        }
        
        response = client.post(url, data=json.dumps(test_data), content_type='application/json')
        print(f"   Status: {response.status_code} (Expected: 400)")
        if response.status_code == 400:
            print("   ✅ Correctly rejected invalid time logic")
        
        # Test 4: Past date
        print("\n4️⃣ Testing past date...")
        yesterday = date.today() - timedelta(days=1)
        test_data = {
            'terrain_id': court.id,
            'date': yesterday.strftime('%Y-%m-%d'),
            'start_time': '10:00',
            'end_time': '11:00'
        }
        
        response = client.post(url, data=json.dumps(test_data), content_type='application/json')
        print(f"   Status: {response.status_code} (Expected: 400)")
        if response.status_code == 400:
            print("   ✅ Correctly rejected past date")
        
        print("\n✅ All validation tests completed!")
        return True
        
    except Exception as e:
        print(f"❌ Error testing validation: {e}")
        return False

if __name__ == '__main__':
    print("🎾 Tennis Management System - Court Reservation API Test")
    print("="*60)
    
    # Test basic functionality
    success1 = test_court_reservation_api()
    
    # Test validation scenarios
    success2 = test_validation_scenarios()
    
    if success1 and success2:
        print("\n🚀 All court reservation tests passed!")
        print("✅ The API is working correctly.")
    else:
        print("\n⚠️  Some tests failed. Check the API implementation.")
