{% extends 'html/modern_sports_navbar.html' %}
{% load static %}

{% block content %}
<style>
    /* Hero Section */
    .hero-section {
        height: 100vh;
        background: linear-gradient(135deg, rgba(30, 64, 175, 0.8) 0%, rgba(139, 92, 246, 0.8) 50%, rgba(16, 185, 129, 0.8) 100%),
                    url('{% static "images/sports-bg.jpg" %}') center/cover;
        display: flex;
        align-items: center;
        justify-content: center;
        position: relative;
        overflow: hidden;
    }

    .hero-content {
        text-align: center;
        color: white;
        z-index: 2;
        max-width: 800px;
        padding: 2rem;
    }

    .hero-title {
        font-family: 'Orbitron', monospace;
        font-size: 4rem;
        font-weight: 900;
        margin-bottom: 1rem;
        text-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
        animation: fadeInUp 1s ease-out;
    }

    .hero-subtitle {
        font-size: 1.5rem;
        font-weight: 300;
        margin-bottom: 2rem;
        opacity: 0.9;
        animation: fadeInUp 1s ease-out 0.3s both;
    }

    .hero-buttons {
        display: flex;
        gap: 1.5rem;
        justify-content: center;
        flex-wrap: wrap;
        animation: fadeInUp 1s ease-out 0.6s both;
    }

    .hero-btn {
        padding: 1rem 2.5rem;
        font-size: 1.1rem;
        font-weight: 600;
        border: none;
        border-radius: 50px;
        text-decoration: none;
        transition: all 0.3s ease;
        text-transform: uppercase;
        letter-spacing: 1px;
        position: relative;
        overflow: hidden;
    }

    .hero-btn.primary {
        background: var(--success-gradient);
        color: white;
        box-shadow: 0 8px 25px rgba(16, 185, 129, 0.4);
    }

    .hero-btn.secondary {
        background: transparent;
        color: white;
        border: 2px solid rgba(255, 255, 255, 0.5);
    }

    .hero-btn:hover {
        transform: translateY(-3px);
        box-shadow: 0 15px 35px rgba(0, 0, 0, 0.3);
    }

    /* Floating Elements */
    .floating-element {
        position: absolute;
        opacity: 0.1;
        animation: float 6s ease-in-out infinite;
    }

    .floating-element:nth-child(1) { top: 20%; left: 10%; animation-delay: 0s; }
    .floating-element:nth-child(2) { top: 60%; right: 15%; animation-delay: 2s; }
    .floating-element:nth-child(3) { bottom: 30%; left: 20%; animation-delay: 4s; }

    @keyframes float {
        0%, 100% { transform: translateY(0px) rotate(0deg); }
        50% { transform: translateY(-20px) rotate(180deg); }
    }

    @keyframes fadeInUp {
        from {
            opacity: 0;
            transform: translateY(30px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    /* Sports Cards Section */
    .sports-section {
        padding: 5rem 0;
        background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    }

    .section-title {
        font-family: 'Orbitron', monospace;
        font-size: 3rem;
        font-weight: 700;
        text-align: center;
        margin-bottom: 3rem;
        background: var(--primary-gradient);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
    }

    .sports-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: 2rem;
        max-width: 1200px;
        margin: 0 auto;
        padding: 0 2rem;
    }

    .sport-card {
        background: white;
        border-radius: 20px;
        padding: 2rem;
        text-align: center;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        transition: all 0.3s ease;
        position: relative;
        overflow: hidden;
    }

    .sport-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 5px;
        background: var(--accent-gradient);
        transform: scaleX(0);
        transition: transform 0.3s ease;
    }

    .sport-card:hover::before {
        transform: scaleX(1);
    }

    .sport-card:hover {
        transform: translateY(-10px);
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
    }

    .sport-card-icon {
        width: 80px;
        height: 80px;
        margin: 0 auto 1.5rem;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 2rem;
        color: white;
        position: relative;
    }

    .sport-card.tennis .sport-card-icon {
        background: var(--success-gradient);
    }

    .sport-card.football .sport-card-icon {
        background: var(--secondary-gradient);
    }

    .sport-card.basketball .sport-card-icon {
        background: var(--sports-purple);
    }

    .sport-card.swimming .sport-card-icon {
        background: var(--accent-gradient);
    }

    .sport-card-title {
        font-size: 1.5rem;
        font-weight: 700;
        margin-bottom: 1rem;
        color: #2d3748;
    }

    .sport-card-description {
        color: #718096;
        line-height: 1.6;
        margin-bottom: 1.5rem;
    }

    .sport-card-btn {
        background: var(--primary-gradient);
        color: white;
        border: none;
        padding: 0.75rem 2rem;
        border-radius: 25px;
        font-weight: 600;
        text-decoration: none;
        display: inline-block;
        transition: all 0.3s ease;
    }

    .sport-card-btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 20px rgba(102, 126, 234, 0.4);
        color: white;
    }

    /* Stats Section */
    .stats-section {
        padding: 4rem 0;
        background: var(--dark-gradient);
        color: white;
    }

    .stats-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 2rem;
        max-width: 1000px;
        margin: 0 auto;
        padding: 0 2rem;
    }

    .stat-item {
        text-align: center;
        padding: 2rem;
    }

    .stat-number {
        font-family: 'Orbitron', monospace;
        font-size: 3rem;
        font-weight: 900;
        color: #4facfe;
        margin-bottom: 0.5rem;
        display: block;
    }

    .stat-label {
        font-size: 1.1rem;
        opacity: 0.8;
        text-transform: uppercase;
        letter-spacing: 1px;
    }

    /* Responsive Design */
    @media (max-width: 768px) {
        .hero-title {
            font-size: 2.5rem;
        }
        
        .hero-subtitle {
            font-size: 1.2rem;
        }
        
        .hero-buttons {
            flex-direction: column;
            align-items: center;
        }
        
        .section-title {
            font-size: 2rem;
        }
        
        .sports-grid {
            grid-template-columns: 1fr;
            padding: 0 1rem;
        }
    }
</style>

<!-- Hero Section -->
<section class="hero-section">
    <!-- Floating Elements -->
    <div class="floating-element">
        <i class="bi bi-trophy-fill" style="font-size: 4rem;"></i>
    </div>
    <div class="floating-element">
        <i class="bi bi-award-fill" style="font-size: 3rem;"></i>
    </div>
    <div class="floating-element">
        <i class="bi bi-star-fill" style="font-size: 3.5rem;"></i>
    </div>

    <div class="hero-content">
        <h1 class="hero-title">ELITE SPORTS CLUB</h1>
        <p class="hero-subtitle">Where Champions Are Made - Excellence in Every Sport</p>
        <div class="hero-buttons">
            <a href="{% url 'register' %}" class="hero-btn primary">Join Now</a>
            <a href="{% url 'services' %}" class="hero-btn secondary">Explore Sports</a>
        </div>
    </div>
</section>

<!-- Sports Section -->
<section class="sports-section">
    <div class="container">
        <h2 class="section-title">Our Sports</h2>
        <div class="sports-grid">
            <div class="sport-card tennis">
                <div class="sport-card-icon">
                    <i class="bi bi-circle"></i>
                </div>
                <h3 class="sport-card-title">Tennis</h3>
                <p class="sport-card-description">
                    Professional tennis courts with expert coaching. Master your serve, perfect your backhand, and dominate the court.
                </p>
                <a href="#" class="sport-card-btn">Book Court</a>
            </div>

            <div class="sport-card football">
                <div class="sport-card-icon">
                    <i class="bi bi-hexagon-fill"></i>
                </div>
                <h3 class="sport-card-title">Football</h3>
                <p class="sport-card-description">
                    State-of-the-art football facilities with professional training programs for all skill levels.
                </p>
                <a href="#" class="sport-card-btn">Join Team</a>
            </div>

            <div class="sport-card basketball">
                <div class="sport-card-icon">
                    <i class="bi bi-circle-fill"></i>
                </div>
                <h3 class="sport-card-title">Basketball</h3>
                <p class="sport-card-description">
                    Indoor basketball courts with professional coaching and competitive leagues for all ages.
                </p>
                <a href="#" class="sport-card-btn">Play Now</a>
            </div>

            <div class="sport-card swimming">
                <div class="sport-card-icon">
                    <i class="bi bi-water"></i>
                </div>
                <h3 class="sport-card-title">Swimming</h3>
                <p class="sport-card-description">
                    Olympic-size swimming pool with certified instructors and competitive swimming programs.
                </p>
                <a href="#" class="sport-card-btn">Dive In</a>
            </div>
        </div>
    </div>
</section>

<!-- Stats Section -->
<section class="stats-section">
    <div class="container">
        <div class="stats-grid">
            <div class="stat-item">
                <span class="stat-number">500+</span>
                <span class="stat-label">Active Members</span>
            </div>
            <div class="stat-item">
                <span class="stat-number">15</span>
                <span class="stat-label">Sports Available</span>
            </div>
            <div class="stat-item">
                <span class="stat-number">25</span>
                <span class="stat-label">Expert Coaches</span>
            </div>
            <div class="stat-item">
                <span class="stat-number">10</span>
                <span class="stat-label">Years Experience</span>
            </div>
        </div>
    </div>
</section>

<script>
    // Counter animation for stats
    function animateCounters() {
        const counters = document.querySelectorAll('.stat-number');
        
        counters.forEach(counter => {
            const target = parseInt(counter.textContent);
            const increment = target / 100;
            let current = 0;
            
            const timer = setInterval(() => {
                current += increment;
                if (current >= target) {
                    counter.textContent = target + (counter.textContent.includes('+') ? '+' : '');
                    clearInterval(timer);
                } else {
                    counter.textContent = Math.floor(current) + (counter.textContent.includes('+') ? '+' : '');
                }
            }, 20);
        });
    }

    // Trigger animation when stats section is visible
    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                animateCounters();
                observer.unobserve(entry.target);
            }
        });
    });

    observer.observe(document.querySelector('.stats-section'));
</script>
{% endblock %}
