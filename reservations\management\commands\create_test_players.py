from django.core.management.base import BaseCommand
from django.contrib.auth.hashers import make_password
from core.models import User
from reservations.models import PlayerProfile
import random
from datetime import date, timedelta

class Command(BaseCommand):
    help = 'Create test players with profiles'

    def handle(self, *args, **options):
        # Sample player data
        players_data = [
            {
                'username': 'alex_johnson',
                'first_name': '<PERSON>',
                'last_name': '<PERSON>',
                'email': '<EMAIL>',
                'skill_level': 'advanced',
                'preferred_sport': 'Tennis',
                'total_matches': 45,
                'matches_won': 32,
                'total_training_hours': 120,
                'bio': 'Passionate tennis player with 5 years of experience. Love competing and helping others improve their game.'
            },
            {
                'username': 'sarah_williams',
                'first_name': '<PERSON>',
                'last_name': '<PERSON>',
                'email': '<EMAIL>',
                'skill_level': 'intermediate',
                'preferred_sport': 'Tennis',
                'total_matches': 28,
                'matches_won': 18,
                'total_training_hours': 85,
                'bio': 'Tennis enthusiast working hard to improve my skills. Always excited for a good match!'
            },
            {
                'username': 'mike_chen',
                'first_name': '<PERSON>',
                'last_name': '<PERSON>',
                'email': '<EMAIL>',
                'skill_level': 'professional',
                'preferred_sport': 'Tennis',
                'total_matches': 78,
                'matches_won': 65,
                'total_training_hours': 200,
                'bio': 'Professional tennis player and coach. Dedicated to excellence and helping others reach their potential.'
            },
            {
                'username': 'emma_davis',
                'first_name': 'Emma',
                'last_name': 'Davis',
                'email': '<EMAIL>',
                'skill_level': 'beginner',
                'preferred_sport': 'Tennis',
                'total_matches': 12,
                'matches_won': 5,
                'total_training_hours': 35,
                'bio': 'New to tennis but loving every minute of it! Excited to learn and improve with this amazing community.'
            },
            {
                'username': 'david_rodriguez',
                'first_name': 'David',
                'last_name': 'Rodriguez',
                'email': '<EMAIL>',
                'skill_level': 'advanced',
                'preferred_sport': 'Tennis',
                'total_matches': 38,
                'matches_won': 26,
                'total_training_hours': 95,
                'bio': 'Competitive player who enjoys the strategic aspects of tennis. Always looking for challenging matches.'
            },
            {
                'username': 'lisa_thompson',
                'first_name': 'Lisa',
                'last_name': 'Thompson',
                'email': '<EMAIL>',
                'skill_level': 'intermediate',
                'preferred_sport': 'Tennis',
                'total_matches': 22,
                'matches_won': 14,
                'total_training_hours': 60,
                'bio': 'Love the social aspect of tennis and meeting new people. Always up for a friendly game!'
            }
        ]

        created_count = 0
        
        for player_data in players_data:
            # Check if user already exists
            if User.objects.filter(username=player_data['username']).exists():
                self.stdout.write(
                    self.style.WARNING(f'User {player_data["username"]} already exists, skipping...')
                )
                continue
            
            # Create user
            user = User.objects.create(
                username=player_data['username'],
                first_name=player_data['first_name'],
                last_name=player_data['last_name'],
                email=player_data['email'],
                password=make_password('password123'),  # Default password
                role='joueur'
            )
            
            # Create player profile
            PlayerProfile.objects.create(
                user=user,
                bio=player_data['bio'],
                skill_level=player_data['skill_level'],
                preferred_sport=player_data['preferred_sport'],
                total_matches=player_data['total_matches'],
                matches_won=player_data['matches_won'],
                total_training_hours=player_data['total_training_hours'],
                phone=f'******-{random.randint(100, 999)}-{random.randint(1000, 9999)}',
                date_of_birth=date.today() - timedelta(days=random.randint(18*365, 35*365))
            )
            
            created_count += 1
            self.stdout.write(
                self.style.SUCCESS(f'Created player: {player_data["first_name"]} {player_data["last_name"]}')
            )
        
        self.stdout.write(
            self.style.SUCCESS(f'Successfully created {created_count} test players!')
        )
        self.stdout.write(
            self.style.WARNING('Default password for all test players: password123')
        )
