<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <title>Tennis Club Reservation</title>
    <link
      href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css"
      rel="stylesheet"
    />
    <style>
      body {
        font-family: "Poppins", sans-serif;
        background: url("https://images.unsplash.com/photo-1505842465776-3e3e59e39418?auto=format&fit=crop&w=1350&q=80")
          no-repeat center center fixed;
        background-size: cover;
        color: #fff;
        min-height: 100vh;
        display: flex;
        justify-content: center;
        align-items: center;
        padding: 40px 10px;
      }

      .reservation-container {
        background: rgba(0, 0, 0, 0.7);
        padding: 40px;
        border-radius: 12px;
        max-width: 480px;
        width: 100%;
        box-shadow: 0 0 20px rgba(133, 239, 80, 0.7);
      }

      h2 {
        text-align: center;
        margin-bottom: 30px;
        color: #85ef50;
        text-transform: uppercase;
        letter-spacing: 2px;
      }

      label {
        color: #c7ea46;
        font-weight: 600;
      }

      .btn-reserve {
        background:hsl(100, 83.20%, 62.50%);
        color: black;
        font-weight: 700;
        width: 100%;
        margin-top: 20px;
        border: none;
        padding: 12px;
        border-radius: 6px;
        transition: background-color 0.3s ease;
      }

      .btn-reserve:hover {
        background: #a5d025;
        color: white;
      }

      .error-message {
        color: #ff4d4d;
        font-size: 0.9em;
        margin-top: 5px;
        display: none;
      }
    </style>
  </head>

  <body>
    <div class="reservation-container">
      <h2>Tennis Court Reservation</h2>
      <form id="reservationForm">
        <div class="mb-3">
          <label for="name" class="form-label">Full Name:</label>
          <input
            type="text"
            id="name"
            name="name"
            class="form-control"
            required
          />
        </div>
        <div class="mb-3">
          <label for="email" class="form-label">Email Address:</label>
          <input
            type="email"
            id="email"
            name="email"
            class="form-control"
            required
          />
        </div>
        <div class="mb-3">
          <label for="terrain" class="form-label">Select Terrain:</label>
          <select id="terrain" name="terrain_id" class="form-control" required>
            <option value="">Select a terrain</option>
          </select>
          <div id="terrain-error" class="error-message">
            Please select a valid terrain.
          </div>
        </div>
        <div class="mb-3">
          <label for="date" class="form-label">Reservation Date:</label>
          <input
            type="date"
            id="date"
            name="date"
            class="form-control"
            required
          />
          <div id="date-error" class="error-message">
            Please select a valid date.
          </div>
        </div>
        <div class="mb-3">
          <label for="start_time" class="form-label">Start Time:</label>
          <input
            type="time"
            id="start_time"
            name="start_time"
            class="form-control"
            required
          />
          <div id="start-time-error" class="error-message">
            Please select a valid start time.
          </div>
        </div>
        <div class="mb-3">
          <label for="end_time" class="form-label">End Time:</label>
          <input
            type="time"
            id="end_time"
            name="end_time"
            class="form-control"
            required
          />
          <div id="end-time-error" class="error-message">
            Please select a valid end time.
          </div>
        </div>
        <button type="submit" class="btn btn-reserve">Reserve Now</button>
      </form>
    </div>

    <script>
      document.addEventListener("DOMContentLoaded", function () {
        // Fetch available terrains
        fetch("/res/terrains/", {
          headers: {
            Authorization: "Bearer " + localStorage.getItem("access_token"), // Include JWT token if required
          },
        })
          .then((response) => {
            if (!response.ok) {
              if (response.status === 401) {
                throw new Error("Please log in to continue.");
              }
              throw new Error(
                "Failed to fetch terrains: " + response.statusText
              );
            }
            return response.json();
          })
          .then((data) => {
            const terrainSelect = document.getElementById("terrain");
            data.forEach((terrain) => {
              const option = document.createElement("option");
              option.value = terrain.id;
              option.textContent = `${terrain.name} - ${terrain.location} ($${terrain.price_per_hour}/hour)`;
              terrainSelect.appendChild(option);
            });
          })
          .catch((error) => {
            console.error("Error fetching terrains:", error);
            alert("Error loading terrains: " + error.message);
          });

        // Form submission
        document
          .getElementById("reservationForm")
          .addEventListener("submit", function (e) {
            e.preventDefault();

            // Clear previous error messages
            document
              .querySelectorAll(".error-message")
              .forEach((el) => (el.style.display = "none"));

            const formData = {
              terrain_id: document.getElementById("terrain").value,
              date: document.getElementById("date").value,
              start_time: document.getElementById("start_time").value,
              end_time: document.getElementById("end_time").value,
            };

            // Client-side validation
            let hasError = false;
            if (!formData.terrain_id) {
              document.getElementById("terrain-error").style.display = "block";
              hasError = true;
            }
            if (!formData.date) {
              document.getElementById("date-error").style.display = "block";
              hasError = true;
            }
            if (!formData.start_time) {
              document.getElementById("start-time-error").style.display =
                "block";
              hasError = true;
            }
            if (!formData.end_time) {
              document.getElementById("end-time-error").style.display = "block";
              hasError = true;
            }
            if (formData.start_time >= formData.end_time) {
              document.getElementById("end-time-error").textContent =
                "End time must be after start time.";
              document.getElementById("end-time-error").style.display = "block";
              hasError = true;
            }

            if (hasError) return;

            fetch("/reservations/", {
              method: "POST",
              headers: {
                "Content-Type": "application/json",
                // No Authorization header for anonymous reservations
              },
              body: JSON.stringify(formData),
            })
              .then((response) => {
                if (!response.ok) {
                  if (response.status === 401) {
                    throw new Error("Authentication error occurred.");
                  }
                  return response.json().then((data) => {
                    throw new Error(data.error || "Failed to make reservation");
                  });
                }
                return response.json();
              })
              .then((data) => {
                alert(
                  "Thank you for your reservation, " +
                    data.reservation.user +
                    "!\n" +
                    "Terrain: " +
                    data.reservation.terrain +
                    "\n" +
                    "Date: " +
                    data.reservation.date +
                    "\n" +
                    "Time: " +
                    data.reservation.start_time +
                    " - " +
                    data.reservation.end_time +
                    "\n" +
                    "Total Price: $" +
                    data.reservation.total_price
                );
                document.getElementById("reservationForm").reset();
              })
              .catch((error) => {
                console.error("Error:", error);
                alert(
                  "An error occurred while making the reservation: " +
                    error.message
                );
              });
          });
      });
    </script>
  </body>
</html>
