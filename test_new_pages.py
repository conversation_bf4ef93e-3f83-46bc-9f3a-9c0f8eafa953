#!/usr/bin/env python
"""
Test des nouvelles pages: Services, Blog, Contact, About
"""
import requests

def test_all_new_pages():
    print("🎾 Test Nouvelles Pages - Services, Blog, Contact, About")
    print("="*60)
    
    pages = [
        {
            'name': 'Home (Updated)',
            'url': 'http://127.0.0.1:8000/',
            'expected_content': ['Tennis Club Elite', 'Our Services', 'Join Our Club'],
            'description': 'Page d\'accueil mise à jour sans réservation'
        },
        {
            'name': 'Services',
            'url': 'http://127.0.0.1:8000/services/',
            'expected_content': ['Our Services', 'Court Rental', 'Professional Coaching', 'Pricing Plans'],
            'description': 'Page des services avec tarifs et fonctionnalités'
        },
        {
            'name': 'Blog',
            'url': 'http://127.0.0.1:8000/blog/',
            'expected_content': ['Tennis Blog', 'Mastering Your Tennis Serve', 'Recent Posts'],
            'description': 'Blog avec articles de tennis'
        },
        {
            'name': 'Contact',
            'url': 'http://127.0.0.1:8000/contact/',
            'expected_content': ['Contact Us', 'Get In Touch', 'Send Us a Message', 'Operating Hours'],
            'description': 'Page de contact avec formulaire et informations'
        },
        {
            'name': 'About',
            'url': 'http://127.0.0.1:8000/about/',
            'expected_content': ['About Tennis Club Elite', 'Our Story', 'Our Values', 'Meet Our Team'],
            'description': 'Page à propos avec histoire et équipe'
        }
    ]
    
    all_passed = True
    
    for page in pages:
        print(f"\n🔍 Test {page['name']}:")
        print(f"   📄 {page['description']}")
        
        try:
            response = requests.get(page['url'])
            
            if response.status_code == 200:
                html_content = response.text
                print(f"   ✅ Page accessible (200)")
                
                # Vérifier le contenu attendu
                content_checks = []
                for expected in page['expected_content']:
                    found = expected in html_content
                    content_checks.append((expected, found))
                    if found:
                        print(f"   ✅ Contenu trouvé: '{expected}'")
                    else:
                        print(f"   ❌ Contenu manquant: '{expected}'")
                        all_passed = False
                
                # Vérifications spécifiques par page
                if page['name'] == 'Home (Updated)':
                    # Vérifier que la réservation a été supprimée
                    if 'Quick Court Reservation' not in html_content:
                        print(f"   ✅ Section de réservation supprimée")
                    else:
                        print(f"   ❌ Section de réservation encore présente")
                        all_passed = False
                    
                    # Vérifier les nouveaux boutons
                    if 'Our Services' in html_content and '/services/' in html_content:
                        print(f"   ✅ Bouton 'Our Services' présent")
                    else:
                        print(f"   ❌ Bouton 'Our Services' manquant")
                        all_passed = False
                
                elif page['name'] == 'Services':
                    # Vérifier les sections de services
                    service_sections = ['Court Rental', 'Professional Coaching', 'Tournaments & Events', 'Equipment & Pro Shop']
                    for section in service_sections:
                        if section in html_content:
                            print(f"   ✅ Section service: '{section}'")
                        else:
                            print(f"   ❌ Section service manquante: '{section}'")
                    
                    # Vérifier les plans de prix
                    pricing_plans = ['Basic', 'Premium', 'Elite']
                    for plan in pricing_plans:
                        if plan in html_content:
                            print(f"   ✅ Plan de prix: '{plan}'")
                        else:
                            print(f"   ❌ Plan de prix manquant: '{plan}'")
                
                elif page['name'] == 'Blog':
                    # Vérifier les articles de blog
                    blog_elements = ['Featured', 'blog-card', 'Recent Posts', 'Categories']
                    for element in blog_elements:
                        if element in html_content:
                            print(f"   ✅ Élément blog: '{element}'")
                        else:
                            print(f"   ❌ Élément blog manquant: '{element}'")
                
                elif page['name'] == 'Contact':
                    # Vérifier les éléments de contact
                    contact_elements = ['Phone', 'Email', 'Address', 'Send Us a Message', 'Operating Hours']
                    for element in contact_elements:
                        if element in html_content:
                            print(f"   ✅ Élément contact: '{element}'")
                        else:
                            print(f"   ❌ Élément contact manquant: '{element}'")
                    
                    # Vérifier le formulaire
                    form_elements = ['First Name', 'Last Name', 'Subject', 'Message']
                    for element in form_elements:
                        if element in html_content:
                            print(f"   ✅ Champ formulaire: '{element}'")
                        else:
                            print(f"   ❌ Champ formulaire manquant: '{element}'")
                
                elif page['name'] == 'About':
                    # Vérifier les sections about
                    about_sections = ['Our Story', 'Our Journey', 'Our Values', 'Meet Our Team']
                    for section in about_sections:
                        if section in html_content:
                            print(f"   ✅ Section about: '{section}'")
                        else:
                            print(f"   ❌ Section about manquante: '{section}'")
                    
                    # Vérifier les statistiques
                    if 'Years of Excellence' in html_content and 'Professional Courts' in html_content:
                        print(f"   ✅ Statistiques présentes")
                    else:
                        print(f"   ❌ Statistiques manquantes")
                
                # Vérifier les éléments communs
                common_elements = ['bootstrap', 'responsive', 'btn-', 'container']
                for element in common_elements:
                    if element in html_content:
                        print(f"   ✅ Élément commun: '{element}'")
                    else:
                        print(f"   ⚠️  Élément commun manquant: '{element}'")
                
            else:
                print(f"   ❌ Page inaccessible: {response.status_code}")
                all_passed = False
                
        except Exception as e:
            print(f"   ❌ Erreur lors du test: {e}")
            all_passed = False
    
    return all_passed

def test_navbar_links():
    print("\n🔗 Test Liens Navigation:")
    print("="*30)
    
    try:
        # Tester la page d'accueil pour vérifier les liens de navigation
        response = requests.get('http://127.0.0.1:8000/')
        
        if response.status_code == 200:
            html_content = response.text
            
            # Vérifier les liens dans la navbar
            expected_links = [
                ('/services/', 'Services'),
                ('/about/', 'About'),
                ('/blog/', 'Blog'),
                ('/contact/', 'Contact Us'),
                ('/reservation', 'Reservation'),
                ('/auth/login/', 'Sign In'),
                ('/auth/register/', 'Sign Up')
            ]
            
            for url, name in expected_links:
                if url in html_content:
                    print(f"   ✅ Lien trouvé: {name} ({url})")
                else:
                    print(f"   ❌ Lien manquant: {name} ({url})")
            
            # Vérifier que les anciens liens # ont été remplacés
            if 'href="#"' in html_content:
                # Compter les occurrences restantes (certaines sont légitimes comme Subscribe)
                hash_count = html_content.count('href="#"')
                print(f"   ℹ️  {hash_count} liens '#' restants (normal pour Subscribe, etc.)")
            else:
                print(f"   ✅ Tous les liens '#' ont été remplacés")
                
        else:
            print(f"   ❌ Impossible de tester les liens: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"   ❌ Erreur test liens: {e}")
        return False
    
    return True

def show_manual_test_instructions():
    print("\n🎯 INSTRUCTIONS POUR TEST MANUEL:")
    print("="*40)
    print("1. 🌐 Tester chaque page individuellement:")
    print("   - Home: http://127.0.0.1:8000/")
    print("   - Services: http://127.0.0.1:8000/services/")
    print("   - About: http://127.0.0.1:8000/about/")
    print("   - Blog: http://127.0.0.1:8000/blog/")
    print("   - Contact: http://127.0.0.1:8000/contact/")
    print("2. 🔍 Vérifier la navigation:")
    print("   - Cliquer sur chaque lien dans la navbar")
    print("   - Tester le menu mobile (hamburger)")
    print("   - Vérifier que tous les liens fonctionnent")
    print("3. 📱 Tester la responsivité:")
    print("   - Redimensionner la fenêtre")
    print("   - Tester sur mobile/tablette")
    print("   - Vérifier l'affichage sur différentes tailles")
    print("4. 🎨 Vérifier le design:")
    print("   - Cohérence visuelle entre les pages")
    print("   - Couleurs tennis (vert lime)")
    print("   - Animations et effets hover")
    print("   - Images et icônes")
    
    print("\n🎨 FONCTIONNALITÉS À TESTER:")
    print("="*30)
    print("✓ Page d'Accueil:")
    print("  - Section hero mise à jour")
    print("  - Bouton 'Our Services' au lieu de réservation")
    print("  - Section features conservée")
    print("✓ Page Services:")
    print("  - 6 services détaillés")
    print("  - 3 plans de prix")
    print("  - Design professionnel")
    print("✓ Page Blog:")
    print("  - Article featured")
    print("  - Grille d'articles")
    print("  - Sidebar avec recent posts")
    print("  - Catégories et newsletter")
    print("✓ Page Contact:")
    print("  - Informations de contact")
    print("  - Formulaire de contact")
    print("  - Carte et horaires")
    print("  - FAQ section")
    print("✓ Page About:")
    print("  - Histoire du club")
    print("  - Timeline des événements")
    print("  - Valeurs et équipe")
    print("  - Statistiques impressionnantes")

if __name__ == '__main__':
    print("🎾 TENNIS MANAGEMENT SYSTEM")
    print("🔧 Test Nouvelles Pages du Site Web")
    print("="*50)
    
    pages_success = test_all_new_pages()
    navbar_success = test_navbar_links()
    
    if pages_success and navbar_success:
        print("\n🚀 TOUTES LES NOUVELLES PAGES FONCTIONNENT!")
        print("="*45)
        print("✅ Page d'accueil mise à jour (sans réservation)")
        print("✅ Page Services complète avec tarifs")
        print("✅ Page Blog avec articles et sidebar")
        print("✅ Page Contact avec formulaire et infos")
        print("✅ Page About avec histoire et équipe")
        print("✅ Navigation mise à jour dans navbar")
        print("✅ Tous les liens fonctionnels")
        
        show_manual_test_instructions()
        
        print("\n🎉 FÉLICITATIONS!")
        print("="*20)
        print("Le site web dispose maintenant de:")
        print("- 5 pages complètes et professionnelles")
        print("- Navigation fonctionnelle")
        print("- Design cohérent et moderne")
        print("- Contenu riche et informatif")
        print("- Responsive design")
        
    else:
        print("\n⚠️  Des problèmes ont été détectés.")
        print("Vérifiez les erreurs ci-dessus et corrigez-les.")
