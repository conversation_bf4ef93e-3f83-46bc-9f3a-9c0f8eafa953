{% load static %}
<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Coach Dashboard - Elite Sports Club</title>

    <!-- Bootstrap Icons -->
    <link
      href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.0/font/bootstrap-icons.css"
      rel="stylesheet"
    />
    <!-- Bootstrap CSS -->
    <link
      href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css"
      rel="stylesheet"
    />
    <!-- Google Fonts -->
    <link
      href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Poppins:wght@300;400;500;600;700&display=swap"
      rel="stylesheet"
    />

    <style>
      :root {
        /* Soft, eye-friendly colors */
        --primary-color: #6366f1;
        --primary-light: #a5b4fc;
        --secondary-color: #10b981;
        --secondary-light: #6ee7b7;
        --accent-color: #f59e0b;
        --accent-light: #fbbf24;
        --danger-color: #ef4444;
        --danger-light: #fca5a5;

        /* Neutral colors */
        --gray-50: #f9fafb;
        --gray-100: #f3f4f6;
        --gray-200: #e5e7eb;
        --gray-300: #d1d5db;
        --gray-400: #9ca3af;
        --gray-500: #6b7280;
        --gray-600: #4b5563;
        --gray-700: #374151;
        --gray-800: #1f2937;
        --gray-900: #111827;

        /* Background gradients */
        --bg-gradient: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
        --card-gradient: linear-gradient(145deg, #ffffff 0%, #f8fafc 100%);
        --primary-gradient: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
        --success-gradient: linear-gradient(135deg, #10b981 0%, #059669 100%);
        --warning-gradient: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
        --danger-gradient: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);

        /* Shadows */
        --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
        --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
        --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
        --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
      }

      * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
      }

      body {
        font-family: "Inter", sans-serif;
        background: var(--bg-gradient);
        color: var(--gray-700);
        line-height: 1.6;
      }

      /* Layout */
      .dashboard-layout {
        display: flex;
        min-height: 100vh;
      }

      /* Sidebar */
      .sidebar {
        width: 280px;
        background: white;
        border-right: 1px solid var(--gray-200);
        box-shadow: var(--shadow-lg);
        position: fixed;
        height: 100vh;
        overflow-y: auto;
        z-index: 1000;
      }

      .sidebar-header {
        padding: 2rem;
        border-bottom: 1px solid var(--gray-100);
      }

      .logo {
        display: flex;
        align-items: center;
        gap: 0.75rem;
        text-decoration: none;
        color: var(--gray-800);
      }

      .logo-icon {
        width: 48px;
        height: 48px;
        background: var(--primary-gradient);
        border-radius: 12px;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 1.25rem;
        box-shadow: var(--shadow-md);
      }

      .logo-text {
        font-family: "Poppins", sans-serif;
        font-weight: 700;
        font-size: 1.125rem;
        color: var(--gray-800);
      }

      .logo-subtitle {
        font-size: 0.75rem;
        color: var(--gray-500);
        font-weight: 500;
      }

      /* Navigation */
      .nav-menu {
        padding: 1rem;
        list-style: none;
      }

      .nav-item {
        margin-bottom: 0.25rem;
      }

      .nav-link {
        display: flex;
        align-items: center;
        gap: 0.75rem;
        padding: 0.75rem 1rem;
        color: var(--gray-600);
        text-decoration: none;
        border-radius: 8px;
        transition: all 0.2s ease;
        font-weight: 500;
        font-size: 0.875rem;
      }

      .nav-link:hover {
        background: var(--gray-50);
        color: var(--primary-color);
        transform: translateX(2px);
      }

      .nav-link.active {
        background: var(--primary-color);
        color: white;
        box-shadow: var(--shadow-md);
      }

      .nav-icon {
        font-size: 1rem;
        width: 20px;
        text-align: center;
      }

      /* Main Content */
      .main-content {
        flex: 1;
        margin-left: 280px;
        padding: 2rem;
        background: var(--bg-gradient);
      }

      /* Header */
      .page-header {
        background: white;
        border-radius: 16px;
        padding: 2rem;
        margin-bottom: 2rem;
        box-shadow: var(--shadow-sm);
        border: 1px solid var(--gray-100);
      }

      .header-content {
        display: flex;
        justify-content: space-between;
        align-items: center;
        flex-wrap: wrap;
        gap: 1rem;
      }

      .page-title {
        font-family: "Poppins", sans-serif;
        font-size: 2rem;
        font-weight: 700;
        color: var(--gray-800);
        margin: 0;
        display: flex;
        align-items: center;
        gap: 0.75rem;
      }

      .title-icon {
        width: 48px;
        height: 48px;
        background: var(--success-gradient);
        border-radius: 12px;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 1.25rem;
      }

      .page-subtitle {
        color: var(--gray-500);
        font-size: 1rem;
        margin-top: 0.5rem;
        font-weight: 400;
      }

      .user-profile {
        display: flex;
        align-items: center;
        gap: 0.75rem;
        background: var(--gray-50);
        padding: 0.75rem 1rem;
        border-radius: 12px;
        border: 1px solid var(--gray-200);
      }

      .profile-avatar {
        width: 40px;
        height: 40px;
        background: var(--primary-gradient);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 1rem;
      }

      .profile-info {
        color: var(--gray-700);
      }

      .profile-name {
        font-weight: 600;
        font-size: 0.875rem;
      }

      .profile-role {
        font-size: 0.75rem;
        color: var(--gray-500);
      }

      /* Stats Grid */
      .stats-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
        gap: 1.5rem;
        margin-bottom: 2rem;
      }

      .stat-card {
        background: white;
        border-radius: 16px;
        padding: 1.5rem;
        border: 1px solid var(--gray-100);
        box-shadow: var(--shadow-sm);
        transition: all 0.3s ease;
        position: relative;
        overflow: hidden;
      }

      .stat-card::before {
        content: "";
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 4px;
        background: var(--card-accent, var(--primary-color));
      }

      .stat-card:hover {
        transform: translateY(-4px);
        box-shadow: var(--shadow-lg);
      }

      .stat-card.primary {
        --card-accent: var(--primary-color);
      }
      .stat-card.success {
        --card-accent: var(--secondary-color);
      }
      .stat-card.warning {
        --card-accent: var(--accent-color);
      }
      .stat-card.danger {
        --card-accent: var(--danger-color);
      }

      .stat-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 1rem;
      }

      .stat-icon {
        width: 48px;
        height: 48px;
        border-radius: 12px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1.25rem;
        color: white;
        background: var(--card-accent, var(--primary-color));
      }

      .stat-value {
        font-size: 2rem;
        font-weight: 700;
        color: var(--gray-800);
        margin-bottom: 0.25rem;
      }

      .stat-label {
        color: var(--gray-600);
        font-size: 0.875rem;
        font-weight: 500;
      }

      .stat-change {
        font-size: 0.75rem;
        margin-top: 0.5rem;
        font-weight: 500;
      }

      .stat-change.positive {
        color: var(--secondary-color);
      }

      .stat-change.negative {
        color: var(--danger-color);
      }

      /* Action Cards */
      .actions-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: 1.5rem;
      }

      .action-card {
        background: white;
        border-radius: 16px;
        padding: 1.5rem;
        border: 1px solid var(--gray-100);
        box-shadow: var(--shadow-sm);
        transition: all 0.3s ease;
      }

      .action-card:hover {
        transform: translateY(-2px);
        box-shadow: var(--shadow-md);
      }

      .action-header {
        display: flex;
        align-items: center;
        gap: 1rem;
        margin-bottom: 1rem;
      }

      .action-icon {
        width: 48px;
        height: 48px;
        border-radius: 12px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1.25rem;
        color: white;
        background: var(--action-color, var(--primary-gradient));
      }

      .action-title {
        font-size: 1.125rem;
        font-weight: 600;
        color: var(--gray-800);
      }

      .action-description {
        color: var(--gray-600);
        margin-bottom: 1.5rem;
        line-height: 1.5;
        font-size: 0.875rem;
      }

      .action-btn {
        background: var(--action-color, var(--primary-gradient));
        color: white;
        border: none;
        padding: 0.75rem 1.5rem;
        border-radius: 8px;
        font-weight: 600;
        font-size: 0.875rem;
        transition: all 0.2s ease;
        cursor: pointer;
        width: 100%;
      }

      .action-btn:hover {
        transform: translateY(-1px);
        box-shadow: var(--shadow-md);
      }

      /* Responsive */
      @media (max-width: 768px) {
        .sidebar {
          transform: translateX(-100%);
          transition: transform 0.3s ease;
        }

        .sidebar.show {
          transform: translateX(0);
        }

        .main-content {
          margin-left: 0;
          padding: 1rem;
        }

        .page-title {
          font-size: 1.5rem;
        }

        .stats-grid {
          grid-template-columns: 1fr;
        }
      }

      /* Smooth animations */
      @keyframes fadeInUp {
        from {
          opacity: 0;
          transform: translateY(20px);
        }
        to {
          opacity: 1;
          transform: translateY(0);
        }
      }

      .animate-in {
        animation: fadeInUp 0.6s ease-out;
      }
    </style>
  </head>
  <body>
    <div class="dashboard-layout">
      <!-- Sidebar -->
      <nav class="sidebar">
        <div class="sidebar-header">
          <a href="{% url 'home' %}" class="logo">
            <div class="logo-icon">
              <i class="bi bi-shield-check"></i>
            </div>
            <div>
              <div class="logo-text">Elite Admin</div>
              <div class="logo-subtitle">Control Center</div>
            </div>
          </a>
        </div>

        <ul class="nav-menu">
          <li class="nav-item">
            <a href="#dashboard" class="nav-link active">
              <i class="bi bi-grid nav-icon"></i>
              Dashboard
            </a>
          </li>
          <li class="nav-item">
            <a href="#users" class="nav-link">
              <i class="bi bi-people nav-icon"></i>
              Manage Users
            </a>
          </li>
          <li class="nav-item">
            <a href="#coaches" class="nav-link">
              <i class="bi bi-person-badge nav-icon"></i>
              Manage Coaches
            </a>
          </li>
          <li class="nav-item">
            <a href="#courts" class="nav-link">
              <i class="bi bi-building nav-icon"></i>
              Court Management
            </a>
          </li>
          <li class="nav-item">
            <a href="#reservations" class="nav-link">
              <i class="bi bi-calendar-event nav-icon"></i>
              Reservations
            </a>
          </li>
          <li class="nav-item">
            <a href="#analytics" class="nav-link">
              <i class="bi bi-graph-up nav-icon"></i>
              Analytics
            </a>
          </li>
          <li class="nav-item">
            <a href="#settings" class="nav-link">
              <i class="bi bi-gear nav-icon"></i>
              Settings
            </a>
          </li>
          <li class="nav-item" style="margin-top: 2rem">
            <a href="{% url 'logout' %}" class="nav-link">
              <i class="bi bi-box-arrow-right nav-icon"></i>
              Logout
            </a>
          </li>
        </ul>
      </nav>

      <!-- Main Content -->
      <main class="main-content">
        <!-- Page Header -->
        <div class="page-header">
          <div class="header-content">
            <div>
              <h1 class="page-title">
                <div class="title-icon">
                  <i class="bi bi-shield-check"></i>
                </div>
                Admin Dashboard
              </h1>
              <p class="page-subtitle">
                Welcome back! Here's what's happening at your sports club today.
              </p>
            </div>
            <div class="user-profile">
              <div class="profile-avatar">
                <i class="bi bi-person"></i>
              </div>
              <div class="profile-info">
                <div class="profile-name">Admin User</div>
                <div class="profile-role">System Administrator</div>
              </div>
            </div>
          </div>
        </div>

        <!-- Stats Grid -->
        <div class="stats-grid">
          <div class="stat-card primary animate-in">
            <div class="stat-header">
              <div class="stat-icon">
                <i class="bi bi-people"></i>
              </div>
            </div>
            <div class="stat-value">1,247</div>
            <div class="stat-label">Total Members</div>
            <div class="stat-change positive">
              <i class="bi bi-arrow-up"></i> +12% this month
            </div>
          </div>

          <div
            class="stat-card success animate-in"
            style="animation-delay: 0.1s"
          >
            <div class="stat-header">
              <div class="stat-icon">
                <i class="bi bi-calendar-check"></i>
              </div>
            </div>
            <div class="stat-value">89</div>
            <div class="stat-label">Active Reservations</div>
            <div class="stat-change positive">
              <i class="bi bi-arrow-up"></i> +8% today
            </div>
          </div>

          <div
            class="stat-card warning animate-in"
            style="animation-delay: 0.2s"
          >
            <div class="stat-header">
              <div class="stat-icon">
                <i class="bi bi-person-badge"></i>
              </div>
            </div>
            <div class="stat-value">24</div>
            <div class="stat-label">Active Coaches</div>
            <div class="stat-change positive">
              <i class="bi bi-arrow-up"></i> +2 new coaches
            </div>
          </div>

          <div
            class="stat-card danger animate-in"
            style="animation-delay: 0.3s"
          >
            <div class="stat-header">
              <div class="stat-icon">
                <i class="bi bi-currency-dollar"></i>
              </div>
            </div>
            <div class="stat-value">$45,230</div>
            <div class="stat-label">Monthly Revenue</div>
            <div class="stat-change positive">
              <i class="bi bi-arrow-up"></i> +15% vs last month
            </div>
          </div>
        </div>

        <!-- Action Cards -->
        <div class="actions-grid">
          <div
            class="action-card animate-in"
            style="
              animation-delay: 0.4s;
              --action-color: var(--primary-gradient);
            "
          >
            <div class="action-header">
              <div class="action-icon">
                <i class="bi bi-person-plus"></i>
              </div>
              <div class="action-title">Add New User</div>
            </div>
            <div class="action-description">
              Register new members and assign roles to expand your sports club
              community.
            </div>
            <button class="action-btn">Add User</button>
          </div>

          <div
            class="action-card animate-in"
            style="
              animation-delay: 0.5s;
              --action-color: var(--success-gradient);
            "
          >
            <div class="action-header">
              <div class="action-icon">
                <i class="bi bi-person-badge"></i>
              </div>
              <div class="action-title">Manage Coaches</div>
            </div>
            <div class="action-description">
              Add new coaches, set schedules, and manage coaching assignments
              for all sports.
            </div>
            <button class="action-btn">Manage Coaches</button>
          </div>

          <div
            class="action-card animate-in"
            style="
              animation-delay: 0.6s;
              --action-color: var(--warning-gradient);
            "
          >
            <div class="action-header">
              <div class="action-icon">
                <i class="bi bi-building"></i>
              </div>
              <div class="action-title">Court Management</div>
            </div>
            <div class="action-description">
              Monitor court availability, maintenance schedules, and booking
              management.
            </div>
            <button class="action-btn">Manage Courts</button>
          </div>

          <div
            class="action-card animate-in"
            style="
              animation-delay: 0.7s;
              --action-color: var(--danger-gradient);
            "
          >
            <div class="action-header">
              <div class="action-icon">
                <i class="bi bi-graph-up"></i>
              </div>
              <div class="action-title">View Analytics</div>
            </div>
            <div class="action-description">
              Access detailed reports, member statistics, and revenue analytics.
            </div>
            <button class="action-btn">View Reports</button>
          </div>
        </div>
      </main>
    </div>

    <!-- JavaScript -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
      // Navigation interactions
      document.querySelectorAll(".nav-link").forEach((link) => {
        link.addEventListener("click", function (e) {
          e.preventDefault();
          document
            .querySelectorAll(".nav-link")
            .forEach((l) => l.classList.remove("active"));
          this.classList.add("active");
        });
      });

      // Mobile sidebar toggle
      function toggleSidebar() {
        document.querySelector(".sidebar").classList.toggle("show");
      }

      // Add click effects to action buttons
      document.querySelectorAll(".action-btn").forEach((btn) => {
        btn.addEventListener("click", function () {
          this.style.transform = "scale(0.98)";
          setTimeout(() => {
            this.style.transform = "";
          }, 150);
        });
      });
    </script>
  </body>
</html>
