#!/usr/bin/env python
"""
Script to test subscription creation directly
"""
import os
import sys
import django
from datetime import timedelta

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'tennis_manager.settings')
django.setup()

from django.contrib.auth import get_user_model
from reservations.models import Subscription, Payment, Notification
from django.utils import timezone

User = get_user_model()

def test_subscription_creation():
    print("🧪 Testing Direct Subscription Creation...")
    
    try:
        # Get test user
        user = User.objects.filter(username='player_bob').first()
        if not user:
            print("❌ Test user 'player_bob' not found")
            return False
        
        print(f"✅ Found test user: {user.username}")
        
        # Check if user already has a subscription
        existing_sub = Subscription.objects.filter(user=user).first()
        if existing_sub:
            print(f"ℹ️  User already has subscription: {existing_sub}")
            existing_sub.delete()
            print("✅ Deleted existing subscription for testing")
        
        # Test subscription creation
        print("\n1️⃣ Creating new subscription...")
        
        plan_type = 'premium'
        duration_months = 1
        monthly_price = 49.99
        
        # Calculate end date
        end_date = timezone.now() + timedelta(days=30 * duration_months)
        
        # Create subscription
        subscription = Subscription.objects.create(
            user=user,
            plan_type=plan_type,
            end_date=end_date,
            monthly_price=monthly_price,
            status='active'
        )
        
        print(f"✅ Created subscription: {subscription}")
        print(f"   Plan: {subscription.plan_type}")
        print(f"   Price: ${subscription.monthly_price}")
        print(f"   Status: {subscription.status}")
        print(f"   Active: {subscription.is_active}")
        print(f"   Start: {subscription.start_date}")
        print(f"   End: {subscription.end_date}")
        
        # Test payment creation
        print("\n2️⃣ Creating payment record...")
        
        payment = Payment.objects.create(
            user=user,
            payment_type='subscription',
            amount=monthly_price * duration_months,
            status='completed',
            transaction_id=f'SUB_{subscription.id}_{timezone.now().timestamp()}',
            description=f'{plan_type.title()} subscription for {duration_months} month(s)'
        )
        
        print(f"✅ Created payment: {payment}")
        
        # Test notification creation
        print("\n3️⃣ Creating notification...")
        
        notification = Notification.objects.create(
            user=user,
            title='Subscription Activated',
            message=f'Your {plan_type.title()} subscription has been activated successfully!',
            notification_type='subscription'
        )
        
        print(f"✅ Created notification: {notification}")
        
        # Test subscription properties
        print("\n4️⃣ Testing subscription properties...")
        
        # Refresh from database
        subscription.refresh_from_db()
        
        print(f"   Is Active: {subscription.is_active}")
        print(f"   Days until expiry: {(subscription.end_date.date() - timezone.now().date()).days}")
        
        print("\n🎉 All subscription operations completed successfully!")
        return True
        
    except Exception as e:
        print(f"❌ Error during subscription creation: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == '__main__':
    print("🎾 Tennis Management System - Direct Subscription Test")
    print("="*60)
    
    success = test_subscription_creation()
    
    if success:
        print("\n🚀 Direct subscription creation works!")
        print("✅ The subscription API should now work correctly.")
    else:
        print("\n⚠️  Direct subscription creation failed.")
