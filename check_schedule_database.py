#!/usr/bin/env python
"""
Vérification des horaires stockés dans la base de données
"""
import os
import sys
import django
from datetime import datetime

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'tennis_manager.settings')
django.setup()

from django.contrib.auth import get_user_model
from reservations.models import Coach, Schedule, ScheduleSlot
from django.db import connection

User = get_user_model()

def show_database_structure():
    print("🗄️  STRUCTURE DE LA BASE DE DONNÉES - HORAIRES")
    print("="*55)
    
    print("\n📋 TABLES CONCERNÉES :")
    print("="*25)
    print("1. 📅 reservations_coach_model (Schedule)")
    print("   → Horaires hebdomadaires récurrents")
    print("   → Ex: 'Tous les lundis de 9h à 12h'")
    
    print("\n2. ⏰ coach_schedule_slots (ScheduleSlot)")
    print("   → Créneaux spécifiques avec dates")
    print("   → Ex: '12 juin 2025 de 15h à 16h'")
    
    print("\n3. 👨‍🏫 coach (Coach)")
    print("   → Profils des coachs")

def check_schedule_tables():
    print("\n🔍 VÉRIFICATION DES TABLES D'HORAIRES")
    print("="*45)
    
    # 1. Table Schedule (horaires hebdomadaires)
    print("\n1️⃣ Table 'reservations_coach_model' (Schedule):")
    schedules = Schedule.objects.all()
    print(f"   📊 Total horaires hebdomadaires: {schedules.count()}")
    
    if schedules.exists():
        print("   📋 Exemples d'horaires hebdomadaires:")
        for schedule in schedules[:5]:
            print(f"      - {schedule.coach.name}: {schedule.get_day_of_week_display()} {schedule.start_time}-{schedule.end_time}")
    else:
        print("   ❌ Aucun horaire hebdomadaire trouvé")
    
    # 2. Table ScheduleSlot (créneaux spécifiques)
    print(f"\n2️⃣ Table 'coach_schedule_slots' (ScheduleSlot):")
    slots = ScheduleSlot.objects.all()
    print(f"   📊 Total créneaux spécifiques: {slots.count()}")
    
    if slots.exists():
        print("   📋 Derniers créneaux créés:")
        recent_slots = slots.order_by('-id')[:10]
        for slot in recent_slots:
            status = "🔒 Réservé" if slot.is_booked else "✅ Disponible"
            print(f"      - ID {slot.id}: {slot.coach.name} - {slot.date} {slot.start_time}-{slot.end_time} ({status})")
    else:
        print("   ❌ Aucun créneau spécifique trouvé")

def check_by_coach():
    print("\n👨‍🏫 HORAIRES PAR COACH")
    print("="*25)
    
    coaches = Coach.objects.all()
    
    for coach in coaches:
        print(f"\n🎾 {coach.name} (ID: {coach.id}):")
        
        # Horaires hebdomadaires
        weekly_schedules = Schedule.objects.filter(coach=coach, is_active=True)
        print(f"   📅 Horaires hebdomadaires: {weekly_schedules.count()}")
        for schedule in weekly_schedules:
            print(f"      - {schedule.get_day_of_week_display()}: {schedule.start_time}-{schedule.end_time}")
        
        # Créneaux spécifiques
        specific_slots = ScheduleSlot.objects.filter(coach=coach)
        available_slots = specific_slots.filter(is_booked=False)
        booked_slots = specific_slots.filter(is_booked=True)
        
        print(f"   ⏰ Créneaux spécifiques: {specific_slots.count()}")
        print(f"      ✅ Disponibles: {available_slots.count()}")
        print(f"      🔒 Réservés: {booked_slots.count()}")
        
        # Afficher quelques créneaux récents
        recent_slots = specific_slots.order_by('-date', '-start_time')[:3]
        if recent_slots:
            print(f"   📋 Créneaux récents:")
            for slot in recent_slots:
                status = "Réservé" if slot.is_booked else "Disponible"
                print(f"      - {slot.date} {slot.start_time}-{slot.end_time} ({status})")

def show_sql_queries():
    print("\n💻 REQUÊTES SQL POUR VÉRIFIER MANUELLEMENT")
    print("="*45)
    
    print("📝 Requêtes SQL directes :")
    print("="*30)
    
    print("\n1. Voir tous les horaires hebdomadaires :")
    print("SELECT s.id, c.name as coach_name, s.day_of_week, s.start_time, s.end_time, s.is_active")
    print("FROM reservations_coach_model s")
    print("JOIN coach c ON s.coach_id = c.id")
    print("ORDER BY c.name, s.day_of_week;")
    
    print("\n2. Voir tous les créneaux spécifiques :")
    print("SELECT ss.id, c.name as coach_name, ss.date, ss.start_time, ss.end_time, ss.is_booked")
    print("FROM coach_schedule_slots ss")
    print("JOIN coach c ON ss.coach_id = c.id")
    print("ORDER BY ss.date DESC, ss.start_time;")
    
    print("\n3. Compter les horaires par coach :")
    print("SELECT c.name, ")
    print("       COUNT(s.id) as horaires_hebdo,")
    print("       COUNT(ss.id) as creneaux_specifiques")
    print("FROM coach c")
    print("LEFT JOIN reservations_coach_model s ON c.id = s.coach_id")
    print("LEFT JOIN coach_schedule_slots ss ON c.id = ss.coach_id")
    print("GROUP BY c.id, c.name;")

def execute_sql_queries():
    print("\n🔍 EXÉCUTION DES REQUÊTES SQL")
    print("="*35)
    
    with connection.cursor() as cursor:
        # 1. Horaires hebdomadaires
        print("\n1️⃣ Horaires hebdomadaires (reservations_coach_model):")
        cursor.execute("""
            SELECT s.id, c.name as coach_name, s.day_of_week, s.start_time, s.end_time, s.is_active
            FROM reservations_coach_model s
            JOIN coach c ON s.coach_id = c.id
            ORDER BY c.name, s.day_of_week
            LIMIT 10
        """)
        
        rows = cursor.fetchall()
        if rows:
            print("   📋 Résultats:")
            for row in rows:
                print(f"      ID {row[0]}: {row[1]} - {row[2]} {row[3]}-{row[4]} (Actif: {row[5]})")
        else:
            print("   ❌ Aucun résultat")
        
        # 2. Créneaux spécifiques
        print("\n2️⃣ Créneaux spécifiques (coach_schedule_slots):")
        cursor.execute("""
            SELECT ss.id, c.name as coach_name, ss.date, ss.start_time, ss.end_time, ss.is_booked
            FROM coach_schedule_slots ss
            JOIN coach c ON ss.coach_id = c.id
            ORDER BY ss.id DESC
            LIMIT 10
        """)
        
        rows = cursor.fetchall()
        if rows:
            print("   📋 Derniers créneaux:")
            for row in rows:
                status = "Réservé" if row[5] else "Disponible"
                print(f"      ID {row[0]}: {row[1]} - {row[2]} {row[3]}-{row[4]} ({status})")
        else:
            print("   ❌ Aucun résultat")
        
        # 3. Statistiques par coach
        print("\n3️⃣ Statistiques par coach:")
        cursor.execute("""
            SELECT c.name, c.id,
                   (SELECT COUNT(*) FROM reservations_coach_model s WHERE s.coach_id = c.id) as horaires_hebdo,
                   (SELECT COUNT(*) FROM coach_schedule_slots ss WHERE ss.coach_id = c.id) as creneaux_specifiques
            FROM coach c
            ORDER BY c.name
        """)
        
        rows = cursor.fetchall()
        if rows:
            print("   📊 Statistiques:")
            for row in rows:
                print(f"      {row[0]} (ID {row[1]}): {row[2]} horaires hebdo, {row[3]} créneaux spécifiques")
        else:
            print("   ❌ Aucun coach trouvé")

def show_database_location():
    print("\n📍 LOCALISATION DE LA BASE DE DONNÉES")
    print("="*40)
    
    from django.conf import settings
    
    db_config = settings.DATABASES['default']
    db_engine = db_config['ENGINE']
    
    print(f"🗄️  Type de base: {db_engine}")
    
    if 'sqlite' in db_engine.lower():
        db_path = db_config['NAME']
        print(f"📁 Fichier SQLite: {db_path}")
        print(f"📂 Répertoire: {os.path.dirname(db_path)}")
        
        # Vérifier si le fichier existe
        if os.path.exists(db_path):
            size = os.path.getsize(db_path)
            print(f"📊 Taille: {size} bytes ({size/1024:.1f} KB)")
        else:
            print("❌ Fichier de base de données non trouvé!")
    
    elif 'postgresql' in db_engine.lower():
        print(f"🏢 Serveur PostgreSQL: {db_config.get('HOST', 'localhost')}")
        print(f"🗄️  Base de données: {db_config.get('NAME', 'N/A')}")
        print(f"👤 Utilisateur: {db_config.get('USER', 'N/A')}")
    
    elif 'mysql' in db_engine.lower():
        print(f"🏢 Serveur MySQL: {db_config.get('HOST', 'localhost')}")
        print(f"🗄️  Base de données: {db_config.get('NAME', 'N/A')}")
        print(f"👤 Utilisateur: {db_config.get('USER', 'N/A')}")

if __name__ == '__main__':
    print("🎾 TENNIS MANAGEMENT SYSTEM")
    print("🔍 Vérification Base de Données - Horaires")
    print("="*55)
    
    # Afficher la structure
    show_database_structure()
    
    # Vérifier les tables
    check_schedule_tables()
    
    # Vérifier par coach
    check_by_coach()
    
    # Exécuter les requêtes SQL
    execute_sql_queries()
    
    # Montrer les requêtes SQL
    show_sql_queries()
    
    # Localisation de la base
    show_database_location()
    
    print("\n🎯 RÉSUMÉ:")
    print("="*15)
    print("✅ Les horaires sont stockés dans 2 tables:")
    print("   1. reservations_coach_model (horaires hebdomadaires)")
    print("   2. coach_schedule_slots (créneaux spécifiques)")
    print("✅ Utilisez les requêtes SQL ci-dessus pour vérifier manuellement")
    print("✅ Les modèles Django Schedule et ScheduleSlot gèrent ces données")
