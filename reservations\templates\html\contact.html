{% load static %}
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Contact Us - Elite Sports Club</title>
    
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.0/font/bootstrap-icons.css" rel="stylesheet">
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Orbitron:wght@400;700;900&family=Rajdhani:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <style>
        :root {
            --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            --secondary-gradient: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            --accent-gradient: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            --success-gradient: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);
            --warning-gradient: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
            --dark-gradient: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Rajdhani', sans-serif;
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
            padding-top: 80px;
        }

        /* Header Section */
        .contact-header {
            background: linear-gradient(135deg, rgba(30, 64, 175, 0.9) 0%, rgba(139, 92, 246, 0.9) 50%, rgba(16, 185, 129, 0.9) 100%);
            padding: 4rem 0;
            text-align: center;
            color: white;
            position: relative;
            overflow: hidden;
        }

        .contact-header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="25" cy="25" r="2" fill="rgba(255,255,255,0.1)"/><circle cx="75" cy="75" r="1.5" fill="rgba(255,255,255,0.08)"/><rect x="45" y="45" width="10" height="10" rx="2" fill="rgba(255,255,255,0.06)"/></svg>') repeat;
            animation: pulse 8s ease-in-out infinite;
        }

        @keyframes pulse {
            0%, 100% { opacity: 0.3; }
            50% { opacity: 0.7; }
        }

        .contact-title {
            font-family: 'Orbitron', monospace;
            font-size: 3rem;
            font-weight: 900;
            margin-bottom: 1rem;
            text-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
            position: relative;
            z-index: 2;
        }

        .contact-subtitle {
            font-size: 1.2rem;
            opacity: 0.9;
            position: relative;
            z-index: 2;
        }

        /* Contact Section */
        .contact-section {
            padding: 4rem 0;
        }

        .contact-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 2rem;
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 4rem;
            align-items: start;
        }

        /* Contact Form */
        .contact-form-container {
            background: white;
            border-radius: 25px;
            padding: 3rem;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .form-title {
            font-family: 'Orbitron', monospace;
            font-size: 2rem;
            font-weight: 700;
            margin-bottom: 2rem;
            background: var(--primary-gradient);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .form-group {
            margin-bottom: 1.5rem;
        }

        .form-label {
            font-weight: 600;
            color: #374151;
            margin-bottom: 0.5rem;
            font-size: 1rem;
        }

        .input-group {
            position: relative;
        }

        .input-icon {
            position: absolute;
            left: 15px;
            top: 50%;
            transform: translateY(-50%);
            color: #9ca3af;
            z-index: 3;
            font-size: 1.1rem;
        }

        .form-control, .form-select {
            padding: 1rem 1rem 1rem 3rem;
            border: 2px solid #e5e7eb;
            border-radius: 15px;
            font-size: 1rem;
            transition: all 0.3s ease;
            background: rgba(255, 255, 255, 0.8);
            width: 100%;
        }

        .form-control:focus, .form-select:focus {
            border-color: #4facfe;
            box-shadow: 0 0 0 3px rgba(79, 172, 254, 0.1);
            background: white;
            outline: none;
        }

        .form-control.textarea {
            min-height: 120px;
            resize: vertical;
            padding-top: 1rem;
        }

        .submit-btn {
            width: 100%;
            padding: 1rem;
            background: var(--success-gradient);
            border: none;
            border-radius: 15px;
            color: white;
            font-size: 1.1rem;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 1px;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .submit-btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: left 0.5s;
        }

        .submit-btn:hover::before {
            left: 100%;
        }

        .submit-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(17, 153, 142, 0.4);
        }

        /* Contact Info */
        .contact-info {
            display: flex;
            flex-direction: column;
            gap: 2rem;
        }

        .info-card {
            background: white;
            border-radius: 20px;
            padding: 2rem;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .info-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: var(--accent-gradient);
            transform: scaleX(0);
            transition: transform 0.3s ease;
        }

        .info-card:hover::before {
            transform: scaleX(1);
        }

        .info-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.15);
        }

        .info-icon {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
            color: white;
            margin-bottom: 1rem;
        }

        .info-card.location .info-icon {
            background: var(--success-gradient);
        }

        .info-card.phone .info-icon {
            background: var(--accent-gradient);
        }

        .info-card.email .info-icon {
            background: var(--secondary-gradient);
        }

        .info-card.hours .info-icon {
            background: var(--warning-gradient);
        }

        .info-title {
            font-size: 1.3rem;
            font-weight: 700;
            margin-bottom: 0.5rem;
            color: #2d3748;
        }

        .info-text {
            color: #718096;
            line-height: 1.6;
        }

        /* Map Section */
        .map-section {
            background: white;
            border-radius: 20px;
            padding: 2rem;
            margin-top: 2rem;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        }

        .map-placeholder {
            height: 300px;
            background: var(--primary-gradient);
            border-radius: 15px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 3rem;
            position: relative;
            overflow: hidden;
        }

        .map-placeholder::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.1), transparent);
            transform: translateX(-100%);
            animation: shine 3s ease-in-out infinite;
        }

        @keyframes shine {
            0% { transform: translateX(-100%); }
            100% { transform: translateX(100%); }
        }

        /* Alert Messages */
        .alert {
            border-radius: 15px;
            border: none;
            margin-bottom: 1.5rem;
        }

        .alert-success {
            background: linear-gradient(135deg, #d1fae5 0%, #a7f3d0 100%);
            color: #059669;
        }

        .alert-danger {
            background: linear-gradient(135deg, #fee2e2 0%, #fecaca 100%);
            color: #dc2626;
        }

        /* Responsive */
        @media (max-width: 768px) {
            .contact-title {
                font-size: 2.5rem;
            }
            
            .contact-container {
                grid-template-columns: 1fr;
                gap: 2rem;
                padding: 0 1rem;
            }
            
            .contact-form-container {
                padding: 2rem;
            }
            
            .form-title {
                font-size: 1.5rem;
            }
        }
    </style>
</head>
<body>
    <!-- Include Navbar -->
    {% include 'html/navbar.html' %}

    <!-- Contact Header -->
    <section class="contact-header">
        <div class="container">
            <h1 class="contact-title">CONTACT US</h1>
            <p class="contact-subtitle">Get in touch with our team - we're here to help you succeed</p>
        </div>
    </section>

    <!-- Contact Section -->
    <section class="contact-section">
        <div class="contact-container">
            <!-- Contact Form -->
            <div class="contact-form-container">
                <h2 class="form-title">Send us a Message</h2>
                
                <!-- Success/Error Messages -->
                <div id="success-message" class="alert alert-success d-none" role="alert">
                    Thank you! Your message has been sent successfully.
                </div>
                <div id="error-message" class="alert alert-danger d-none" role="alert">
                    Sorry, there was an error sending your message. Please try again.
                </div>

                <form id="contactForm">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="firstName" class="form-label">First Name</label>
                                <div class="input-group">
                                    <i class="bi bi-person input-icon"></i>
                                    <input type="text" class="form-control" id="firstName" name="firstName" required 
                                           placeholder="Your first name">
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="lastName" class="form-label">Last Name</label>
                                <div class="input-group">
                                    <i class="bi bi-person-fill input-icon"></i>
                                    <input type="text" class="form-control" id="lastName" name="lastName" required 
                                           placeholder="Your last name">
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="email" class="form-label">Email Address</label>
                        <div class="input-group">
                            <i class="bi bi-envelope input-icon"></i>
                            <input type="email" class="form-control" id="email" name="email" required 
                                   placeholder="<EMAIL>">
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="phone" class="form-label">Phone Number</label>
                        <div class="input-group">
                            <i class="bi bi-telephone input-icon"></i>
                            <input type="tel" class="form-control" id="phone" name="phone" 
                                   placeholder="+****************">
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="subject" class="form-label">Subject</label>
                        <div class="input-group">
                            <i class="bi bi-chat-dots input-icon"></i>
                            <select class="form-select" id="subject" name="subject" required>
                                <option value="">Select a subject</option>
                                <option value="membership">Membership Inquiry</option>
                                <option value="training">Training Programs</option>
                                <option value="facilities">Facilities Information</option>
                                <option value="events">Events & Competitions</option>
                                <option value="support">Technical Support</option>
                                <option value="other">Other</option>
                            </select>
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="message" class="form-label">Message</label>
                        <div class="input-group">
                            <i class="bi bi-chat-text input-icon" style="top: 20px;"></i>
                            <textarea class="form-control textarea" id="message" name="message" required 
                                      placeholder="Tell us how we can help you..."></textarea>
                        </div>
                    </div>

                    <button type="submit" class="submit-btn">
                        Send Message
                    </button>
                </form>
            </div>

            <!-- Contact Info -->
            <div class="contact-info">
                <div class="info-card location">
                    <div class="info-icon">
                        <i class="bi bi-geo-alt-fill"></i>
                    </div>
                    <h3 class="info-title">Visit Us</h3>
                    <p class="info-text">
                        123 Sports Avenue<br>
                        Elite District, ED 12345<br>
                        United States
                    </p>
                </div>

                <div class="info-card phone">
                    <div class="info-icon">
                        <i class="bi bi-telephone-fill"></i>
                    </div>
                    <h3 class="info-title">Call Us</h3>
                    <p class="info-text">
                        Main: +****************<br>
                        Emergency: +****************<br>
                        Mon-Fri: 6AM - 10PM
                    </p>
                </div>

                <div class="info-card email">
                    <div class="info-icon">
                        <i class="bi bi-envelope-fill"></i>
                    </div>
                    <h3 class="info-title">Email Us</h3>
                    <p class="info-text">
                        <EMAIL><br>
                        <EMAIL><br>
                        We reply within 24 hours
                    </p>
                </div>

                <div class="info-card hours">
                    <div class="info-icon">
                        <i class="bi bi-clock-fill"></i>
                    </div>
                    <h3 class="info-title">Opening Hours</h3>
                    <p class="info-text">
                        Monday - Friday: 6:00 AM - 10:00 PM<br>
                        Saturday - Sunday: 7:00 AM - 9:00 PM<br>
                        Holidays: 8:00 AM - 6:00 PM
                    </p>
                </div>

                <!-- Map Section -->
                <div class="map-section">
                    <h3 class="info-title" style="margin-bottom: 1rem;">Find Us</h3>
                    <div class="map-placeholder">
                        <i class="bi bi-map"></i>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- JavaScript -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Contact form submission
        document.getElementById('contactForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const formData = new FormData(this);
            const data = Object.fromEntries(formData);
            
            const successDiv = document.getElementById('success-message');
            const errorDiv = document.getElementById('error-message');
            
            // Hide previous messages
            successDiv.classList.add('d-none');
            errorDiv.classList.add('d-none');
            
            // Simulate form submission (replace with actual API call)
            setTimeout(() => {
                if (Math.random() > 0.1) { // 90% success rate for demo
                    successDiv.classList.remove('d-none');
                    this.reset();
                } else {
                    errorDiv.classList.remove('d-none');
                }
            }, 1000);
        });

        // Add hover effects to info cards
        const infoCards = document.querySelectorAll('.info-card');
        infoCards.forEach(card => {
            card.addEventListener('mouseenter', function() {
                this.style.transform = 'translateY(-8px) scale(1.02)';
            });
            
            card.addEventListener('mouseleave', function() {
                this.style.transform = 'translateY(0) scale(1)';
            });
        });

        // Animate cards on scroll
        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.style.animation = 'fadeInUp 0.6s ease-out';
                }
            });
        });

        infoCards.forEach(card => {
            observer.observe(card);
        });

        // Add fadeInUp animation
        const style = document.createElement('style');
        style.textContent = `
            @keyframes fadeInUp {
                from {
                    opacity: 0;
                    transform: translateY(30px);
                }
                to {
                    opacity: 1;
                    transform: translateY(0);
                }
            }
        `;
        document.head.appendChild(style);
    </script>
</body>
</html>
