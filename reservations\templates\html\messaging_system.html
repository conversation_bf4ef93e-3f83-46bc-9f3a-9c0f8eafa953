{% load static %}
<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Messaging System - Elite Sports Club</title>

    <!-- Bootstrap Icons -->
    <link
      href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.0/font/bootstrap-icons.css"
      rel="stylesheet"
    />
    <!-- Bootstrap CSS -->
    <link
      href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css"
      rel="stylesheet"
    />
    <!-- Google Fonts -->
    <link
      href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Poppins:wght@300;400;500;600;700&display=swap"
      rel="stylesheet"
    />

    <style>
      :root {
        --primary-color: #6366f1;
        --secondary-color: #10b981;
        --accent-color: #f59e0b;
        --danger-color: #ef4444;
        --gray-50: #f9fafb;
        --gray-100: #f3f4f6;
        --gray-200: #e5e7eb;
        --gray-300: #d1d5db;
        --gray-600: #4b5563;
        --gray-700: #374151;
        --gray-800: #1f2937;
      }

      body {
        font-family: "Inter", sans-serif;
        background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
        color: var(--gray-700);
        padding-top: 80px;
        height: 100vh;
        overflow: hidden;
      }

      .messaging-container {
        height: calc(100vh - 80px);
        display: flex;
        max-width: 1400px;
        margin: 0 auto;
        padding: 1rem;
        gap: 1rem;
      }

      .sidebar {
        width: 350px;
        background: white;
        border-radius: 16px;
        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
        border: 1px solid var(--gray-100);
        display: flex;
        flex-direction: column;
        overflow: hidden;
      }

      .sidebar-header {
        padding: 1.5rem;
        border-bottom: 1px solid var(--gray-100);
        background: linear-gradient(
          135deg,
          var(--primary-color) 0%,
          var(--secondary-color) 100%
        );
        color: white;
      }

      .sidebar-title {
        font-family: "Poppins", sans-serif;
        font-weight: 600;
        margin: 0;
        display: flex;
        align-items: center;
        gap: 0.5rem;
      }

      .search-section {
        padding: 1rem;
        border-bottom: 1px solid var(--gray-100);
      }

      .search-input {
        width: 100%;
        padding: 0.75rem 1rem;
        border: 2px solid var(--gray-200);
        border-radius: 12px;
        font-size: 0.875rem;
        transition: all 0.2s ease;
      }

      .search-input:focus {
        border-color: var(--primary-color);
        box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
        outline: none;
      }

      .conversations-list {
        flex: 1;
        overflow-y: auto;
        padding: 0.5rem;
      }

      .conversation-item {
        display: flex;
        align-items: center;
        gap: 1rem;
        padding: 1rem;
        border-radius: 12px;
        cursor: pointer;
        transition: all 0.2s ease;
        margin-bottom: 0.5rem;
        position: relative;
      }

      .conversation-item:hover {
        background: var(--gray-50);
      }

      .conversation-item.active {
        background: var(--primary-color);
        color: white;
      }

      .conversation-avatar {
        width: 48px;
        height: 48px;
        border-radius: 50%;
        background: linear-gradient(
          135deg,
          var(--primary-color),
          var(--secondary-color)
        );
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-weight: 600;
        font-size: 1.1rem;
        flex-shrink: 0;
      }

      .conversation-info {
        flex: 1;
        min-width: 0;
      }

      .conversation-name {
        font-weight: 600;
        margin-bottom: 0.25rem;
        font-size: 0.95rem;
      }

      .conversation-preview {
        font-size: 0.8rem;
        opacity: 0.7;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }

      .conversation-meta {
        display: flex;
        flex-direction: column;
        align-items: flex-end;
        gap: 0.25rem;
      }

      .conversation-time {
        font-size: 0.75rem;
        opacity: 0.6;
      }

      .unread-badge {
        background: var(--danger-color);
        color: white;
        border-radius: 50%;
        width: 20px;
        height: 20px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 0.7rem;
        font-weight: 600;
      }

      .chat-area {
        flex: 1;
        background: white;
        border-radius: 16px;
        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
        border: 1px solid var(--gray-100);
        display: flex;
        flex-direction: column;
        overflow: hidden;
      }

      .chat-header {
        padding: 1.5rem;
        border-bottom: 1px solid var(--gray-100);
        background: var(--gray-50);
        display: flex;
        align-items: center;
        gap: 1rem;
      }

      .chat-avatar {
        width: 48px;
        height: 48px;
        border-radius: 50%;
        background: linear-gradient(
          135deg,
          var(--primary-color),
          var(--secondary-color)
        );
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-weight: 600;
        font-size: 1.1rem;
      }

      .chat-info h3 {
        margin: 0;
        font-size: 1.1rem;
        font-weight: 600;
        color: var(--gray-800);
      }

      .chat-status {
        font-size: 0.8rem;
        color: var(--gray-600);
        display: flex;
        align-items: center;
        gap: 0.25rem;
      }

      .status-online {
        width: 8px;
        height: 8px;
        background: var(--secondary-color);
        border-radius: 50%;
      }

      .chat-actions {
        margin-left: auto;
        display: flex;
        gap: 0.5rem;
      }

      .chat-btn {
        width: 40px;
        height: 40px;
        border: none;
        border-radius: 50%;
        background: var(--gray-100);
        color: var(--gray-600);
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        transition: all 0.2s ease;
      }

      .chat-btn:hover {
        background: var(--primary-color);
        color: white;
      }

      .messages-container {
        flex: 1;
        overflow-y: auto;
        padding: 1rem;
        display: flex;
        flex-direction: column;
        gap: 1rem;
      }

      .message {
        display: flex;
        gap: 0.75rem;
        max-width: 70%;
      }

      .message.sent {
        align-self: flex-end;
        flex-direction: row-reverse;
      }

      .message-avatar {
        width: 32px;
        height: 32px;
        border-radius: 50%;
        background: linear-gradient(
          135deg,
          var(--primary-color),
          var(--secondary-color)
        );
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-weight: 600;
        font-size: 0.8rem;
        flex-shrink: 0;
      }

      .message-content {
        flex: 1;
      }

      .message-bubble {
        background: var(--gray-100);
        padding: 0.75rem 1rem;
        border-radius: 18px;
        font-size: 0.9rem;
        line-height: 1.4;
      }

      .message.sent .message-bubble {
        background: var(--primary-color);
        color: white;
      }

      .message-time {
        font-size: 0.7rem;
        color: var(--gray-500);
        margin-top: 0.25rem;
        text-align: right;
      }

      .message.received .message-time {
        text-align: left;
      }

      .message-input-area {
        padding: 1rem;
        border-top: 1px solid var(--gray-100);
        background: var(--gray-50);
      }

      .message-input-container {
        display: flex;
        gap: 0.75rem;
        align-items: flex-end;
      }

      .message-input {
        flex: 1;
        padding: 0.75rem 1rem;
        border: 2px solid var(--gray-200);
        border-radius: 20px;
        resize: none;
        font-size: 0.9rem;
        line-height: 1.4;
        max-height: 100px;
        transition: all 0.2s ease;
      }

      .message-input:focus {
        border-color: var(--primary-color);
        box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
        outline: none;
      }

      .send-btn {
        width: 44px;
        height: 44px;
        border: none;
        border-radius: 50%;
        background: var(--primary-color);
        color: white;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        transition: all 0.2s ease;
        flex-shrink: 0;
      }

      .send-btn:hover {
        background: #5856eb;
        transform: scale(1.05);
      }

      .send-btn:disabled {
        background: var(--gray-300);
        cursor: not-allowed;
        transform: none;
      }

      .empty-state {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        height: 100%;
        color: var(--gray-500);
        text-align: center;
      }

      .empty-icon {
        width: 80px;
        height: 80px;
        background: var(--gray-100);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 2rem;
        margin-bottom: 1rem;
      }

      /* Responsive */
      @media (max-width: 768px) {
        .messaging-container {
          padding: 0.5rem;
          flex-direction: column;
          height: calc(100vh - 80px);
        }

        .sidebar {
          width: 100%;
          height: 40%;
        }

        .chat-area {
          height: 60%;
        }
      }

      /* Scrollbar Styling */
      .conversations-list::-webkit-scrollbar,
      .messages-container::-webkit-scrollbar {
        width: 6px;
      }

      .conversations-list::-webkit-scrollbar-track,
      .messages-container::-webkit-scrollbar-track {
        background: var(--gray-100);
        border-radius: 3px;
      }

      .conversations-list::-webkit-scrollbar-thumb,
      .messages-container::-webkit-scrollbar-thumb {
        background: var(--gray-300);
        border-radius: 3px;
      }

      .conversations-list::-webkit-scrollbar-thumb:hover,
      .messages-container::-webkit-scrollbar-thumb:hover {
        background: var(--gray-400);
      }
    </style>
  </head>
  <body>
    <!-- Include Navbar -->
    {% include 'html/navbar.html' %}

    <div class="messaging-container">
      <!-- Sidebar -->
      <div class="sidebar">
        <div class="sidebar-header">
          <h2 class="sidebar-title">
            <i class="bi bi-chat-dots"></i>
            Messages
          </h2>
        </div>

        <div class="search-section">
          <input
            type="text"
            class="search-input"
            placeholder="Search conversations..."
            id="searchInput"
          />
        </div>

        <div class="conversations-list" id="conversationsList">
          <!-- Conversations will be populated here -->
        </div>
      </div>

      <!-- Chat Area -->
      <div class="chat-area">
        <div id="chatContent">
          <div class="empty-state">
            <div class="empty-icon">
              <i class="bi bi-chat-text"></i>
            </div>
            <h3>Select a conversation</h3>
            <p>Choose a conversation from the sidebar to start messaging</p>
          </div>
        </div>
      </div>
    </div>

    <!-- JavaScript -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
      // Sample conversations data
      const conversations = [
        {
          id: 1,
          name: "Coach Martinez",
          role: "Tennis Coach",
          avatar: "CM",
          lastMessage: "Great practice today! Keep up the good work.",
          time: "2 min ago",
          unread: 2,
          online: true,
          messages: [
            {
              id: 1,
              sender: "Coach Martinez",
              content: "Hi! How are you feeling about tomorrow's match?",
              time: "10:30 AM",
              sent: false,
            },
            {
              id: 2,
              sender: "You",
              content: "I'm feeling confident! Been practicing my serves.",
              time: "10:32 AM",
              sent: true,
            },
            {
              id: 3,
              sender: "Coach Martinez",
              content:
                "That's great to hear! Remember to focus on your footwork too.",
              time: "10:35 AM",
              sent: false,
            },
            {
              id: 4,
              sender: "Coach Martinez",
              content: "Great practice today! Keep up the good work.",
              time: "2:15 PM",
              sent: false,
            },
          ],
        },
        {
          id: 2,
          name: "Sarah Williams",
          role: "Team Captain",
          avatar: "SW",
          lastMessage: "Are you joining us for the team dinner?",
          time: "1 hour ago",
          unread: 0,
          online: true,
          messages: [
            {
              id: 1,
              sender: "Sarah Williams",
              content: "Hey! Team meeting at 3 PM today.",
              time: "9:00 AM",
              sent: false,
            },
            {
              id: 2,
              sender: "You",
              content: "Thanks for the reminder! I'll be there.",
              time: "9:05 AM",
              sent: true,
            },
            {
              id: 3,
              sender: "Sarah Williams",
              content: "Are you joining us for the team dinner?",
              time: "1:30 PM",
              sent: false,
            },
          ],
        },
        {
          id: 3,
          name: "Mike Chen",
          role: "Training Partner",
          avatar: "MC",
          lastMessage: "Let's schedule our next practice session",
          time: "3 hours ago",
          unread: 1,
          online: false,
          messages: [
            {
              id: 1,
              sender: "Mike Chen",
              content: "Good game yesterday!",
              time: "Yesterday",
              sent: false,
            },
            {
              id: 2,
              sender: "You",
              content: "Thanks! You really pushed me to play better.",
              time: "Yesterday",
              sent: true,
            },
            {
              id: 3,
              sender: "Mike Chen",
              content: "Let's schedule our next practice session",
              time: "11:00 AM",
              sent: false,
            },
          ],
        },
        {
          id: 4,
          name: "Admin Team",
          role: "Administration",
          avatar: "AT",
          lastMessage: "Your membership renewal is due next week",
          time: "1 day ago",
          unread: 0,
          online: false,
          messages: [
            {
              id: 1,
              sender: "Admin Team",
              content: "Welcome to Elite Sports Club!",
              time: "Last week",
              sent: false,
            },
            {
              id: 2,
              sender: "Admin Team",
              content: "Your membership renewal is due next week",
              time: "Yesterday",
              sent: false,
            },
          ],
        },
      ];

      let activeConversation = null;

      function renderConversations() {
        const container = document.getElementById("conversationsList");
        container.innerHTML = "";

        conversations.forEach((conversation) => {
          const conversationElement = document.createElement("div");
          conversationElement.className = `conversation-item ${
            activeConversation === conversation.id ? "active" : ""
          }`;
          conversationElement.onclick = () => openConversation(conversation.id);

          conversationElement.innerHTML = `
                    <div class="conversation-avatar">${
                      conversation.avatar
                    }</div>
                    <div class="conversation-info">
                        <div class="conversation-name">${
                          conversation.name
                        }</div>
                        <div class="conversation-preview">${
                          conversation.lastMessage
                        }</div>
                    </div>
                    <div class="conversation-meta">
                        <div class="conversation-time">${
                          conversation.time
                        }</div>
                        ${
                          conversation.unread > 0
                            ? `<div class="unread-badge">${conversation.unread}</div>`
                            : ""
                        }
                    </div>
                `;

          container.appendChild(conversationElement);
        });
      }

      function openConversation(conversationId) {
        activeConversation = conversationId;
        const conversation = conversations.find((c) => c.id === conversationId);

        if (!conversation) return;

        // Mark as read
        conversation.unread = 0;

        const chatContent = document.getElementById("chatContent");
        chatContent.innerHTML = `
                <div class="chat-header">
                    <div class="chat-avatar">${conversation.avatar}</div>
                    <div class="chat-info">
                        <h3>${conversation.name}</h3>
                        <div class="chat-status">
                            ${
                              conversation.online
                                ? '<div class="status-online"></div> Online'
                                : "Offline"
                            }
                        </div>
                    </div>
                    <div class="chat-actions">
                        <button class="chat-btn" title="Voice Call">
                            <i class="bi bi-telephone"></i>
                        </button>
                        <button class="chat-btn" title="Video Call">
                            <i class="bi bi-camera-video"></i>
                        </button>
                        <button class="chat-btn" title="More Options">
                            <i class="bi bi-three-dots"></i>
                        </button>
                    </div>
                </div>
                
                <div class="messages-container" id="messagesContainer">
                    ${conversation.messages
                      .map(
                        (message) => `
                        <div class="message ${
                          message.sent ? "sent" : "received"
                        }">
                            <div class="message-avatar">${
                              message.sent
                                ? "You".charAt(0)
                                : conversation.avatar
                            }</div>
                            <div class="message-content">
                                <div class="message-bubble">${
                                  message.content
                                }</div>
                                <div class="message-time">${message.time}</div>
                            </div>
                        </div>
                    `
                      )
                      .join("")}
                </div>
                
                <div class="message-input-area">
                    <div class="message-input-container">
                        <textarea class="message-input" placeholder="Type a message..." id="messageInput" rows="1"></textarea>
                        <button class="send-btn" id="sendBtn" onclick="sendMessage()">
                            <i class="bi bi-send"></i>
                        </button>
                    </div>
                </div>
            `;

        // Auto-resize textarea
        const messageInput = document.getElementById("messageInput");
        messageInput.addEventListener("input", function () {
          this.style.height = "auto";
          this.style.height = Math.min(this.scrollHeight, 100) + "px";
        });

        // Send on Enter
        messageInput.addEventListener("keypress", function (e) {
          if (e.key === "Enter" && !e.shiftKey) {
            e.preventDefault();
            sendMessage();
          }
        });

        // Scroll to bottom
        const messagesContainer = document.getElementById("messagesContainer");
        messagesContainer.scrollTop = messagesContainer.scrollHeight;

        renderConversations();
      }

      function sendMessage() {
        const messageInput = document.getElementById("messageInput");
        const content = messageInput.value.trim();

        if (!content || !activeConversation) return;

        const conversation = conversations.find(
          (c) => c.id === activeConversation
        );
        const newMessage = {
          id: conversation.messages.length + 1,
          sender: "You",
          content: content,
          time: new Date().toLocaleTimeString([], {
            hour: "2-digit",
            minute: "2-digit",
          }),
          sent: true,
        };

        conversation.messages.push(newMessage);
        conversation.lastMessage = content;
        conversation.time = "now";

        // Re-render messages
        const messagesContainer = document.getElementById("messagesContainer");
        const messageElement = document.createElement("div");
        messageElement.className = "message sent";
        messageElement.innerHTML = `
                <div class="message-avatar">Y</div>
                <div class="message-content">
                    <div class="message-bubble">${content}</div>
                    <div class="message-time">${newMessage.time}</div>
                </div>
            `;

        messagesContainer.appendChild(messageElement);
        messagesContainer.scrollTop = messagesContainer.scrollHeight;

        messageInput.value = "";
        messageInput.style.height = "auto";

        renderConversations();
      }

      // Search functionality
      document
        .getElementById("searchInput")
        .addEventListener("input", function (e) {
          const searchTerm = e.target.value.toLowerCase();
          const conversationItems =
            document.querySelectorAll(".conversation-item");

          conversationItems.forEach((item) => {
            const name = item
              .querySelector(".conversation-name")
              .textContent.toLowerCase();
            const preview = item
              .querySelector(".conversation-preview")
              .textContent.toLowerCase();

            if (name.includes(searchTerm) || preview.includes(searchTerm)) {
              item.style.display = "flex";
            } else {
              item.style.display = "none";
            }
          });
        });

      // Initialize
      document.addEventListener("DOMContentLoaded", function () {
        renderConversations();

        // Simulate real-time updates
        setInterval(() => {
          // Randomly update online status
          conversations.forEach((conv) => {
            if (Math.random() > 0.8) {
              conv.online = !conv.online;
            }
          });

          if (activeConversation) {
            const conversation = conversations.find(
              (c) => c.id === activeConversation
            );
            if (conversation) {
              const statusElement = document.querySelector(".chat-status");
              if (statusElement) {
                statusElement.innerHTML = conversation.online
                  ? '<div class="status-online"></div> Online'
                  : "Offline";
              }
            }
          }

          renderConversations();
        }, 30000); // Update every 30 seconds
      });
    </script>
  </body>
</html>
