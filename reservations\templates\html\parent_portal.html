{% load static %}
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Parent Portal - Elite Sports Club</title>
    
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.0/font/bootstrap-icons.css" rel="stylesheet">
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <style>
        :root {
            --primary-color: #8b5cf6;
            --secondary-color: #06b6d4;
            --accent-color: #10b981;
            --warning-color: #f59e0b;
            --danger-color: #ef4444;
            --gray-50: #f9fafb;
            --gray-100: #f3f4f6;
            --gray-200: #e5e7eb;
            --gray-600: #4b5563;
            --gray-700: #374151;
            --gray-800: #1f2937;
        }

        body {
            font-family: 'Inter', sans-serif;
            background: linear-gradient(135deg, #faf5ff 0%, #f3e8ff 100%);
            color: var(--gray-700);
            padding-top: 80px;
        }

        .portal-container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 2rem;
        }

        .page-header {
            background: white;
            border-radius: 16px;
            padding: 2rem;
            margin-bottom: 2rem;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
            border: 1px solid var(--gray-100);
        }

        .page-title {
            font-family: 'Poppins', sans-serif;
            font-size: 2.5rem;
            font-weight: 700;
            color: var(--gray-800);
            margin: 0;
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .title-icon {
            width: 56px;
            height: 56px;
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            border-radius: 16px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.5rem;
        }

        .children-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 2rem;
            margin-bottom: 2rem;
        }

        .child-card {
            background: white;
            border-radius: 16px;
            padding: 2rem;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
            border: 1px solid var(--gray-100);
            transition: all 0.3s ease;
        }

        .child-card:hover {
            transform: translateY(-4px);
            box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
        }

        .child-header {
            display: flex;
            align-items: center;
            gap: 1rem;
            margin-bottom: 1.5rem;
        }

        .child-avatar {
            width: 64px;
            height: 64px;
            border-radius: 50%;
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.5rem;
            font-weight: 700;
        }

        .child-info h3 {
            margin: 0;
            font-weight: 600;
            color: var(--gray-800);
        }

        .child-age {
            color: var(--gray-600);
            font-size: 0.875rem;
        }

        .child-sport {
            background: var(--accent-color);
            color: white;
            padding: 0.25rem 0.75rem;
            border-radius: 20px;
            font-size: 0.75rem;
            font-weight: 500;
            margin-top: 0.5rem;
            display: inline-block;
        }

        .quick-stats {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 1rem;
            margin: 1.5rem 0;
        }

        .stat-item {
            text-align: center;
            padding: 1rem;
            background: var(--gray-50);
            border-radius: 12px;
        }

        .stat-value {
            font-size: 1.25rem;
            font-weight: 700;
            color: var(--primary-color);
            margin-bottom: 0.25rem;
        }

        .stat-label {
            font-size: 0.75rem;
            color: var(--gray-600);
            text-transform: uppercase;
            font-weight: 500;
        }

        .child-actions {
            display: flex;
            gap: 0.5rem;
        }

        .btn-action {
            flex: 1;
            padding: 0.75rem;
            border: none;
            border-radius: 8px;
            font-weight: 500;
            font-size: 0.875rem;
            transition: all 0.2s ease;
            cursor: pointer;
        }

        .btn-primary-action {
            background: var(--primary-color);
            color: white;
        }

        .btn-primary-action:hover {
            background: #7c3aed;
        }

        .btn-secondary-action {
            background: var(--gray-100);
            color: var(--gray-700);
        }

        .btn-secondary-action:hover {
            background: var(--gray-200);
        }

        .portal-sections {
            display: grid;
            grid-template-columns: 2fr 1fr;
            gap: 2rem;
        }

        .main-section {
            display: flex;
            flex-direction: column;
            gap: 2rem;
        }

        .sidebar-section {
            display: flex;
            flex-direction: column;
            gap: 2rem;
        }

        .section-card {
            background: white;
            border-radius: 16px;
            padding: 2rem;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
            border: 1px solid var(--gray-100);
        }

        .section-title {
            font-size: 1.25rem;
            font-weight: 600;
            color: var(--gray-800);
            margin-bottom: 1.5rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .schedule-item {
            display: flex;
            align-items: center;
            gap: 1rem;
            padding: 1rem;
            background: var(--gray-50);
            border-radius: 12px;
            margin-bottom: 1rem;
        }

        .schedule-time {
            background: var(--primary-color);
            color: white;
            padding: 0.5rem 0.75rem;
            border-radius: 8px;
            font-weight: 600;
            font-size: 0.875rem;
            min-width: 80px;
            text-align: center;
        }

        .schedule-details h4 {
            margin: 0 0 0.25rem 0;
            font-size: 1rem;
            color: var(--gray-800);
            font-weight: 600;
        }

        .schedule-details p {
            margin: 0;
            color: var(--gray-600);
            font-size: 0.875rem;
        }

        .message-item {
            display: flex;
            gap: 1rem;
            padding: 1rem;
            background: var(--gray-50);
            border-radius: 12px;
            margin-bottom: 1rem;
        }

        .message-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: var(--secondary-color);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: 600;
            font-size: 0.875rem;
            flex-shrink: 0;
        }

        .message-content h5 {
            margin: 0 0 0.25rem 0;
            font-size: 0.875rem;
            font-weight: 600;
            color: var(--gray-800);
        }

        .message-content p {
            margin: 0;
            font-size: 0.8rem;
            color: var(--gray-600);
        }

        .message-time {
            font-size: 0.7rem;
            color: var(--gray-500);
            margin-top: 0.25rem;
        }

        .payment-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 1rem;
            background: var(--gray-50);
            border-radius: 12px;
            margin-bottom: 1rem;
        }

        .payment-info h5 {
            margin: 0 0 0.25rem 0;
            font-size: 0.875rem;
            font-weight: 600;
            color: var(--gray-800);
        }

        .payment-info p {
            margin: 0;
            font-size: 0.8rem;
            color: var(--gray-600);
        }

        .payment-amount {
            font-weight: 700;
            color: var(--primary-color);
        }

        .payment-status {
            padding: 0.25rem 0.75rem;
            border-radius: 20px;
            font-size: 0.75rem;
            font-weight: 600;
            margin-left: 1rem;
        }

        .status-paid {
            background: #dcfce7;
            color: #16a34a;
        }

        .status-pending {
            background: #fef3c7;
            color: #d97706;
        }

        .status-overdue {
            background: #fee2e2;
            color: #dc2626;
        }

        /* Responsive */
        @media (max-width: 768px) {
            .portal-container {
                padding: 1rem;
            }
            
            .page-title {
                font-size: 2rem;
            }
            
            .children-grid {
                grid-template-columns: 1fr;
            }
            
            .portal-sections {
                grid-template-columns: 1fr;
            }
            
            .quick-stats {
                grid-template-columns: 1fr;
            }
        }

        /* Animation */
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .animate-in {
            animation: fadeInUp 0.6s ease-out forwards;
        }
    </style>
</head>
<body>
    <!-- Include Navbar -->
    {% include 'html/navbar.html' %}

    <div class="portal-container">
        <!-- Page Header -->
        <div class="page-header animate-in">
            <h1 class="page-title">
                <div class="title-icon">
                    <i class="bi bi-shield-check"></i>
                </div>
                Parent Portal
            </h1>
            <p class="text-muted mt-2">Monitor your children's progress, schedule, and activities at Elite Sports Club</p>
        </div>

        <!-- Children Overview -->
        <div class="children-grid">
            <div class="child-card animate-in" style="animation-delay: 0.1s;">
                <div class="child-header">
                    <div class="child-avatar">EJ</div>
                    <div class="child-info">
                        <h3>Emma Johnson</h3>
                        <div class="child-age">12 years old</div>
                        <span class="child-sport">Tennis</span>
                    </div>
                </div>
                
                <div class="quick-stats">
                    <div class="stat-item">
                        <div class="stat-value">18</div>
                        <div class="stat-label">Sessions</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-value">92%</div>
                        <div class="stat-label">Attendance</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-value">A+</div>
                        <div class="stat-label">Progress</div>
                    </div>
                </div>
                
                <div class="child-actions">
                    <button class="btn-action btn-primary-action">View Details</button>
                    <button class="btn-action btn-secondary-action">Message Coach</button>
                </div>
            </div>

            <div class="child-card animate-in" style="animation-delay: 0.2s;">
                <div class="child-header">
                    <div class="child-avatar">LJ</div>
                    <div class="child-info">
                        <h3>Lucas Johnson</h3>
                        <div class="child-age">9 years old</div>
                        <span class="child-sport">Football</span>
                    </div>
                </div>
                
                <div class="quick-stats">
                    <div class="stat-item">
                        <div class="stat-value">15</div>
                        <div class="stat-label">Sessions</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-value">88%</div>
                        <div class="stat-label">Attendance</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-value">B+</div>
                        <div class="stat-label">Progress</div>
                    </div>
                </div>
                
                <div class="child-actions">
                    <button class="btn-action btn-primary-action">View Details</button>
                    <button class="btn-action btn-secondary-action">Message Coach</button>
                </div>
            </div>
        </div>

        <!-- Portal Sections -->
        <div class="portal-sections">
            <!-- Main Section -->
            <div class="main-section">
                <!-- Upcoming Schedule -->
                <div class="section-card animate-in" style="animation-delay: 0.3s;">
                    <h3 class="section-title">
                        <i class="bi bi-calendar-event"></i>
                        Upcoming Schedule
                    </h3>
                    
                    <div class="schedule-item">
                        <div class="schedule-time">Today<br>4:00 PM</div>
                        <div class="schedule-details">
                            <h4>Emma - Tennis Training</h4>
                            <p>Court 1 • Coach Martinez • 60 minutes</p>
                        </div>
                    </div>
                    
                    <div class="schedule-item">
                        <div class="schedule-time">Tomorrow<br>3:30 PM</div>
                        <div class="schedule-details">
                            <h4>Lucas - Football Practice</h4>
                            <p>Field A • Coach Thompson • 90 minutes</p>
                        </div>
                    </div>
                    
                    <div class="schedule-item">
                        <div class="schedule-time">Friday<br>5:00 PM</div>
                        <div class="schedule-details">
                            <h4>Emma - Match vs Sarah</h4>
                            <p>Court 2 • Competitive Match • 120 minutes</p>
                        </div>
                    </div>
                </div>

                <!-- Recent Messages -->
                <div class="section-card animate-in" style="animation-delay: 0.4s;">
                    <h3 class="section-title">
                        <i class="bi bi-chat-dots"></i>
                        Recent Messages from Coaches
                    </h3>
                    
                    <div class="message-item">
                        <div class="message-avatar">CM</div>
                        <div class="message-content">
                            <h5>Coach Martinez</h5>
                            <p>Emma showed great improvement in her backhand today. Keep up the excellent work!</p>
                            <div class="message-time">2 hours ago</div>
                        </div>
                    </div>
                    
                    <div class="message-item">
                        <div class="message-avatar">CT</div>
                        <div class="message-content">
                            <h5>Coach Thompson</h5>
                            <p>Lucas has been working hard on his passing skills. Please ensure he brings his water bottle to practice.</p>
                            <div class="message-time">1 day ago</div>
                        </div>
                    </div>
                    
                    <div class="message-item">
                        <div class="message-avatar">AD</div>
                        <div class="message-content">
                            <h5>Admin Team</h5>
                            <p>Reminder: Parent-teacher conference scheduled for next week. Please confirm your attendance.</p>
                            <div class="message-time">2 days ago</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Sidebar Section -->
            <div class="sidebar-section">
                <!-- Payment Status -->
                <div class="section-card animate-in" style="animation-delay: 0.5s;">
                    <h3 class="section-title">
                        <i class="bi bi-credit-card"></i>
                        Payment Status
                    </h3>
                    
                    <div class="payment-item">
                        <div class="payment-info">
                            <h5>June Membership</h5>
                            <p>Emma & Lucas</p>
                        </div>
                        <div>
                            <div class="payment-amount">$180</div>
                            <span class="payment-status status-paid">Paid</span>
                        </div>
                    </div>
                    
                    <div class="payment-item">
                        <div class="payment-info">
                            <h5>Equipment Rental</h5>
                            <p>Tennis Racket</p>
                        </div>
                        <div>
                            <div class="payment-amount">$25</div>
                            <span class="payment-status status-pending">Pending</span>
                        </div>
                    </div>
                    
                    <div class="payment-item">
                        <div class="payment-info">
                            <h5>Tournament Fee</h5>
                            <p>Summer Championship</p>
                        </div>
                        <div>
                            <div class="payment-amount">$50</div>
                            <span class="payment-status status-overdue">Overdue</span>
                        </div>
                    </div>
                </div>

                <!-- Quick Actions -->
                <div class="section-card animate-in" style="animation-delay: 0.6s;">
                    <h3 class="section-title">
                        <i class="bi bi-lightning"></i>
                        Quick Actions
                    </h3>
                    
                    <div class="d-grid gap-2">
                        <button class="btn btn-primary">
                            <i class="bi bi-calendar-plus"></i> Book Session
                        </button>
                        <button class="btn btn-outline-primary">
                            <i class="bi bi-chat"></i> Message Coach
                        </button>
                        <button class="btn btn-outline-primary">
                            <i class="bi bi-file-text"></i> View Reports
                        </button>
                        <button class="btn btn-outline-primary">
                            <i class="bi bi-credit-card"></i> Make Payment
                        </button>
                        <button class="btn btn-outline-primary">
                            <i class="bi bi-gear"></i> Update Profile
                        </button>
                    </div>
                </div>

                <!-- Emergency Contacts -->
                <div class="section-card animate-in" style="animation-delay: 0.7s;">
                    <h3 class="section-title">
                        <i class="bi bi-telephone"></i>
                        Emergency Contacts
                    </h3>
                    
                    <div class="contact-item mb-3">
                        <h6 class="mb-1">Club Reception</h6>
                        <p class="text-muted mb-0">
                            <i class="bi bi-telephone"></i> (*************
                        </p>
                    </div>
                    
                    <div class="contact-item mb-3">
                        <h6 class="mb-1">Coach Martinez</h6>
                        <p class="text-muted mb-0">
                            <i class="bi bi-telephone"></i> (*************
                        </p>
                    </div>
                    
                    <div class="contact-item">
                        <h6 class="mb-1">First Aid</h6>
                        <p class="text-muted mb-0">
                            <i class="bi bi-telephone"></i> (*************
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- JavaScript -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Add click handlers for child cards
            document.querySelectorAll('.child-card').forEach(card => {
                card.addEventListener('click', function(e) {
                    if (!e.target.classList.contains('btn-action')) {
                        // Navigate to child details
                        console.log('Navigate to child details');
                    }
                });
            });

            // Add click handlers for action buttons
            document.querySelectorAll('.btn-action').forEach(btn => {
                btn.addEventListener('click', function(e) {
                    e.stopPropagation();
                    const action = this.textContent.trim();
                    console.log('Action clicked:', action);
                    
                    // Add visual feedback
                    this.style.transform = 'scale(0.95)';
                    setTimeout(() => {
                        this.style.transform = '';
                    }, 150);
                });
            });

            // Add hover effects to cards
            document.querySelectorAll('.section-card').forEach(card => {
                card.addEventListener('mouseenter', function() {
                    this.style.transform = 'translateY(-2px)';
                });
                
                card.addEventListener('mouseleave', function() {
                    this.style.transform = 'translateY(0)';
                });
            });
        });
    </script>
</body>
</html>
