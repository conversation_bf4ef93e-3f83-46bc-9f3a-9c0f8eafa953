from django.db.models.signals import post_save
from django.dispatch import receiver
from django.contrib.auth import get_user_model
from .models import Coach, PlayerProfile

User = get_user_model()

@receiver(post_save, sender=User)
def create_coach_profile(sender, instance, created, **kwargs):
    """
    Automatically create a Coach profile when a User with role='coach' is created
    """
    if created and instance.role == 'coach':
        # Create coach profile with default values
        Coach.objects.create(
            user=instance,
            name=f"{instance.first_name} {instance.last_name}".strip() or instance.username,
            email=instance.email,
            price_per_hour=50.00,  # Default price
            experience=1,  # Default experience
            bio=f"Professional tennis coach with expertise in player development.",
            specialization="General Tennis Coaching",
            is_active=True
        )
        print(f"✅ Created Coach profile for user: {instance.username}")

@receiver(post_save, sender=User)
def update_coach_profile(sender, instance, created, **kwargs):
    """
    Update Coach profile when User information changes
    """
    if not created and instance.role == 'coach':
        try:
            coach = Coach.objects.get(user=instance)
            # Update coach information based on user changes
            coach.name = f"{instance.first_name} {instance.last_name}".strip() or instance.username
            coach.email = instance.email
            coach.save()
            print(f"✅ Updated Coach profile for user: {instance.username}")
        except Coach.DoesNotExist:
            # If coach profile doesn't exist, create it
            Coach.objects.create(
                user=instance,
                name=f"{instance.first_name} {instance.last_name}".strip() or instance.username,
                email=instance.email,
                price_per_hour=50.00,
                experience=1,
                bio=f"Professional tennis coach with expertise in player development.",
                specialization="General Tennis Coaching",
                is_active=True
            )
            print(f"✅ Created missing Coach profile for user: {instance.username}")

@receiver(post_save, sender=User)
def create_player_profile(sender, instance, created, **kwargs):
    """
    Automatically create a PlayerProfile when a User with role='joueur' is created
    """
    if created and instance.role == 'joueur':
        # Create player profile with default values
        PlayerProfile.objects.create(
            user=instance,
            bio=f"Welcome to Elite Sports Club! I'm excited to improve my skills and meet other players.",
            skill_level='beginner',
            preferred_sport='Tennis',
            total_matches=0,
            matches_won=0,
            total_training_hours=0
        )
        print(f"✅ Created Player profile for user: {instance.username}")

@receiver(post_save, sender=User)
def update_player_profile(sender, instance, created, **kwargs):
    """
    Update PlayerProfile when User information changes
    """
    if not created and instance.role == 'joueur':
        try:
            player_profile = PlayerProfile.objects.get(user=instance)
            # Player profile exists, no automatic updates needed for now
            print(f"✅ Player profile exists for user: {instance.username}")
        except PlayerProfile.DoesNotExist:
            # If player profile doesn't exist, create it
            PlayerProfile.objects.create(
                user=instance,
                bio=f"Welcome to Elite Sports Club! I'm excited to improve my skills and meet other players.",
                skill_level='beginner',
                preferred_sport='Tennis',
                total_matches=0,
                matches_won=0,
                total_training_hours=0
            )
            print(f"✅ Created Player profile for user: {instance.username}")
