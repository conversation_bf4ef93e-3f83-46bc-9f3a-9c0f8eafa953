#!/usr/bin/env python
"""
Test direct de l'API de création d'horaires via Django
"""
import os
import sys
import django
from datetime import datetime, timedelta
import json

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'tennis_manager.settings')
django.setup()

from django.test import Client
from django.contrib.auth import get_user_model
from reservations.models import Coach, ScheduleSlot

User = get_user_model()

def test_schedule_api_direct():
    print("🔍 TEST DIRECT API CRÉATION D'HORAIRES")
    print("="*45)
    
    # 1. Créer un admin de test
    print("\n1️⃣ Création d'un admin de test...")
    admin_user, created = User.objects.get_or_create(
        email='<EMAIL>',
        defaults={
            'username': 'api_test_admin',
            'role': 'admin',
            'is_staff': True,
            'is_superuser': True
        }
    )
    
    if created:
        admin_user.set_password('test123')
        admin_user.save()
        print(f"   ✅ Admin créé: {admin_user.email}")
    else:
        print(f"   ✅ Admin existe: {admin_user.email}")
    
    # 2. Créer un coach de test
    print("\n2️⃣ Création d'un coach de test...")
    coach, created = Coach.objects.get_or_create(
        email='<EMAIL>',
        defaults={
            'name': 'Test Coach API',
            'specialization': 'API Testing',
            'experience': 3,
            'price_per_hour': 45.00,
            'is_active': True
        }
    )
    
    if created:
        print(f"   ✅ Coach créé: {coach.name} (ID: {coach.id})")
    else:
        print(f"   ✅ Coach existe: {coach.name} (ID: {coach.id})")
    
    # 3. Test avec Django Client
    print("\n3️⃣ Test avec Django Client...")
    client = Client()
    
    # Connexion
    login_success = client.login(username='api_test_admin', password='test123')
    if not login_success:
        print("   ❌ Échec de connexion avec Django Client")
        return False
    
    print("   ✅ Connexion réussie avec Django Client")
    
    # 4. Test de création d'horaire
    print("\n4️⃣ Test de création d'horaire...")
    
    # Compter les créneaux avant
    slots_before = ScheduleSlot.objects.filter(coach=coach).count()
    print(f"   📊 Créneaux avant: {slots_before}")
    
    # Préparer les données
    tomorrow = (datetime.now() + timedelta(days=1)).strftime('%Y-%m-%d')
    schedule_data = {
        'coach_id': coach.id,
        'date': tomorrow,
        'start_time': '18:00',
        'end_time': '19:00'
    }
    
    print(f"   📋 Données: {schedule_data}")
    
    # Appel API
    response = client.post(
        '/res/api/coach-schedule/create/',
        data=json.dumps(schedule_data),
        content_type='application/json'
    )
    
    print(f"   📥 Status: {response.status_code}")
    print(f"   📥 Response: {response.content.decode()}")
    
    # Compter les créneaux après
    slots_after = ScheduleSlot.objects.filter(coach=coach).count()
    print(f"   📊 Créneaux après: {slots_after}")
    
    if response.status_code == 201:
        print("   ✅ API fonctionne - Horaire créé!")
        
        # Vérifier le créneau créé
        new_slot = ScheduleSlot.objects.filter(
            coach=coach,
            date=tomorrow,
            start_time='18:00',
            end_time='19:00'
        ).first()
        
        if new_slot:
            print(f"   ✅ Créneau trouvé en base: ID {new_slot.id}")
            print(f"   📅 {new_slot.date} {new_slot.start_time}-{new_slot.end_time}")
            print(f"   🔒 Réservé: {new_slot.is_booked}")
        else:
            print("   ❌ Créneau non trouvé en base")
        
        return True
        
    elif response.status_code == 400:
        error_data = json.loads(response.content.decode())
        print(f"   ❌ Erreur de validation: {error_data.get('error', 'Unknown')}")
        
        # Essayer avec une heure différente
        print("   🔄 Tentative avec heure différente...")
        schedule_data['start_time'] = '20:00'
        schedule_data['end_time'] = '21:00'
        
        response2 = client.post(
            '/res/api/coach-schedule/create/',
            data=json.dumps(schedule_data),
            content_type='application/json'
        )
        
        print(f"   📥 Status retry: {response2.status_code}")
        print(f"   📥 Response retry: {response2.content.decode()}")
        
        if response2.status_code == 201:
            print("   ✅ Création réussie avec heure différente!")
            return True
        
    else:
        print(f"   ❌ Erreur inattendue: {response.status_code}")
        
    return False

def test_api_view_directly():
    """Test direct de la vue API"""
    print("\n5️⃣ Test direct de la vue API...")
    
    try:
        from reservations.api_views import create_coach_schedule
        from django.http import HttpRequest
        from django.contrib.auth import get_user_model
        
        User = get_user_model()
        admin_user = User.objects.get(email='<EMAIL>')
        coach = Coach.objects.get(email='<EMAIL>')
        
        # Créer une requête simulée
        request = HttpRequest()
        request.method = 'POST'
        request.user = admin_user
        
        tomorrow = (datetime.now() + timedelta(days=1)).strftime('%Y-%m-%d')
        request_data = {
            'coach_id': coach.id,
            'date': tomorrow,
            'start_time': '22:00',
            'end_time': '23:00'
        }
        
        request._body = json.dumps(request_data).encode('utf-8')
        
        print(f"   📋 Test direct avec: {request_data}")
        
        # Appeler la vue directement
        response = create_coach_schedule(request)
        
        print(f"   📥 Status: {response.status_code}")
        print(f"   📥 Content: {response.content.decode()}")
        
        if response.status_code == 201:
            print("   ✅ Vue API fonctionne directement!")
            return True
        else:
            print("   ❌ Problème avec la vue API")
            
    except Exception as e:
        print(f"   ❌ Erreur test direct: {e}")
        import traceback
        traceback.print_exc()
    
    return False

def show_current_state():
    """Afficher l'état actuel"""
    print("\n📊 ÉTAT ACTUEL DU SYSTÈME:")
    print("="*30)
    
    coaches = Coach.objects.all()
    total_slots = ScheduleSlot.objects.all().count()
    
    print(f"👥 Total coachs: {coaches.count()}")
    print(f"⏰ Total créneaux: {total_slots}")
    
    # Afficher les derniers créneaux créés
    recent_slots = ScheduleSlot.objects.order_by('-id')[:5]
    if recent_slots:
        print(f"\n🕐 5 derniers créneaux créés:")
        for slot in recent_slots:
            print(f"   - ID {slot.id}: {slot.coach.name} - {slot.date} {slot.start_time}-{slot.end_time}")

def show_troubleshooting():
    """Guide de dépannage"""
    print("\n🔧 GUIDE DE DÉPANNAGE:")
    print("="*25)
    
    print("❌ Si l'API ne fonctionne toujours pas:")
    print("1. 🔍 Vérifiez l'URL dans le JavaScript du dashboard admin")
    print("2. 🔍 Vérifiez les headers d'authentification")
    print("3. 🔍 Vérifiez le format JSON des données")
    print("4. 🔍 Vérifiez les logs du serveur Django")
    print("5. 🔍 Vérifiez les permissions CSRF")
    
    print("\n✅ Si ce test fonctionne mais pas l'interface:")
    print("1. 🌐 Problème côté frontend (JavaScript)")
    print("2. 🌐 Problème d'authentification JWT")
    print("3. 🌐 Problème de CSRF token")
    print("4. 🌐 Problème de format de données")

if __name__ == '__main__':
    print("🎾 TENNIS MANAGEMENT SYSTEM")
    print("🔧 Test Direct API Création d'Horaires")
    print("="*55)
    
    # Afficher l'état initial
    show_current_state()
    
    # Tests
    api_success = test_schedule_api_direct()
    direct_success = test_api_view_directly()
    
    # Afficher l'état final
    show_current_state()
    
    if api_success or direct_success:
        print("\n🎉 AU MOINS UN TEST RÉUSSI!")
        print("="*30)
        print("✅ L'API de création d'horaires fonctionne")
        print("✅ Le problème est probablement côté frontend")
        
        print("\n🎯 PROCHAINES ÉTAPES:")
        print("1. Vérifiez l'interface web avec les outils de développement")
        print("2. Vérifiez l'authentification JWT")
        print("3. Vérifiez le JavaScript du dashboard admin")
        
    else:
        print("\n❌ TOUS LES TESTS ONT ÉCHOUÉ")
        print("="*30)
        print("Le problème est au niveau de l'API Django")
        
        show_troubleshooting()
        
    print("\n💡 ADMIN DE TEST CRÉÉ:")
    print("Email: <EMAIL>")
    print("Password: test123")
    print("Utilisez ces identifiants pour tester l'interface web")
