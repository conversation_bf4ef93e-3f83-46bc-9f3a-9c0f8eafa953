{% load static %}
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Interactive Calendar - Elite Sports Club</title>
    
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.0/font/bootstrap-icons.css" rel="stylesheet">
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <!-- FullCalendar CSS -->
    <link href="https://cdn.jsdelivr.net/npm/fullcalendar@6.1.8/index.global.min.css" rel="stylesheet">
    
    <style>
        :root {
            --primary-color: #6366f1;
            --secondary-color: #10b981;
            --accent-color: #f59e0b;
            --danger-color: #ef4444;
            --gray-50: #f9fafb;
            --gray-100: #f3f4f6;
            --gray-200: #e5e7eb;
            --gray-600: #4b5563;
            --gray-700: #374151;
            --gray-800: #1f2937;
        }

        body {
            font-family: 'Inter', sans-serif;
            background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
            color: var(--gray-700);
            padding-top: 80px;
        }

        .calendar-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 2rem;
        }

        .page-header {
            background: white;
            border-radius: 16px;
            padding: 2rem;
            margin-bottom: 2rem;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
            border: 1px solid var(--gray-100);
        }

        .page-title {
            font-family: 'Poppins', sans-serif;
            font-size: 2.5rem;
            font-weight: 700;
            color: var(--gray-800);
            margin: 0;
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .title-icon {
            width: 56px;
            height: 56px;
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            border-radius: 16px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.5rem;
        }

        .page-subtitle {
            color: var(--gray-600);
            font-size: 1.1rem;
            margin-top: 0.5rem;
        }

        .calendar-controls {
            background: white;
            border-radius: 16px;
            padding: 1.5rem;
            margin-bottom: 2rem;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            border: 1px solid var(--gray-100);
        }

        .filter-buttons {
            display: flex;
            gap: 0.5rem;
            flex-wrap: wrap;
        }

        .filter-btn {
            padding: 0.5rem 1rem;
            border: 2px solid var(--gray-200);
            background: white;
            border-radius: 8px;
            font-weight: 500;
            transition: all 0.2s ease;
            cursor: pointer;
            font-size: 0.875rem;
        }

        .filter-btn.active {
            background: var(--primary-color);
            border-color: var(--primary-color);
            color: white;
        }

        .filter-btn:hover {
            border-color: var(--primary-color);
            color: var(--primary-color);
        }

        .calendar-wrapper {
            background: white;
            border-radius: 16px;
            padding: 2rem;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
            border: 1px solid var(--gray-100);
        }

        /* FullCalendar Customization */
        .fc {
            font-family: 'Inter', sans-serif;
        }

        .fc-toolbar-title {
            font-family: 'Poppins', sans-serif;
            font-weight: 600;
            color: var(--gray-800);
        }

        .fc-button-primary {
            background: var(--primary-color);
            border-color: var(--primary-color);
            border-radius: 8px;
            font-weight: 500;
        }

        .fc-button-primary:hover {
            background: #5856eb;
            border-color: #5856eb;
        }

        .fc-event {
            border-radius: 6px;
            border: none;
            font-weight: 500;
            font-size: 0.875rem;
        }

        .fc-event.training {
            background: var(--secondary-color);
        }

        .fc-event.match {
            background: var(--danger-color);
        }

        .fc-event.tournament {
            background: var(--accent-color);
        }

        .fc-event.camp {
            background: var(--primary-color);
        }

        .legend {
            display: flex;
            gap: 1.5rem;
            margin-top: 1.5rem;
            flex-wrap: wrap;
        }

        .legend-item {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            font-size: 0.875rem;
            font-weight: 500;
        }

        .legend-color {
            width: 16px;
            height: 16px;
            border-radius: 4px;
        }

        .event-modal .modal-content {
            border-radius: 16px;
            border: none;
            box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
        }

        .event-modal .modal-header {
            border-bottom: 1px solid var(--gray-100);
            border-radius: 16px 16px 0 0;
        }

        .event-modal .modal-footer {
            border-top: 1px solid var(--gray-100);
            border-radius: 0 0 16px 16px;
        }

        .btn-register {
            background: var(--secondary-color);
            border-color: var(--secondary-color);
            color: white;
            border-radius: 8px;
            font-weight: 500;
            padding: 0.5rem 1.5rem;
        }

        .btn-register:hover {
            background: #059669;
            border-color: #059669;
            color: white;
        }

        /* Responsive */
        @media (max-width: 768px) {
            .calendar-container {
                padding: 1rem;
            }
            
            .page-title {
                font-size: 2rem;
            }
            
            .filter-buttons {
                justify-content: center;
            }
            
            .legend {
                justify-content: center;
            }
        }
    </style>
</head>
<body>
    <!-- Include Navbar -->
    {% include 'html/navbar.html' %}

    <div class="calendar-container">
        <!-- Page Header -->
        <div class="page-header">
            <h1 class="page-title">
                <div class="title-icon">
                    <i class="bi bi-calendar-event"></i>
                </div>
                Interactive Calendar
            </h1>
            <p class="page-subtitle">View and manage all trainings, matches, tournaments, and events</p>
        </div>

        <!-- Calendar Controls -->
        <div class="calendar-controls">
            <div class="d-flex justify-content-between align-items-center flex-wrap gap-3">
                <div class="filter-buttons">
                    <button class="filter-btn active" data-filter="all">All Events</button>
                    <button class="filter-btn" data-filter="training">Trainings</button>
                    <button class="filter-btn" data-filter="match">Matches</button>
                    <button class="filter-btn" data-filter="tournament">Tournaments</button>
                    <button class="filter-btn" data-filter="camp">Camps</button>
                </div>
                <div>
                    <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addEventModal">
                        <i class="bi bi-plus-circle"></i> Add Event
                    </button>
                </div>
            </div>
        </div>

        <!-- Calendar -->
        <div class="calendar-wrapper">
            <div id="calendar"></div>
            
            <!-- Legend -->
            <div class="legend">
                <div class="legend-item">
                    <div class="legend-color" style="background: var(--secondary-color);"></div>
                    <span>Training Sessions</span>
                </div>
                <div class="legend-item">
                    <div class="legend-color" style="background: var(--danger-color);"></div>
                    <span>Matches</span>
                </div>
                <div class="legend-item">
                    <div class="legend-color" style="background: var(--accent-color);"></div>
                    <span>Tournaments</span>
                </div>
                <div class="legend-item">
                    <div class="legend-color" style="background: var(--primary-color);"></div>
                    <span>Training Camps</span>
                </div>
            </div>
        </div>
    </div>

    <!-- Event Details Modal -->
    <div class="modal fade event-modal" id="eventModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="eventModalTitle">Event Details</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body" id="eventModalBody">
                    <!-- Event details will be populated here -->
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                    <button type="button" class="btn btn-register" id="registerBtn">Register</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Add Event Modal -->
    <div class="modal fade event-modal" id="addEventModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Add New Event</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="addEventForm">
                        <div class="mb-3">
                            <label for="eventTitle" class="form-label">Event Title</label>
                            <input type="text" class="form-control" id="eventTitle" required>
                        </div>
                        <div class="mb-3">
                            <label for="eventType" class="form-label">Event Type</label>
                            <select class="form-select" id="eventType" required>
                                <option value="">Select type</option>
                                <option value="training">Training Session</option>
                                <option value="match">Match</option>
                                <option value="tournament">Tournament</option>
                                <option value="camp">Training Camp</option>
                            </select>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <label for="eventDate" class="form-label">Date</label>
                                <input type="date" class="form-control" id="eventDate" required>
                            </div>
                            <div class="col-md-6">
                                <label for="eventTime" class="form-label">Time</label>
                                <input type="time" class="form-control" id="eventTime" required>
                            </div>
                        </div>
                        <div class="mb-3 mt-3">
                            <label for="eventDescription" class="form-label">Description</label>
                            <textarea class="form-control" id="eventDescription" rows="3"></textarea>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="button" class="btn btn-primary" id="saveEventBtn">Save Event</button>
                </div>
            </div>
        </div>
    </div>

    <!-- JavaScript -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/fullcalendar@6.1.8/index.global.min.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const calendarEl = document.getElementById('calendar');
            
            // Sample events data
            const events = [
                {
                    id: '1',
                    title: 'Tennis Training - Beginners',
                    start: '2025-06-18T09:00:00',
                    end: '2025-06-18T10:30:00',
                    className: 'training',
                    extendedProps: {
                        type: 'training',
                        coach: 'Coach Smith',
                        location: 'Court 1',
                        participants: 8,
                        maxParticipants: 12
                    }
                },
                {
                    id: '2',
                    title: 'Championship Match',
                    start: '2025-06-20T14:00:00',
                    end: '2025-06-20T16:00:00',
                    className: 'match',
                    extendedProps: {
                        type: 'match',
                        teams: 'Eagles vs Lions',
                        location: 'Main Court',
                        ticketPrice: '$15'
                    }
                },
                {
                    id: '3',
                    title: 'Summer Tournament',
                    start: '2025-06-25',
                    end: '2025-06-27',
                    className: 'tournament',
                    extendedProps: {
                        type: 'tournament',
                        registrationFee: '$50',
                        prizes: '$1000 total',
                        location: 'All Courts'
                    }
                },
                {
                    id: '4',
                    title: 'Youth Training Camp',
                    start: '2025-07-01',
                    end: '2025-07-05',
                    className: 'camp',
                    extendedProps: {
                        type: 'camp',
                        ageGroup: '12-16 years',
                        cost: '$200',
                        includes: 'Meals, Equipment, Coaching'
                    }
                }
            ];

            const calendar = new FullCalendar.Calendar(calendarEl, {
                initialView: 'dayGridMonth',
                headerToolbar: {
                    left: 'prev,next today',
                    center: 'title',
                    right: 'dayGridMonth,timeGridWeek,timeGridDay'
                },
                events: events,
                eventClick: function(info) {
                    showEventDetails(info.event);
                },
                height: 'auto',
                eventDisplay: 'block',
                dayMaxEvents: 3
            });

            calendar.render();

            // Filter functionality
            document.querySelectorAll('.filter-btn').forEach(btn => {
                btn.addEventListener('click', function() {
                    document.querySelectorAll('.filter-btn').forEach(b => b.classList.remove('active'));
                    this.classList.add('active');
                    
                    const filter = this.dataset.filter;
                    if (filter === 'all') {
                        calendar.removeAllEvents();
                        calendar.addEventSource(events);
                    } else {
                        const filteredEvents = events.filter(event => 
                            event.extendedProps.type === filter
                        );
                        calendar.removeAllEvents();
                        calendar.addEventSource(filteredEvents);
                    }
                });
            });

            // Show event details
            function showEventDetails(event) {
                const modal = new bootstrap.Modal(document.getElementById('eventModal'));
                const title = document.getElementById('eventModalTitle');
                const body = document.getElementById('eventModalBody');
                
                title.textContent = event.title;
                
                let detailsHTML = `
                    <div class="row">
                        <div class="col-md-6">
                            <h6><i class="bi bi-calendar"></i> Date & Time</h6>
                            <p>${event.start.toLocaleDateString()} at ${event.start.toLocaleTimeString()}</p>
                        </div>
                        <div class="col-md-6">
                            <h6><i class="bi bi-geo-alt"></i> Location</h6>
                            <p>${event.extendedProps.location}</p>
                        </div>
                    </div>
                `;
                
                if (event.extendedProps.coach) {
                    detailsHTML += `
                        <div class="row">
                            <div class="col-md-6">
                                <h6><i class="bi bi-person-badge"></i> Coach</h6>
                                <p>${event.extendedProps.coach}</p>
                            </div>
                            <div class="col-md-6">
                                <h6><i class="bi bi-people"></i> Participants</h6>
                                <p>${event.extendedProps.participants}/${event.extendedProps.maxParticipants}</p>
                            </div>
                        </div>
                    `;
                }
                
                body.innerHTML = detailsHTML;
                modal.show();
            }

            // Add event functionality
            document.getElementById('saveEventBtn').addEventListener('click', function() {
                const form = document.getElementById('addEventForm');
                const formData = new FormData(form);
                
                // Here you would typically send the data to your backend
                console.log('New event data:', Object.fromEntries(formData));
                
                // Close modal and show success message
                bootstrap.Modal.getInstance(document.getElementById('addEventModal')).hide();
                alert('Event added successfully!');
            });
        });
    </script>
</body>
</html>
