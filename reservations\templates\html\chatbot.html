<!-- Tennis Club Elite Chatbot Component -->
<style>
  /* Chatbot Styles */
  .chatbot-container {
    position: fixed;
    bottom: 20px;
    right: 20px;
    z-index: 1000;
  }

  .chatbot-toggle {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background: linear-gradient(45deg, #b5ff35, #85ef50);
    border: none;
    box-shadow: 0 4px 20px rgba(181, 255, 53, 0.4);
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
    color: #000;
    font-size: 1.5rem;
  }

  .chatbot-toggle:hover {
    transform: scale(1.1);
    box-shadow: 0 6px 25px rgba(181, 255, 53, 0.6);
  }

  .chatbot-window {
    position: absolute;
    bottom: 80px;
    right: 0;
    width: 350px;
    height: 500px;
    background: white;
    border-radius: 15px;
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.2);
    display: none;
    flex-direction: column;
    overflow: hidden;
    border: 2px solid #b5ff35;
  }

  .chatbot-header {
    background: linear-gradient(45deg, #b5ff35, #85ef50);
    color: #000;
    padding: 15px;
    font-weight: 700;
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .chatbot-close {
    background: none;
    border: none;
    color: #000;
    font-size: 1.2rem;
    cursor: pointer;
    padding: 0;
    width: 25px;
    height: 25px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: background 0.3s ease;
  }

  .chatbot-close:hover {
    background: rgba(0, 0, 0, 0.1);
  }

  .chatbot-messages {
    flex: 1;
    padding: 15px;
    overflow-y: auto;
    background: #f8f9fa;
  }

  .message {
    margin-bottom: 15px;
    display: flex;
    align-items: flex-start;
  }

  .message.user {
    justify-content: flex-end;
  }

  .message-content {
    max-width: 80%;
    padding: 10px 15px;
    border-radius: 15px;
    word-wrap: break-word;
  }

  .message.user .message-content {
    background: #b5ff35;
    color: #000;
    border-bottom-right-radius: 5px;
  }

  .message.assistant .message-content {
    background: white;
    color: #333;
    border: 1px solid #e9ecef;
    border-bottom-left-radius: 5px;
  }

  .message-avatar {
    width: 30px;
    height: 30px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.8rem;
    font-weight: bold;
    margin: 0 8px;
  }

  .message.user .message-avatar {
    background: #2c3e50;
    color: white;
    order: 2;
  }

  .message.assistant .message-avatar {
    background: #b5ff35;
    color: #000;
  }

  .chatbot-input {
    padding: 15px;
    border-top: 1px solid #e9ecef;
    background: white;
  }

  .input-group {
    display: flex;
    gap: 10px;
  }

  .message-input {
    flex: 1;
    border: 1px solid #e9ecef;
    border-radius: 20px;
    padding: 10px 15px;
    outline: none;
    font-size: 0.9rem;
  }

  .message-input:focus {
    border-color: #b5ff35;
    box-shadow: 0 0 0 0.2rem rgba(181, 255, 53, 0.25);
  }

  .send-button {
    background: #b5ff35;
    border: none;
    border-radius: 50%;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
    color: #000;
  }

  .send-button:hover {
    background: #85ef50;
    transform: scale(1.1);
  }

  .send-button:disabled {
    background: #ccc;
    cursor: not-allowed;
    transform: none;
  }

  .typing-indicator {
    display: none;
    padding: 10px 15px;
    background: white;
    border-radius: 15px;
    border: 1px solid #e9ecef;
    max-width: 80px;
    margin-bottom: 15px;
  }

  .typing-dots {
    display: flex;
    gap: 3px;
  }

  .typing-dot {
    width: 6px;
    height: 6px;
    border-radius: 50%;
    background: #b5ff35;
    animation: typing 1.4s infinite ease-in-out;
  }

  .typing-dot:nth-child(1) { animation-delay: -0.32s; }
  .typing-dot:nth-child(2) { animation-delay: -0.16s; }

  @keyframes typing {
    0%, 80%, 100% { transform: scale(0.8); opacity: 0.5; }
    40% { transform: scale(1); opacity: 1; }
  }

  .welcome-message {
    text-align: center;
    color: #6c757d;
    padding: 20px;
    font-style: italic;
  }

  @media (max-width: 768px) {
    .chatbot-window {
      width: 300px;
      height: 450px;
    }
    
    .chatbot-container {
      bottom: 15px;
      right: 15px;
    }
  }
</style>

<!-- Chatbot HTML -->
<div class="chatbot-container">
  <!-- Toggle Button -->
  <button class="chatbot-toggle" onclick="toggleChatbot()">
    <i class="bi bi-chat-dots"></i>
  </button>

  <!-- Chatbot Window -->
  <div class="chatbot-window" id="chatbotWindow">
    <!-- Header -->
    <div class="chatbot-header">
      <div>
        <i class="bi bi-robot"></i>
        Tennis Club Assistant
      </div>
      <button class="chatbot-close" onclick="toggleChatbot()">
        <i class="bi bi-x"></i>
      </button>
    </div>

    <!-- Messages Area -->
    <div class="chatbot-messages" id="chatbotMessages">
      <div class="welcome-message">
        👋 Hello! I'm your Tennis Club Elite assistant. I can help you with court reservations, coaching, tournaments, and general tennis questions. How can I assist you today?
      </div>
    </div>

    <!-- Typing Indicator -->
    <div class="typing-indicator" id="typingIndicator">
      <div class="typing-dots">
        <div class="typing-dot"></div>
        <div class="typing-dot"></div>
        <div class="typing-dot"></div>
      </div>
    </div>

    <!-- Input Area -->
    <div class="chatbot-input">
      <div class="input-group">
        <input 
          type="text" 
          class="message-input" 
          id="messageInput" 
          placeholder="Type your message..."
          onkeypress="handleKeyPress(event)"
        >
        <button class="send-button" id="sendButton" onclick="sendMessage()">
          <i class="bi bi-send"></i>
        </button>
      </div>
    </div>
  </div>
</div>

<script>
// Chatbot JavaScript
let currentConversationId = null;
let isTyping = false;

function toggleChatbot() {
  const window = document.getElementById('chatbotWindow');
  const isVisible = window.style.display === 'flex';
  
  if (isVisible) {
    window.style.display = 'none';
  } else {
    window.style.display = 'flex';
    document.getElementById('messageInput').focus();
  }
}

function handleKeyPress(event) {
  if (event.key === 'Enter' && !event.shiftKey) {
    event.preventDefault();
    sendMessage();
  }
}

async function sendMessage() {
  const input = document.getElementById('messageInput');
  const message = input.value.trim();
  
  if (!message || isTyping) return;
  
  // Clear input
  input.value = '';
  
  // Add user message to chat
  addMessage('user', message);
  
  // Show typing indicator
  showTyping();
  
  try {
    // Send to API
    const response = await api.post('/res/api/chat/message/', {
      message: message,
      conversation_id: currentConversationId
    });
    
    if (response.status === 200) {
      const data = response.data;
      currentConversationId = data.conversation_id;
      
      // Add assistant response
      addMessage('assistant', data.response);
    } else {
      addMessage('assistant', 'Sorry, I encountered an error. Please try again.');
    }
    
  } catch (error) {
    console.error('Chat error:', error);
    const errorMessage = error.response?.data?.response || 'Sorry, I\'m having trouble right now. Please try again later.';
    addMessage('assistant', errorMessage);
  } finally {
    hideTyping();
  }
}

function addMessage(role, content) {
  const messagesContainer = document.getElementById('chatbotMessages');
  
  const messageDiv = document.createElement('div');
  messageDiv.className = `message ${role}`;
  
  const avatar = document.createElement('div');
  avatar.className = 'message-avatar';
  avatar.textContent = role === 'user' ? 'U' : '🎾';
  
  const contentDiv = document.createElement('div');
  contentDiv.className = 'message-content';
  contentDiv.textContent = content;
  
  messageDiv.appendChild(avatar);
  messageDiv.appendChild(contentDiv);
  
  messagesContainer.appendChild(messageDiv);
  
  // Scroll to bottom
  messagesContainer.scrollTop = messagesContainer.scrollHeight;
}

function showTyping() {
  isTyping = true;
  document.getElementById('typingIndicator').style.display = 'block';
  document.getElementById('sendButton').disabled = true;
  
  // Scroll to show typing indicator
  const messagesContainer = document.getElementById('chatbotMessages');
  messagesContainer.scrollTop = messagesContainer.scrollHeight;
}

function hideTyping() {
  isTyping = false;
  document.getElementById('typingIndicator').style.display = 'none';
  document.getElementById('sendButton').disabled = false;
}

// Initialize chatbot when page loads
document.addEventListener('DOMContentLoaded', function() {
  // Add some sample quick actions (optional)
  const welcomeDiv = document.querySelector('.welcome-message');
  if (welcomeDiv) {
    welcomeDiv.innerHTML += `
      <div style="margin-top: 15px;">
        <small style="color: #b5ff35; font-weight: bold;">Quick questions:</small><br>
        <small style="cursor: pointer; color: #007bff;" onclick="quickMessage('What are your hours?')">• What are your hours?</small><br>
        <small style="cursor: pointer; color: #007bff;" onclick="quickMessage('How do I book a court?')">• How do I book a court?</small><br>
        <small style="cursor: pointer; color: #007bff;" onclick="quickMessage('Tell me about coaching')">• Tell me about coaching</small>
      </div>
    `;
  }
});

function quickMessage(message) {
  document.getElementById('messageInput').value = message;
  sendMessage();
}
</script>
