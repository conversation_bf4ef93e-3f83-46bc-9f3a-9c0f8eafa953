{% load static %}
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Services - Elite Sports Club</title>
    
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.0/font/bootstrap-icons.css" rel="stylesheet">
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Orbitron:wght@400;700;900&family=Rajdhani:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <style>
        :root {
            --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            --secondary-gradient: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            --accent-gradient: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            --success-gradient: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);
            --warning-gradient: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
            --dark-gradient: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            --sports-blue: #1e40af;
            --sports-orange: #f97316;
            --sports-green: #10b981;
            --sports-purple: #8b5cf6;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Rajdhani', sans-serif;
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
            padding-top: 80px;
        }

        /* Header Section */
        .services-header {
            background: linear-gradient(135deg, rgba(30, 64, 175, 0.9) 0%, rgba(139, 92, 246, 0.9) 50%, rgba(16, 185, 129, 0.9) 100%);
            padding: 5rem 0;
            text-align: center;
            color: white;
            position: relative;
            overflow: hidden;
        }

        .services-header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="20" cy="20" r="2" fill="rgba(255,255,255,0.1)"/><circle cx="80" cy="80" r="1.5" fill="rgba(255,255,255,0.08)"/><polygon points="50,10 55,20 45,20" fill="rgba(255,255,255,0.06)"/></svg>') repeat;
            animation: float 20s linear infinite;
        }

        @keyframes float {
            0% { transform: translateX(0) translateY(0); }
            100% { transform: translateX(-100px) translateY(-100px); }
        }

        .services-title {
            font-family: 'Orbitron', monospace;
            font-size: 3.5rem;
            font-weight: 900;
            margin-bottom: 1rem;
            text-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
            position: relative;
            z-index: 2;
        }

        .services-subtitle {
            font-size: 1.3rem;
            opacity: 0.9;
            position: relative;
            z-index: 2;
        }

        /* Services Grid */
        .services-section {
            padding: 5rem 0;
        }

        .services-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 2.5rem;
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 2rem;
        }

        .service-card {
            background: white;
            border-radius: 20px;
            padding: 2.5rem;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .service-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 5px;
            background: var(--accent-gradient);
            transform: scaleX(0);
            transition: transform 0.3s ease;
        }

        .service-card:hover::before {
            transform: scaleX(1);
        }

        .service-card:hover {
            transform: translateY(-10px);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
        }

        .service-icon {
            width: 80px;
            height: 80px;
            margin: 0 auto 1.5rem;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2rem;
            color: white;
            position: relative;
            overflow: hidden;
        }

        .service-card.tennis .service-icon {
            background: var(--success-gradient);
        }

        .service-card.football .service-icon {
            background: var(--secondary-gradient);
        }

        .service-card.basketball .service-icon {
            background: var(--sports-purple);
        }

        .service-card.swimming .service-icon {
            background: var(--accent-gradient);
        }

        .service-card.fitness .service-icon {
            background: var(--warning-gradient);
        }

        .service-card.yoga .service-icon {
            background: var(--primary-gradient);
        }

        .service-title {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 1rem;
            color: #2d3748;
            text-align: center;
        }

        .service-description {
            color: #718096;
            line-height: 1.6;
            margin-bottom: 1.5rem;
            text-align: center;
        }

        .service-features {
            list-style: none;
            margin-bottom: 2rem;
        }

        .service-features li {
            padding: 0.5rem 0;
            color: #4a5568;
            display: flex;
            align-items: center;
        }

        .service-features li::before {
            content: '✓';
            color: #10b981;
            font-weight: bold;
            margin-right: 0.5rem;
            width: 20px;
        }

        .service-price {
            font-size: 2rem;
            font-weight: 700;
            color: #2d3748;
            text-align: center;
            margin-bottom: 1rem;
        }

        .service-btn {
            width: 100%;
            background: var(--primary-gradient);
            color: white;
            border: none;
            padding: 1rem 2rem;
            border-radius: 15px;
            font-weight: 600;
            text-decoration: none;
            display: inline-block;
            text-align: center;
            transition: all 0.3s ease;
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        .service-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(102, 126, 234, 0.4);
            color: white;
        }

        /* CTA Section */
        .cta-section {
            background: var(--dark-gradient);
            padding: 4rem 0;
            text-align: center;
            color: white;
        }

        .cta-title {
            font-family: 'Orbitron', monospace;
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 1rem;
        }

        .cta-subtitle {
            font-size: 1.2rem;
            margin-bottom: 2rem;
            opacity: 0.9;
        }

        .cta-btn {
            background: var(--success-gradient);
            color: white;
            border: none;
            padding: 1rem 3rem;
            border-radius: 50px;
            font-size: 1.1rem;
            font-weight: 600;
            text-decoration: none;
            display: inline-block;
            transition: all 0.3s ease;
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        .cta-btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 10px 25px rgba(17, 153, 142, 0.4);
            color: white;
        }

        /* Responsive */
        @media (max-width: 768px) {
            .services-title {
                font-size: 2.5rem;
            }
            
            .services-grid {
                grid-template-columns: 1fr;
                padding: 0 1rem;
            }
            
            .service-card {
                padding: 2rem;
            }
        }
    </style>
</head>
<body>
    <!-- Include Navbar -->
    {% include 'html/navbar.html' %}

    <!-- Services Header -->
    <section class="services-header">
        <div class="container">
            <h1 class="services-title">OUR SERVICES</h1>
            <p class="services-subtitle">Professional sports training and facilities for champions</p>
        </div>
    </section>

    <!-- Services Section -->
    <section class="services-section">
        <div class="container">
            <div class="services-grid">
                <!-- Tennis -->
                <div class="service-card tennis">
                    <div class="service-icon">
                        <i class="bi bi-circle"></i>
                    </div>
                    <h3 class="service-title">Tennis Training</h3>
                    <p class="service-description">
                        Professional tennis coaching with state-of-the-art courts and equipment.
                    </p>
                    <ul class="service-features">
                        <li>Professional courts</li>
                        <li>Expert coaching</li>
                        <li>Equipment provided</li>
                        <li>Group & private lessons</li>
                    </ul>
                    <div class="service-price">$50/hour</div>
                    <a href="{% url 'register' %}" class="service-btn">Book Now</a>
                </div>

                <!-- Football -->
                <div class="service-card football">
                    <div class="service-icon">
                        <i class="bi bi-hexagon-fill"></i>
                    </div>
                    <h3 class="service-title">Football Training</h3>
                    <p class="service-description">
                        Comprehensive football training programs for all skill levels.
                    </p>
                    <ul class="service-features">
                        <li>Full-size pitches</li>
                        <li>Team training</li>
                        <li>Tactical coaching</li>
                        <li>Youth programs</li>
                    </ul>
                    <div class="service-price">$40/hour</div>
                    <a href="{% url 'register' %}" class="service-btn">Join Team</a>
                </div>

                <!-- Basketball -->
                <div class="service-card basketball">
                    <div class="service-icon">
                        <i class="bi bi-circle-fill"></i>
                    </div>
                    <h3 class="service-title">Basketball Training</h3>
                    <p class="service-description">
                        Indoor basketball courts with professional coaching and leagues.
                    </p>
                    <ul class="service-features">
                        <li>Indoor courts</li>
                        <li>League play</li>
                        <li>Skills training</li>
                        <li>All ages welcome</li>
                    </ul>
                    <div class="service-price">$45/hour</div>
                    <a href="{% url 'register' %}" class="service-btn">Play Now</a>
                </div>

                <!-- Swimming -->
                <div class="service-card swimming">
                    <div class="service-icon">
                        <i class="bi bi-water"></i>
                    </div>
                    <h3 class="service-title">Swimming</h3>
                    <p class="service-description">
                        Olympic-size pool with certified instructors and competitive programs.
                    </p>
                    <ul class="service-features">
                        <li>Olympic-size pool</li>
                        <li>Certified instructors</li>
                        <li>Competitive training</li>
                        <li>All skill levels</li>
                    </ul>
                    <div class="service-price">$35/hour</div>
                    <a href="{% url 'register' %}" class="service-btn">Dive In</a>
                </div>

                <!-- Fitness -->
                <div class="service-card fitness">
                    <div class="service-icon">
                        <i class="bi bi-heart-pulse-fill"></i>
                    </div>
                    <h3 class="service-title">Fitness Center</h3>
                    <p class="service-description">
                        Modern gym with latest equipment and personal training services.
                    </p>
                    <ul class="service-features">
                        <li>Modern equipment</li>
                        <li>Personal trainers</li>
                        <li>Group classes</li>
                        <li>24/7 access</li>
                    </ul>
                    <div class="service-price">$30/month</div>
                    <a href="{% url 'register' %}" class="service-btn">Get Fit</a>
                </div>

                <!-- Yoga -->
                <div class="service-card yoga">
                    <div class="service-icon">
                        <i class="bi bi-person-arms-up"></i>
                    </div>
                    <h3 class="service-title">Yoga & Wellness</h3>
                    <p class="service-description">
                        Relaxing yoga sessions and wellness programs for mind and body.
                    </p>
                    <ul class="service-features">
                        <li>Peaceful studios</li>
                        <li>Expert instructors</li>
                        <li>Various styles</li>
                        <li>Meditation classes</li>
                    </ul>
                    <div class="service-price">$25/session</div>
                    <a href="{% url 'register' %}" class="service-btn">Find Peace</a>
                </div>
            </div>
        </div>
    </section>

    <!-- CTA Section -->
    <section class="cta-section">
        <div class="container">
            <h2 class="cta-title">Ready to Start Your Journey?</h2>
            <p class="cta-subtitle">Join Elite Sports Club today and unlock your potential</p>
            <a href="{% url 'register' %}" class="cta-btn">Join Now</a>
        </div>
    </section>

    <!-- JavaScript -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Add hover effects to service cards
        const serviceCards = document.querySelectorAll('.service-card');
        serviceCards.forEach(card => {
            card.addEventListener('mouseenter', function() {
                this.style.transform = 'translateY(-15px) scale(1.02)';
            });
            
            card.addEventListener('mouseleave', function() {
                this.style.transform = 'translateY(0) scale(1)';
            });
        });

        // Animate service icons on scroll
        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    const icon = entry.target.querySelector('.service-icon');
                    if (icon) {
                        icon.style.animation = 'pulse 2s ease-in-out';
                    }
                }
            });
        });

        serviceCards.forEach(card => {
            observer.observe(card);
        });
    </script>
</body>
</html>
