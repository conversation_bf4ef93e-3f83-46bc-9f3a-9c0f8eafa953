#!/usr/bin/env python
"""
Script pour créer des tournois de test
"""
import os
import sys
import django
from datetime import datetime, timedelta

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'tennis_manager.settings')
django.setup()

from django.contrib.auth import get_user_model
from reservations.models import Tournament
from decimal import Decimal

User = get_user_model()

def create_test_tournaments():
    print("🎾 Création de tournois de test...")
    
    # Get or create an admin user to be the creator
    admin_user, created = User.objects.get_or_create(
        username='admin',
        defaults={
            'email': '<EMAIL>',
            'role': 'admin',
            'first_name': 'Admin',
            'last_name': 'User'
        }
    )
    
    if created:
        admin_user.set_password('admin123')
        admin_user.save()
        print(f"✅ Admin user created: {admin_user.username}")
    else:
        print(f"✅ Admin user exists: {admin_user.username}")
    
    # Delete existing tournaments to start fresh
    Tournament.objects.all().delete()
    print("🗑️ Existing tournaments deleted")
    
    # Create test tournaments
    tournaments_data = [
        {
            'name': 'Spring Championship 2024',
            'description': 'Annual spring tennis championship for all skill levels. Join us for an exciting tournament with great prizes!',
            'tournament_type': 'singles',
            'start_date': datetime.now() + timedelta(days=15),
            'end_date': datetime.now() + timedelta(days=17),
            'registration_deadline': datetime.now() + timedelta(days=10),
            'max_participants': 32,
            'entry_fee': Decimal('50.00'),
            'prize_money': Decimal('1000.00'),
            'status': 'upcoming'
        },
        {
            'name': 'Summer Doubles Classic',
            'description': 'Doubles tournament for teams of two. Perfect for friends and partners to compete together.',
            'tournament_type': 'doubles',
            'start_date': datetime.now() + timedelta(days=25),
            'end_date': datetime.now() + timedelta(days=27),
            'registration_deadline': datetime.now() + timedelta(days=20),
            'max_participants': 16,
            'entry_fee': Decimal('75.00'),
            'prize_money': Decimal('1500.00'),
            'status': 'upcoming'
        },
        {
            'name': 'Mixed Doubles Extravaganza',
            'description': 'Mixed doubles tournament featuring male and female partnerships. Great fun and competitive play!',
            'tournament_type': 'mixed',
            'start_date': datetime.now() + timedelta(days=35),
            'end_date': datetime.now() + timedelta(days=37),
            'registration_deadline': datetime.now() + timedelta(days=30),
            'max_participants': 20,
            'entry_fee': Decimal('60.00'),
            'prize_money': Decimal('1200.00'),
            'status': 'upcoming'
        },
        {
            'name': 'Junior Championship',
            'description': 'Special tournament for young players under 18. Develop your skills and compete with peers!',
            'tournament_type': 'singles',
            'start_date': datetime.now() + timedelta(days=45),
            'end_date': datetime.now() + timedelta(days=47),
            'registration_deadline': datetime.now() + timedelta(days=40),
            'max_participants': 24,
            'entry_fee': Decimal('25.00'),
            'prize_money': Decimal('500.00'),
            'status': 'upcoming'
        },
        {
            'name': 'Elite Masters Tournament',
            'description': 'High-level tournament for experienced players. Showcase your advanced skills and compete for the top prize!',
            'tournament_type': 'singles',
            'start_date': datetime.now() + timedelta(days=55),
            'end_date': datetime.now() + timedelta(days=57),
            'registration_deadline': datetime.now() + timedelta(days=50),
            'max_participants': 16,
            'entry_fee': Decimal('100.00'),
            'prize_money': Decimal('2500.00'),
            'status': 'upcoming'
        }
    ]
    
    created_tournaments = []
    for tournament_data in tournaments_data:
        tournament = Tournament.objects.create(
            created_by=admin_user,
            **tournament_data
        )
        created_tournaments.append(tournament)
        print(f"✅ Tournament created: {tournament.name}")
        print(f"   📅 Start: {tournament.start_date.strftime('%Y-%m-%d')}")
        print(f"   💰 Entry Fee: ${tournament.entry_fee}")
        print(f"   🏆 Prize: ${tournament.prize_money}")
        print(f"   👥 Max Participants: {tournament.max_participants}")
        print()
    
    print(f"🎉 Successfully created {len(created_tournaments)} tournaments!")
    return created_tournaments

def show_tournament_info():
    print("\n📊 TOURNAMENT SUMMARY:")
    print("="*50)
    
    tournaments = Tournament.objects.all().order_by('start_date')
    
    for i, tournament in enumerate(tournaments, 1):
        print(f"{i}. {tournament.name}")
        print(f"   Type: {tournament.get_tournament_type_display()}")
        print(f"   Start: {tournament.start_date.strftime('%Y-%m-%d %H:%M')}")
        print(f"   Registration Deadline: {tournament.registration_deadline.strftime('%Y-%m-%d %H:%M')}")
        print(f"   Entry Fee: ${tournament.entry_fee}")
        print(f"   Prize Money: ${tournament.prize_money}")
        print(f"   Max Participants: {tournament.max_participants}")
        print(f"   Status: {tournament.status}")
        print()
    
    print("🎯 NEXT STEPS:")
    print("="*20)
    print("1. 🌐 Go to player dashboard: http://127.0.0.1:8000/auth/joueur-dashboard/")
    print("2. 🔍 Click on 'Find Matches' section")
    print("3. 👀 See the available tournaments")
    print("4. 🎾 Click 'Join Tournament' to register")
    print("5. 📊 Check 'Matches Played' counter updates")

if __name__ == '__main__':
    print("🎾 TENNIS TOURNAMENT SYSTEM")
    print("🔧 Creating Test Tournaments")
    print("="*40)
    
    try:
        tournaments = create_test_tournaments()
        show_tournament_info()
        
        print("\n🚀 TOURNAMENTS READY!")
        print("="*25)
        print("✅ 5 test tournaments created")
        print("✅ Different types: Singles, Doubles, Mixed")
        print("✅ Various entry fees and prizes")
        print("✅ Realistic dates and deadlines")
        print("✅ Ready for player registration")
        
    except Exception as e:
        print(f"❌ Error creating tournaments: {e}")
        import traceback
        traceback.print_exc()
