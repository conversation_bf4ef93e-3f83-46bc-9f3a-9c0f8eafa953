{% load static %}
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Performance Dashboard - Elite Sports Club</title>
    
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.0/font/bootstrap-icons.css" rel="stylesheet">
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    
    <style>
        :root {
            --primary-color: #6366f1;
            --secondary-color: #10b981;
            --accent-color: #f59e0b;
            --danger-color: #ef4444;
            --gray-50: #f9fafb;
            --gray-100: #f3f4f6;
            --gray-200: #e5e7eb;
            --gray-600: #4b5563;
            --gray-700: #374151;
            --gray-800: #1f2937;
        }

        body {
            font-family: 'Inter', sans-serif;
            background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
            color: var(--gray-700);
            padding-top: 80px;
        }

        .dashboard-container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 2rem;
        }

        .page-header {
            background: white;
            border-radius: 16px;
            padding: 2rem;
            margin-bottom: 2rem;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
            border: 1px solid var(--gray-100);
        }

        .page-title {
            font-family: 'Poppins', sans-serif;
            font-size: 2.5rem;
            font-weight: 700;
            color: var(--gray-800);
            margin: 0;
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .title-icon {
            width: 56px;
            height: 56px;
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            border-radius: 16px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.5rem;
        }

        .stats-overview {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
        }

        .stat-card {
            background: white;
            border-radius: 16px;
            padding: 1.5rem;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
            border: 1px solid var(--gray-100);
            position: relative;
            overflow: hidden;
        }

        .stat-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: var(--card-color, var(--primary-color));
        }

        .stat-card.primary { --card-color: var(--primary-color); }
        .stat-card.secondary { --card-color: var(--secondary-color); }
        .stat-card.accent { --card-color: var(--accent-color); }
        .stat-card.danger { --card-color: var(--danger-color); }

        .stat-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1rem;
        }

        .stat-icon {
            width: 48px;
            height: 48px;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.25rem;
            color: white;
            background: var(--card-color, var(--primary-color));
        }

        .stat-value {
            font-size: 2rem;
            font-weight: 700;
            color: var(--gray-800);
            margin-bottom: 0.25rem;
        }

        .stat-label {
            color: var(--gray-600);
            font-size: 0.875rem;
            font-weight: 500;
        }

        .stat-change {
            font-size: 0.75rem;
            margin-top: 0.5rem;
            font-weight: 500;
        }

        .stat-change.positive {
            color: var(--secondary-color);
        }

        .stat-change.negative {
            color: var(--danger-color);
        }

        .charts-grid {
            display: grid;
            grid-template-columns: 2fr 1fr;
            gap: 2rem;
            margin-bottom: 2rem;
        }

        .chart-card {
            background: white;
            border-radius: 16px;
            padding: 2rem;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
            border: 1px solid var(--gray-100);
        }

        .chart-title {
            font-size: 1.25rem;
            font-weight: 600;
            color: var(--gray-800);
            margin-bottom: 1.5rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .chart-container {
            position: relative;
            height: 300px;
        }

        .achievements-section {
            background: white;
            border-radius: 16px;
            padding: 2rem;
            margin-bottom: 2rem;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
            border: 1px solid var(--gray-100);
        }

        .achievements-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 1.5rem;
        }

        .achievement-card {
            background: var(--gray-50);
            border-radius: 12px;
            padding: 1.5rem;
            border: 1px solid var(--gray-100);
            transition: all 0.3s ease;
        }

        .achievement-card:hover {
            transform: translateY(-4px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
        }

        .achievement-header {
            display: flex;
            align-items: center;
            gap: 1rem;
            margin-bottom: 1rem;
        }

        .achievement-icon {
            width: 48px;
            height: 48px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.25rem;
            color: white;
        }

        .achievement-icon.gold {
            background: linear-gradient(135deg, #fbbf24, #f59e0b);
        }

        .achievement-icon.silver {
            background: linear-gradient(135deg, #e5e7eb, #9ca3af);
        }

        .achievement-icon.bronze {
            background: linear-gradient(135deg, #d97706, #92400e);
        }

        .achievement-title {
            font-weight: 600;
            color: var(--gray-800);
            margin: 0;
        }

        .achievement-date {
            font-size: 0.875rem;
            color: var(--gray-600);
        }

        .progress-section {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 2rem;
        }

        .progress-card {
            background: white;
            border-radius: 16px;
            padding: 2rem;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
            border: 1px solid var(--gray-100);
        }

        .progress-item {
            margin-bottom: 1.5rem;
        }

        .progress-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 0.5rem;
        }

        .progress-label {
            font-weight: 500;
            color: var(--gray-700);
        }

        .progress-value {
            font-weight: 600;
            color: var(--primary-color);
        }

        .progress-bar {
            width: 100%;
            height: 8px;
            background: var(--gray-200);
            border-radius: 4px;
            overflow: hidden;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
            border-radius: 4px;
            transition: width 0.3s ease;
        }

        /* Responsive */
        @media (max-width: 768px) {
            .dashboard-container {
                padding: 1rem;
            }
            
            .page-title {
                font-size: 2rem;
            }
            
            .charts-grid {
                grid-template-columns: 1fr;
            }
            
            .stats-overview {
                grid-template-columns: 1fr;
            }
        }

        /* Animation */
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .animate-in {
            animation: fadeInUp 0.6s ease-out forwards;
        }
    </style>
</head>
<body>
    <!-- Include Navbar -->
    {% include 'html/navbar.html' %}

    <div class="dashboard-container">
        <!-- Page Header -->
        <div class="page-header animate-in">
            <h1 class="page-title">
                <div class="title-icon">
                    <i class="bi bi-speedometer2"></i>
                </div>
                Performance Dashboard
            </h1>
            <p class="text-muted mt-2">Track your progress, analyze your performance, and achieve your goals</p>
        </div>

        <!-- Stats Overview -->
        <div class="stats-overview">
            <div class="stat-card primary animate-in" style="animation-delay: 0.1s;">
                <div class="stat-header">
                    <div class="stat-icon">
                        <i class="bi bi-trophy"></i>
                    </div>
                </div>
                <div class="stat-value">94</div>
                <div class="stat-label">Performance Score</div>
                <div class="stat-change positive">
                    <i class="bi bi-arrow-up"></i> +8% this month
                </div>
            </div>

            <div class="stat-card secondary animate-in" style="animation-delay: 0.2s;">
                <div class="stat-header">
                    <div class="stat-icon">
                        <i class="bi bi-target"></i>
                    </div>
                </div>
                <div class="stat-value">87%</div>
                <div class="stat-label">Goal Achievement</div>
                <div class="stat-change positive">
                    <i class="bi bi-arrow-up"></i> +12% improvement
                </div>
            </div>

            <div class="stat-card accent animate-in" style="animation-delay: 0.3s;">
                <div class="stat-header">
                    <div class="stat-icon">
                        <i class="bi bi-clock"></i>
                    </div>
                </div>
                <div class="stat-value">156h</div>
                <div class="stat-label">Training Hours</div>
                <div class="stat-change positive">
                    <i class="bi bi-arrow-up"></i> +24h this month
                </div>
            </div>

            <div class="stat-card danger animate-in" style="animation-delay: 0.4s;">
                <div class="stat-header">
                    <div class="stat-icon">
                        <i class="bi bi-graph-up"></i>
                    </div>
                </div>
                <div class="stat-value">#12</div>
                <div class="stat-label">Current Ranking</div>
                <div class="stat-change positive">
                    <i class="bi bi-arrow-up"></i> +3 positions
                </div>
            </div>
        </div>

        <!-- Charts Section -->
        <div class="charts-grid">
            <div class="chart-card animate-in" style="animation-delay: 0.5s;">
                <h3 class="chart-title">
                    <i class="bi bi-graph-up"></i>
                    Performance Trend
                </h3>
                <div class="chart-container">
                    <canvas id="performanceChart"></canvas>
                </div>
            </div>

            <div class="chart-card animate-in" style="animation-delay: 0.6s;">
                <h3 class="chart-title">
                    <i class="bi bi-pie-chart"></i>
                    Skills Breakdown
                </h3>
                <div class="chart-container">
                    <canvas id="skillsChart"></canvas>
                </div>
            </div>
        </div>

        <!-- Achievements Section -->
        <div class="achievements-section animate-in" style="animation-delay: 0.7s;">
            <h3 class="chart-title">
                <i class="bi bi-award"></i>
                Recent Achievements
            </h3>
            <div class="achievements-grid">
                <div class="achievement-card">
                    <div class="achievement-header">
                        <div class="achievement-icon gold">
                            <i class="bi bi-trophy"></i>
                        </div>
                        <div>
                            <h4 class="achievement-title">Regional Champion</h4>
                            <div class="achievement-date">June 15, 2024</div>
                        </div>
                    </div>
                    <p class="text-muted">Won the regional tennis championship with outstanding performance</p>
                </div>

                <div class="achievement-card">
                    <div class="achievement-header">
                        <div class="achievement-icon silver">
                            <i class="bi bi-star"></i>
                        </div>
                        <div>
                            <h4 class="achievement-title">Most Improved Player</h4>
                            <div class="achievement-date">May 28, 2024</div>
                        </div>
                    </div>
                    <p class="text-muted">Recognized for exceptional improvement in technique and performance</p>
                </div>

                <div class="achievement-card">
                    <div class="achievement-header">
                        <div class="achievement-icon bronze">
                            <i class="bi bi-bullseye"></i>
                        </div>
                        <div>
                            <h4 class="achievement-title">Perfect Attendance</h4>
                            <div class="achievement-date">May 1, 2024</div>
                        </div>
                    </div>
                    <p class="text-muted">Attended all training sessions for 3 consecutive months</p>
                </div>
            </div>
        </div>

        <!-- Progress Section -->
        <div class="progress-section">
            <div class="progress-card animate-in" style="animation-delay: 0.8s;">
                <h3 class="chart-title">
                    <i class="bi bi-clipboard-check"></i>
                    Skill Progress
                </h3>
                
                <div class="progress-item">
                    <div class="progress-header">
                        <span class="progress-label">Technique</span>
                        <span class="progress-value">92%</span>
                    </div>
                    <div class="progress-bar">
                        <div class="progress-fill" style="width: 92%;"></div>
                    </div>
                </div>

                <div class="progress-item">
                    <div class="progress-header">
                        <span class="progress-label">Fitness</span>
                        <span class="progress-value">87%</span>
                    </div>
                    <div class="progress-bar">
                        <div class="progress-fill" style="width: 87%;"></div>
                    </div>
                </div>

                <div class="progress-item">
                    <div class="progress-header">
                        <span class="progress-label">Mental Game</span>
                        <span class="progress-value">78%</span>
                    </div>
                    <div class="progress-bar">
                        <div class="progress-fill" style="width: 78%;"></div>
                    </div>
                </div>

                <div class="progress-item">
                    <div class="progress-header">
                        <span class="progress-label">Strategy</span>
                        <span class="progress-value">85%</span>
                    </div>
                    <div class="progress-bar">
                        <div class="progress-fill" style="width: 85%;"></div>
                    </div>
                </div>
            </div>

            <div class="progress-card animate-in" style="animation-delay: 0.9s;">
                <h3 class="chart-title">
                    <i class="bi bi-calendar-check"></i>
                    Monthly Goals
                </h3>
                
                <div class="progress-item">
                    <div class="progress-header">
                        <span class="progress-label">Training Sessions</span>
                        <span class="progress-value">18/20</span>
                    </div>
                    <div class="progress-bar">
                        <div class="progress-fill" style="width: 90%;"></div>
                    </div>
                </div>

                <div class="progress-item">
                    <div class="progress-header">
                        <span class="progress-label">Match Wins</span>
                        <span class="progress-value">7/10</span>
                    </div>
                    <div class="progress-bar">
                        <div class="progress-fill" style="width: 70%;"></div>
                    </div>
                </div>

                <div class="progress-item">
                    <div class="progress-header">
                        <span class="progress-label">Fitness Goals</span>
                        <span class="progress-value">12/15</span>
                    </div>
                    <div class="progress-bar">
                        <div class="progress-fill" style="width: 80%;"></div>
                    </div>
                </div>

                <div class="progress-item">
                    <div class="progress-header">
                        <span class="progress-label">Skill Drills</span>
                        <span class="progress-value">25/25</span>
                    </div>
                    <div class="progress-bar">
                        <div class="progress-fill" style="width: 100%;"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- JavaScript -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Performance Trend Chart
            const performanceCtx = document.getElementById('performanceChart').getContext('2d');
            new Chart(performanceCtx, {
                type: 'line',
                data: {
                    labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun'],
                    datasets: [{
                        label: 'Performance Score',
                        data: [78, 82, 85, 88, 91, 94],
                        borderColor: '#6366f1',
                        backgroundColor: 'rgba(99, 102, 241, 0.1)',
                        tension: 0.4,
                        fill: true
                    }, {
                        label: 'Goal Target',
                        data: [80, 82, 84, 86, 88, 90],
                        borderColor: '#10b981',
                        backgroundColor: 'rgba(16, 185, 129, 0.1)',
                        tension: 0.4,
                        fill: false,
                        borderDash: [5, 5]
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            beginAtZero: true,
                            max: 100
                        }
                    },
                    plugins: {
                        legend: {
                            position: 'top'
                        }
                    }
                }
            });

            // Skills Breakdown Chart
            const skillsCtx = document.getElementById('skillsChart').getContext('2d');
            new Chart(skillsCtx, {
                type: 'doughnut',
                data: {
                    labels: ['Technique', 'Fitness', 'Mental Game', 'Strategy'],
                    datasets: [{
                        data: [92, 87, 78, 85],
                        backgroundColor: [
                            '#6366f1',
                            '#10b981',
                            '#f59e0b',
                            '#ef4444'
                        ],
                        borderWidth: 0
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'bottom'
                        }
                    }
                }
            });

            // Animate progress bars
            setTimeout(() => {
                const progressBars = document.querySelectorAll('.progress-fill');
                progressBars.forEach(bar => {
                    const width = bar.style.width;
                    bar.style.width = '0%';
                    setTimeout(() => {
                        bar.style.width = width;
                    }, 100);
                });
            }, 1000);
        });
    </script>
</body>
</html>
