{% load static %}
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Media Gallery - Elite Sports Club</title>
    
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.0/font/bootstrap-icons.css" rel="stylesheet">
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <!-- Lightbox CSS -->
    <link href="https://cdn.jsdelivr.net/npm/lightbox2@2.11.4/dist/css/lightbox.min.css" rel="stylesheet">
    
    <style>
        :root {
            --primary-color: #6366f1;
            --secondary-color: #10b981;
            --accent-color: #f59e0b;
            --danger-color: #ef4444;
            --gray-50: #f9fafb;
            --gray-100: #f3f4f6;
            --gray-200: #e5e7eb;
            --gray-600: #4b5563;
            --gray-700: #374151;
            --gray-800: #1f2937;
        }

        body {
            font-family: 'Inter', sans-serif;
            background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
            color: var(--gray-700);
            padding-top: 80px;
        }

        .gallery-container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 2rem;
        }

        .page-header {
            background: white;
            border-radius: 16px;
            padding: 2rem;
            margin-bottom: 2rem;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
            border: 1px solid var(--gray-100);
        }

        .page-title {
            font-family: 'Poppins', sans-serif;
            font-size: 2.5rem;
            font-weight: 700;
            color: var(--gray-800);
            margin: 0;
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .title-icon {
            width: 56px;
            height: 56px;
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            border-radius: 16px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.5rem;
        }

        .gallery-tabs {
            background: white;
            border-radius: 16px;
            padding: 1.5rem;
            margin-bottom: 2rem;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            border: 1px solid var(--gray-100);
        }

        .nav-tabs {
            border: none;
            gap: 0.5rem;
        }

        .nav-tabs .nav-link {
            border: 2px solid var(--gray-200);
            border-radius: 12px;
            color: var(--gray-600);
            font-weight: 500;
            padding: 0.75rem 1.5rem;
            transition: all 0.2s ease;
        }

        .nav-tabs .nav-link.active {
            background: var(--primary-color);
            border-color: var(--primary-color);
            color: white;
        }

        .nav-tabs .nav-link:hover {
            border-color: var(--primary-color);
            color: var(--primary-color);
        }

        .upload-section {
            background: linear-gradient(135deg, var(--gray-50) 0%, white 100%);
            border: 2px dashed var(--gray-300);
            border-radius: 16px;
            padding: 2rem;
            text-align: center;
            margin-bottom: 2rem;
            transition: all 0.3s ease;
        }

        .upload-section:hover {
            border-color: var(--primary-color);
            background: linear-gradient(135deg, rgba(99, 102, 241, 0.05) 0%, white 100%);
        }

        .upload-icon {
            width: 64px;
            height: 64px;
            background: var(--primary-color);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 2rem;
            margin: 0 auto 1rem;
        }

        .media-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 1.5rem;
        }

        .media-item {
            background: white;
            border-radius: 16px;
            overflow: hidden;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
            border: 1px solid var(--gray-100);
            transition: all 0.3s ease;
            position: relative;
        }

        .media-item:hover {
            transform: translateY(-8px);
            box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
        }

        .media-thumbnail {
            width: 100%;
            height: 200px;
            object-fit: cover;
            cursor: pointer;
        }

        .video-thumbnail {
            position: relative;
        }

        .video-play-btn {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 60px;
            height: 60px;
            background: rgba(0, 0, 0, 0.7);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.5rem;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .video-play-btn:hover {
            background: rgba(0, 0, 0, 0.9);
            transform: translate(-50%, -50%) scale(1.1);
        }

        .media-info {
            padding: 1.5rem;
        }

        .media-title {
            font-weight: 600;
            color: var(--gray-800);
            margin-bottom: 0.5rem;
            font-size: 1.1rem;
        }

        .media-meta {
            display: flex;
            justify-content: between;
            align-items: center;
            color: var(--gray-600);
            font-size: 0.875rem;
            margin-bottom: 1rem;
        }

        .media-date {
            display: flex;
            align-items: center;
            gap: 0.25rem;
        }

        .media-category {
            background: var(--gray-100);
            color: var(--gray-700);
            padding: 0.25rem 0.75rem;
            border-radius: 20px;
            font-size: 0.75rem;
            font-weight: 500;
        }

        .media-category.tennis {
            background: #dcfce7;
            color: #16a34a;
        }

        .media-category.football {
            background: #fef3c7;
            color: #d97706;
        }

        .media-category.basketball {
            background: #dbeafe;
            color: #1e40af;
        }

        .media-actions {
            display: flex;
            gap: 0.5rem;
        }

        .btn-action {
            flex: 1;
            padding: 0.5rem;
            border: none;
            border-radius: 8px;
            font-weight: 500;
            font-size: 0.875rem;
            transition: all 0.2s ease;
            cursor: pointer;
        }

        .btn-view {
            background: var(--primary-color);
            color: white;
        }

        .btn-view:hover {
            background: #5856eb;
        }

        .btn-share {
            background: var(--gray-100);
            color: var(--gray-700);
        }

        .btn-share:hover {
            background: var(--gray-200);
        }

        .video-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
            gap: 2rem;
        }

        .video-item {
            background: white;
            border-radius: 16px;
            overflow: hidden;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
            border: 1px solid var(--gray-100);
        }

        .video-player {
            width: 100%;
            height: 200px;
            background: #000;
        }

        .stats-bar {
            background: white;
            border-radius: 16px;
            padding: 1.5rem;
            margin-bottom: 2rem;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            border: 1px solid var(--gray-100);
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1.5rem;
        }

        .stat-item {
            text-align: center;
        }

        .stat-value {
            font-size: 2rem;
            font-weight: 700;
            color: var(--primary-color);
            margin-bottom: 0.25rem;
        }

        .stat-label {
            color: var(--gray-600);
            font-size: 0.875rem;
            font-weight: 500;
        }

        /* Modal Styles */
        .media-modal .modal-content {
            border-radius: 20px;
            border: none;
            box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
        }

        .media-modal .modal-dialog {
            max-width: 90vw;
        }

        .media-modal .modal-body {
            padding: 0;
        }

        .modal-media {
            width: 100%;
            max-height: 80vh;
            object-fit: contain;
        }

        /* Responsive */
        @media (max-width: 768px) {
            .gallery-container {
                padding: 1rem;
            }
            
            .page-title {
                font-size: 2rem;
            }
            
            .media-grid {
                grid-template-columns: 1fr;
            }
            
            .video-grid {
                grid-template-columns: 1fr;
            }
        }

        /* Animation */
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .animate-in {
            animation: fadeInUp 0.6s ease-out forwards;
        }
    </style>
</head>
<body>
    <!-- Include Navbar -->
    {% include 'html/navbar.html' %}

    <div class="gallery-container">
        <!-- Page Header -->
        <div class="page-header">
            <h1 class="page-title">
                <div class="title-icon">
                    <i class="bi bi-camera"></i>
                </div>
                Media Gallery
            </h1>
            <p class="text-muted mt-2">Explore our collection of photos and videos from matches, training sessions, and events</p>
        </div>

        <!-- Stats Bar -->
        <div class="stats-bar">
            <div class="stats-grid">
                <div class="stat-item">
                    <div class="stat-value">1,247</div>
                    <div class="stat-label">Total Photos</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value">89</div>
                    <div class="stat-label">Video Highlights</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value">156</div>
                    <div class="stat-label">Albums</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value">24</div>
                    <div class="stat-label">Live Streams</div>
                </div>
            </div>
        </div>

        <!-- Gallery Tabs -->
        <div class="gallery-tabs">
            <ul class="nav nav-tabs" id="galleryTabs" role="tablist">
                <li class="nav-item" role="presentation">
                    <button class="nav-link active" id="photos-tab" data-bs-toggle="tab" data-bs-target="#photos" type="button" role="tab">
                        <i class="bi bi-images"></i> Photos
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="videos-tab" data-bs-toggle="tab" data-bs-target="#videos" type="button" role="tab">
                        <i class="bi bi-play-circle"></i> Videos
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="upload-tab" data-bs-toggle="tab" data-bs-target="#upload" type="button" role="tab">
                        <i class="bi bi-cloud-upload"></i> Upload
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="live-tab" data-bs-toggle="tab" data-bs-target="#live" type="button" role="tab">
                        <i class="bi bi-broadcast"></i> Live Stream
                    </button>
                </li>
            </ul>
        </div>

        <!-- Tab Content -->
        <div class="tab-content" id="galleryTabContent">
            <!-- Photos Tab -->
            <div class="tab-pane fade show active" id="photos" role="tabpanel">
                <div class="media-grid" id="photoGrid">
                    <!-- Photos will be populated here -->
                </div>
            </div>

            <!-- Videos Tab -->
            <div class="tab-pane fade" id="videos" role="tabpanel">
                <div class="video-grid" id="videoGrid">
                    <!-- Videos will be populated here -->
                </div>
            </div>

            <!-- Upload Tab -->
            <div class="tab-pane fade" id="upload" role="tabpanel">
                <div class="upload-section">
                    <div class="upload-icon">
                        <i class="bi bi-cloud-upload"></i>
                    </div>
                    <h3>Upload Your Media</h3>
                    <p class="text-muted mb-4">Share your photos and videos with the community</p>
                    
                    <form id="uploadForm" enctype="multipart/form-data">
                        <div class="row justify-content-center">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <input type="file" class="form-control" id="mediaFile" accept="image/*,video/*" multiple>
                                </div>
                                <div class="mb-3">
                                    <input type="text" class="form-control" id="mediaTitle" placeholder="Title">
                                </div>
                                <div class="mb-3">
                                    <select class="form-select" id="mediaCategory">
                                        <option value="">Select Category</option>
                                        <option value="tennis">Tennis</option>
                                        <option value="football">Football</option>
                                        <option value="basketball">Basketball</option>
                                        <option value="swimming">Swimming</option>
                                        <option value="general">General</option>
                                    </select>
                                </div>
                                <div class="mb-3">
                                    <textarea class="form-control" id="mediaDescription" rows="3" placeholder="Description (optional)"></textarea>
                                </div>
                                <button type="submit" class="btn btn-primary btn-lg">
                                    <i class="bi bi-upload"></i> Upload Media
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Live Stream Tab -->
            <div class="tab-pane fade" id="live" role="tabpanel">
                <div class="text-center py-5">
                    <div class="upload-icon mx-auto mb-4">
                        <i class="bi bi-broadcast"></i>
                    </div>
                    <h3>Live Stream</h3>
                    <p class="text-muted mb-4">Watch live matches and events</p>
                    <button class="btn btn-primary btn-lg">
                        <i class="bi bi-play-circle"></i> Start Watching
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Media Modal -->
    <div class="modal fade media-modal" id="mediaModal" tabindex="-1">
        <div class="modal-dialog modal-xl">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="mediaModalTitle">Media</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div id="mediaModalContent"></div>
                </div>
            </div>
        </div>
    </div>

    <!-- JavaScript -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/lightbox2@2.11.4/dist/js/lightbox.min.js"></script>
    <script>
        // Sample media data
        const photos = [
            {
                id: 1,
                title: "Championship Final 2024",
                src: "https://images.unsplash.com/photo-1554068865-24cecd4e34b8?w=400",
                category: "tennis",
                date: "2024-06-15",
                description: "Epic final match between our top players"
            },
            {
                id: 2,
                title: "Training Session",
                src: "https://images.unsplash.com/photo-1622279457486-62dcc4a431d6?w=400",
                category: "football",
                date: "2024-06-10",
                description: "Intensive training session with Coach Martinez"
            },
            {
                id: 3,
                title: "Youth Tournament",
                src: "https://images.unsplash.com/photo-1546519638-68e109498ffc?w=400",
                category: "basketball",
                date: "2024-06-08",
                description: "Young talents showcasing their skills"
            },
            {
                id: 4,
                title: "Team Celebration",
                src: "https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?w=400",
                category: "general",
                date: "2024-06-05",
                description: "Victory celebration after winning the league"
            },
            {
                id: 5,
                title: "Swimming Competition",
                src: "https://images.unsplash.com/photo-1530549387789-4c1017266635?w=400",
                category: "swimming",
                date: "2024-06-03",
                description: "Regional swimming championship"
            },
            {
                id: 6,
                title: "Equipment Training",
                src: "https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?w=400",
                category: "tennis",
                date: "2024-06-01",
                description: "Learning proper equipment handling"
            }
        ];

        const videos = [
            {
                id: 1,
                title: "Match Highlights - Championship Final",
                thumbnail: "https://images.unsplash.com/photo-1554068865-24cecd4e34b8?w=400",
                category: "tennis",
                date: "2024-06-15",
                duration: "5:32",
                views: 1247
            },
            {
                id: 2,
                title: "Training Techniques - Advanced Footwork",
                thumbnail: "https://images.unsplash.com/photo-1622279457486-62dcc4a431d6?w=400",
                category: "football",
                date: "2024-06-12",
                duration: "8:15",
                views: 892
            },
            {
                id: 3,
                title: "Youth Development Program",
                thumbnail: "https://images.unsplash.com/photo-1546519638-68e109498ffc?w=400",
                category: "basketball",
                date: "2024-06-10",
                duration: "12:45",
                views: 654
            }
        ];

        function renderPhotos() {
            const grid = document.getElementById('photoGrid');
            grid.innerHTML = '';

            photos.forEach((photo, index) => {
                const photoElement = document.createElement('div');
                photoElement.className = 'media-item animate-in';
                photoElement.style.animationDelay = `${index * 0.1}s`;
                
                photoElement.innerHTML = `
                    <img src="${photo.src}" alt="${photo.title}" class="media-thumbnail" onclick="openPhotoModal('${photo.src}', '${photo.title}')">
                    <div class="media-info">
                        <h3 class="media-title">${photo.title}</h3>
                        <div class="media-meta">
                            <div class="media-date">
                                <i class="bi bi-calendar"></i>
                                ${new Date(photo.date).toLocaleDateString()}
                            </div>
                            <span class="media-category ${photo.category}">${photo.category}</span>
                        </div>
                        <p class="text-muted small">${photo.description}</p>
                        <div class="media-actions">
                            <button class="btn-action btn-view" onclick="openPhotoModal('${photo.src}', '${photo.title}')">
                                <i class="bi bi-eye"></i> View
                            </button>
                            <button class="btn-action btn-share">
                                <i class="bi bi-share"></i> Share
                            </button>
                        </div>
                    </div>
                `;
                
                grid.appendChild(photoElement);
            });
        }

        function renderVideos() {
            const grid = document.getElementById('videoGrid');
            grid.innerHTML = '';

            videos.forEach((video, index) => {
                const videoElement = document.createElement('div');
                videoElement.className = 'video-item animate-in';
                videoElement.style.animationDelay = `${index * 0.1}s`;
                
                videoElement.innerHTML = `
                    <div class="video-thumbnail">
                        <img src="${video.thumbnail}" alt="${video.title}" class="media-thumbnail">
                        <div class="video-play-btn" onclick="playVideo(${video.id})">
                            <i class="bi bi-play-fill"></i>
                        </div>
                    </div>
                    <div class="media-info">
                        <h3 class="media-title">${video.title}</h3>
                        <div class="media-meta">
                            <div class="media-date">
                                <i class="bi bi-calendar"></i>
                                ${new Date(video.date).toLocaleDateString()}
                            </div>
                            <span class="media-category ${video.category}">${video.category}</span>
                        </div>
                        <div class="d-flex justify-content-between text-muted small mb-3">
                            <span><i class="bi bi-clock"></i> ${video.duration}</span>
                            <span><i class="bi bi-eye"></i> ${video.views} views</span>
                        </div>
                        <div class="media-actions">
                            <button class="btn-action btn-view" onclick="playVideo(${video.id})">
                                <i class="bi bi-play"></i> Play
                            </button>
                            <button class="btn-action btn-share">
                                <i class="bi bi-share"></i> Share
                            </button>
                        </div>
                    </div>
                `;
                
                grid.appendChild(videoElement);
            });
        }

        function openPhotoModal(src, title) {
            const modal = new bootstrap.Modal(document.getElementById('mediaModal'));
            const modalTitle = document.getElementById('mediaModalTitle');
            const modalContent = document.getElementById('mediaModalContent');
            
            modalTitle.textContent = title;
            modalContent.innerHTML = `<img src="${src}" alt="${title}" class="modal-media">`;
            
            modal.show();
        }

        function playVideo(videoId) {
            const video = videos.find(v => v.id === videoId);
            if (!video) return;

            const modal = new bootstrap.Modal(document.getElementById('mediaModal'));
            const modalTitle = document.getElementById('mediaModalTitle');
            const modalContent = document.getElementById('mediaModalContent');
            
            modalTitle.textContent = video.title;
            modalContent.innerHTML = `
                <video controls class="modal-media">
                    <source src="sample-video.mp4" type="video/mp4">
                    Your browser does not support the video tag.
                </video>
            `;
            
            modal.show();
        }

        // Upload form handling
        document.getElementById('uploadForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const formData = new FormData();
            const fileInput = document.getElementById('mediaFile');
            const title = document.getElementById('mediaTitle').value;
            const category = document.getElementById('mediaCategory').value;
            const description = document.getElementById('mediaDescription').value;
            
            if (fileInput.files.length === 0) {
                alert('Please select at least one file to upload.');
                return;
            }
            
            if (!title || !category) {
                alert('Please fill in all required fields.');
                return;
            }
            
            // Here you would typically send the data to your backend
            console.log('Upload data:', {
                files: fileInput.files,
                title: title,
                category: category,
                description: description
            });
            
            alert('Media uploaded successfully!');
            this.reset();
        });

        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            renderPhotos();
            renderVideos();
        });

        // Tab change handler
        document.querySelectorAll('[data-bs-toggle="tab"]').forEach(tab => {
            tab.addEventListener('shown.bs.tab', function(e) {
                const target = e.target.getAttribute('data-bs-target');
                if (target === '#photos') {
                    renderPhotos();
                } else if (target === '#videos') {
                    renderVideos();
                }
            });
        });
    </script>
</body>
</html>
