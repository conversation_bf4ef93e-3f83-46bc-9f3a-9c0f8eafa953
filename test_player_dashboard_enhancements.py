#!/usr/bin/env python
"""
Test des améliorations du dashboard joueur:
- Today's Sessions
- This Week Total Earnings  
- Today's Coach Schedules
"""
import requests
import json

def test_player_dashboard_enhancements():
    print("🎾 Test Dashboard Joueur - Nouvelles Fonctionnalités")
    print("="*50)
    
    # 1. Test login player
    print("\n1️⃣ Test Login Player:")
    try:
        login_response = requests.post('http://127.0.0.1:8000/auth/api/login/', json={
            'email': '<EMAIL>',
            'password': 'player123'
        })
        
        if login_response.status_code == 200:
            print("✅ Login player réussi")
            access_token = login_response.json().get('access')
            headers = {'Authorization': f'Bearer {access_token}'}
        else:
            print(f"❌ Login player échoué: {login_response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Erreur login player: {e}")
        return False
    
    # 2. Test API Today's Coach Schedules
    print("\n2️⃣ Test API Today's Coach Schedules:")
    try:
        schedules_response = requests.get('http://127.0.0.1:8000/res/api/player/todays-coach-schedules/', headers=headers)
        
        if schedules_response.status_code == 200:
            schedules_data = schedules_response.json()
            summary = schedules_data.get('summary', {})
            coaches_schedules = schedules_data.get('coaches_schedules', [])
            
            print(f"✅ API Today's Coach Schedules fonctionnelle")
            print(f"   📅 Date: {schedules_data.get('date')}")
            print(f"   👨‍🏫 {summary.get('total_coaches', 0)} coachs actifs")
            print(f"   📊 {summary.get('total_slots', 0)} créneaux au total")
            print(f"   ✅ {summary.get('total_available', 0)} créneaux disponibles")
            print(f"   ⚠️  {summary.get('total_booked', 0)} créneaux réservés")
            print(f"   💰 ${summary.get('total_earnings', 0):.2f} gains aujourd'hui")
            
            # Afficher quelques exemples de coachs
            print(f"   🔍 Exemples de coachs:")
            for i, coach_schedule in enumerate(coaches_schedules[:3]):
                coach = coach_schedule['coach']
                schedule = coach_schedule['schedule']
                print(f"      {i+1}. {coach['name']} - ${coach['price_per_hour']}/hr")
                print(f"         📈 {schedule['available_slots']} disponibles, {schedule['booked_slots']} réservés")
                print(f"         💵 ${schedule['todays_earnings']:.2f} gains aujourd'hui")
                
        else:
            print(f"❌ Erreur API Today's Coach Schedules: {schedules_response.status_code}")
            print(f"   Message: {schedules_response.text}")
            return False
    except Exception as e:
        print(f"❌ Erreur test API Today's Coach Schedules: {e}")
        return False
    
    # 3. Test API Weekly Spending
    print("\n3️⃣ Test API Weekly Spending:")
    try:
        spending_response = requests.get('http://127.0.0.1:8000/res/api/player/weekly-spending/', headers=headers)
        
        if spending_response.status_code == 200:
            spending_data = spending_response.json()
            week_period = spending_data.get('week_period', {})
            spending = spending_data.get('spending', {})
            sessions_count = spending_data.get('sessions_count', {})
            todays_sessions = spending_data.get('todays_sessions', [])
            
            print(f"✅ API Weekly Spending fonctionnelle")
            print(f"   📅 Période: {week_period.get('start_date')} à {week_period.get('end_date')}")
            print(f"   💰 Dépenses totales: ${spending.get('total_spending', 0):.2f}")
            print(f"   🏟️ Dépenses courts: ${spending.get('court_spending', 0):.2f}")
            print(f"   👨‍🏫 Dépenses coachs: ${spending.get('coach_spending', 0):.2f}")
            print(f"   📊 Sessions aujourd'hui: {sessions_count.get('total_sessions', 0)}")
            print(f"      - Courts: {sessions_count.get('court_sessions', 0)}")
            print(f"      - Coachs: {sessions_count.get('coach_sessions', 0)}")
            
            # Afficher les sessions d'aujourd'hui
            if len(todays_sessions) > 0:
                print(f"   🔍 Sessions d'aujourd'hui:")
                for i, session in enumerate(todays_sessions):
                    session_type = "🏟️" if session['type'] == 'court' else "👨‍🏫"
                    print(f"      {i+1}. {session_type} {session['title']} - {session['time']} (${session['price']:.2f})")
            else:
                print(f"   ℹ️  Aucune session aujourd'hui")
                
        else:
            print(f"❌ Erreur API Weekly Spending: {spending_response.status_code}")
            print(f"   Message: {spending_response.text}")
            return False
    except Exception as e:
        print(f"❌ Erreur test API Weekly Spending: {e}")
        return False
    
    # 4. Test Interface Player Dashboard
    print("\n4️⃣ Test Interface Player Dashboard:")
    try:
        dashboard_response = requests.get('http://127.0.0.1:8000/auth/joueur-dashboard/')
        
        if dashboard_response.status_code == 200:
            html_content = dashboard_response.text
            
            # Vérifier les nouveaux éléments de l'interface
            checks = [
                ("Today's Sessions Section", "Today's Sessions" in html_content),
                ("This Week Total Earnings Section", "This Week Total Earnings" in html_content),
                ("Today's Coach Schedules Section", "Today's Coach Schedules" in html_content),
                ("Today's Sessions Container", "todaysSessions" in html_content),
                ("Weekly Earnings Display", "weeklyEarnings" in html_content),
                ("Weekly Progress Bar", "weeklyProgress" in html_content),
                ("Coach Schedules Container", "todaysCoachSchedules" in html_content),
                ("Load Today's Sessions Function", "loadTodaysSessions" in html_content),
                ("Load Weekly Spending Function", "loadWeeklySpending" in html_content),
                ("Load Today's Coach Schedules Function", "loadTodaysCoachSchedules" in html_content),
                ("Select Coach for Booking Function", "selectCoachForBooking" in html_content),
                ("API Calls", "/res/api/player/todays-coach-schedules/" in html_content),
                ("Weekly Spending API", "/res/api/player/weekly-spending/" in html_content),
                ("Bootstrap Icons", "bi-calendar-day" in html_content),
                ("Progress Bar Styling", "progress-bar bg-warning" in html_content)
            ]
            
            all_passed = True
            for check_name, check_result in checks:
                if check_result:
                    print(f"   ✅ {check_name}")
                else:
                    print(f"   ❌ {check_name} manquant")
                    all_passed = False
            
            if all_passed:
                print("✅ Interface Player Dashboard complète!")
            else:
                print("⚠️  Certains éléments manquent dans l'interface")
        else:
            print(f"❌ Dashboard player inaccessible: {dashboard_response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Erreur test interface player: {e}")
        return False
    
    return True

def show_manual_test_instructions():
    print("\n🎯 INSTRUCTIONS POUR TEST MANUEL:")
    print("="*40)
    print("1. 🌐 Aller sur: http://127.0.0.1:8000/auth/login/")
    print("2. 🔑 Se connecter: <EMAIL> / player123")
    print("3. 📊 Observer le Dashboard Player")
    print("4. 🔍 Vérifier les nouvelles sections:")
    print("   📅 Today's Sessions:")
    print("      - Liste des sessions d'aujourd'hui")
    print("      - Badges par type (court/coach)")
    print("      - Prix et statut de chaque session")
    print("   💰 This Week Total Earnings:")
    print("      - Montant total dépensé cette semaine")
    print("      - Barre de progression colorée")
    print("      - Indicateur de budget hebdomadaire")
    print("   👨‍🏫 Today's Coach Schedules:")
    print("      - Statistiques globales (coachs, créneaux, gains)")
    print("      - Cartes par coach avec leurs horaires")
    print("      - Créneaux disponibles/réservés")
    print("      - Boutons 'Book Session' fonctionnels")
    
    print("\n🎨 FONCTIONNALITÉS À TESTER:")
    print("="*30)
    print("✓ Today's Sessions:")
    print("  - Affichage des sessions du jour")
    print("  - Icônes différenciées (🏟️ court, 👨‍🏫 coach)")
    print("  - Prix et horaires affichés")
    print("  - Message si aucune session")
    print("✓ Weekly Earnings:")
    print("  - Montant total mis à jour")
    print("  - Barre de progression dynamique")
    print("  - Couleurs selon le niveau de dépense")
    print("✓ Coach Schedules:")
    print("  - Statistiques en temps réel")
    print("  - Cartes par coach avec détails")
    print("  - Créneaux colorés selon disponibilité")
    print("  - Navigation vers réservation coach")

if __name__ == '__main__':
    print("🎾 TENNIS MANAGEMENT SYSTEM")
    print("🔧 Test Dashboard Player - Nouvelles Fonctionnalités")
    print("="*55)
    
    success = test_player_dashboard_enhancements()
    
    if success:
        print("\n🚀 DASHBOARD PLAYER AMÉLIORÉ FONCTIONNEL!")
        print("="*45)
        print("✅ API Today's Coach Schedules complète")
        print("✅ API Weekly Spending complète")
        print("✅ Interface Today's Sessions")
        print("✅ Interface This Week Total Earnings")
        print("✅ Interface Today's Coach Schedules")
        print("✅ Fonctions JavaScript intégrées")
        print("✅ Design responsive et moderne")
        
        show_manual_test_instructions()
        
        print("\n🎉 FÉLICITATIONS!")
        print("="*20)
        print("Le dashboard player dispose maintenant de:")
        print("- Today's Sessions avec détails complets")
        print("- This Week Total Earnings avec progression")
        print("- Today's Coach Schedules avec tous les coachs")
        print("- APIs backend robustes")
        print("- Interface moderne et intuitive")
        
    else:
        print("\n⚠️  Des problèmes ont été détectés.")
        print("Vérifiez les erreurs ci-dessus et corrigez-les.")
